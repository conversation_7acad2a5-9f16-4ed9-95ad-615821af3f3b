.form_editor_fields_container{background-color:transparent;display:flex;flex-direction:column;flex-grow:1;max-height:calc(100% - 144px);max-width:calc(100% - 594px);min-height:calc(100% - 144px);overflow-x:hidden;overflow-y:auto;padding:1rem 2rem 2rem;position:relative;right:0;top:4rem;z-index:1}.gf_browser_safari .form_editor_fields_container>.simplebar-wrapper>.simplebar-mask>.simplebar-offset>.simplebar-content-wrapper>.simplebar-content{min-height:calc(100vh - 9rem)}.form_editor_fields_container.form_editor_fields_no_fields{overflow-y:hidden}.form_editor_fields_container.droppable{max-width:100%;padding:1rem 562px 2rem 2rem;z-index:2}.gform-jetpack-admin-menu .form_editor_fields_container{max-width:calc(100% - 706px)}.gform-jetpack-admin-menu .form_editor_fields_container.droppable{padding-right:674px}.folded .form_editor_fields_container{max-width:calc(100% - 470px)}.folded .form_editor_fields_container.droppable{max-width:100%;padding:2rem 438px 2rem 2rem;z-index:2}.dropzone__placeholder{background:#fff;box-sizing:border-box;height:calc(100vh - 186px);left:50%;margin:0;position:absolute;text-align:center;top:6rem;transform:translateX(-50%);transition:opacity .19s ease-out;width:100%;z-index:2}.dropzone-loader-visible .dropzone__placeholder{opacity:0}.droppable .dropzone__placeholder{height:calc(100vh - 130px)}.dropzone__placeholder p{color:#242748;display:block;font-size:1.0625rem;font-style:normal;font-weight:400;line-height:1.75rem;padding:0 3.125rem}.dropzone__placeholder .gform-editor__no-fields-graphic{height:auto;max-width:100%}.dropzone__placeholder.hovering{opacity:0}.dropzone__placeholder.hovering::after{opacity:.25}.dropzone__loader{padding:1rem}.dropzone__loader-item{background:#ecedf8;border-radius:3px}.dropzone__loader-label{height:1.438rem;width:8.125rem}.dropzone__loader-content{height:3.125rem;margin-top:.25rem}.dropzone__target{background:#f6f9fc;border:1px dashed #175cff;border-radius:3px;height:225px;opacity:0;position:relative;transition:opacity .19s ease-out;z-index:1}.dropzone__target.hovering{opacity:1}html[dir=rtl] .form_editor_fields_container.droppable{max-width:100%;padding:2rem 2rem 2rem 562px}html[dir=rtl] .folded .form_editor_fields_container.droppable{max-width:100%;padding:2rem 2rem 2rem 438px}@media only screen and (max-width:960px){.form_editor_fields_container{max-width:calc(100% - 494px);width:100%}.form_editor_fields_container.droppable{max-width:100%;padding:2rem 462px 2rem 2rem}.gform-jetpack-admin-menu .form_editor_fields_container{max-width:calc(100% - 605px)}.gform-jetpack-admin-menu .form_editor_fields_container.droppable{padding-right:573px}.auto-fold .form_editor_fields_container,.folded .form_editor_fields_container{max-width:calc(100% - 370px);width:100%}.auto-fold .form_editor_fields_container.droppable,.folded .form_editor_fields_container.droppable{max-width:100%;padding:2rem 338px 2rem 2rem}html[dir=rtl] .form_editor_fields_container{max-width:calc(100% - 494px);padding-left:2rem}html[dir=rtl] .form_editor_fields_container.droppable{max-width:calc(100% - 494px);padding:2rem 2rem 2rem 338px}html[dir=rtl] .auto-fold .form_editor_fields_container,html[dir=rtl] .folded .form_editor_fields_container{max-width:calc(100% - 370px)}html[dir=rtl] .auto-fold .form_editor_fields_container.droppable,html[dir=rtl] .folded .form_editor_fields_container.droppable{max-width:100%;padding:2rem 2rem 2rem 338px}}@media only screen and (max-width:782px){.form_editor_fields_container{max-height:calc(100% - 158px);max-width:calc(100% - 334px);min-height:calc(100% - 158px)}.form_editor_fields_container.droppable{max-width:100%;padding:2rem 302px 2rem 2rem}.auto-fold .form_editor_fields_container,.folded .form_editor_fields_container{max-width:calc(100% - 334px);width:100%}.auto-fold .form_editor_fields_container.droppable,.folded .form_editor_fields_container.droppable{max-width:100%;padding:2rem 302px 2rem 2rem}html[dir=rtl] .auto-fold .form_editor_fields_container,html[dir=rtl] .folded .form_editor_fields_container,html[dir=rtl] .form_editor_fields_container{max-width:calc(100% - 334px)}html[dir=rtl] .auto-fold .form_editor_fields_container.droppable,html[dir=rtl] .folded .form_editor_fields_container.droppable,html[dir=rtl] .form_editor_fields_container.droppable{max-width:100%;padding-left:302px}}.add-buttons,.panel-block-tabs__body{border:none;border-left:none;padding:.875rem;width:auto}html[dir=rtl] .add-buttons{justify-content:right}.add-buttons{align-content:stretch;display:flex;flex-wrap:wrap;justify-content:left;margin:0;overflow:visible}.add-buttons li{margin-bottom:.8125rem;width:33.33%}.add-buttons li button,.add-buttons li button.button{align-items:stretch;background:0 0;border:1px solid #d5d7e9;border-radius:3px;color:#242748;cursor:grab;display:flex;flex-direction:column;font-size:.8125rem;height:5.8125rem;justify-content:space-around;margin:0 auto;word-wrap:anywhere;padding:.5rem 0 0 0;transition:.1s all ease-in-out;width:6.1875rem}.add-buttons li button.button:hover,.add-buttons li button:hover{border-color:#3e7da6;color:#3e7da6}.add-buttons li button.button:focus,.add-buttons li button:focus{border:1px solid #3985b7;border-radius:3px;box-shadow:0 0 0 2px #bed8ed;color:#242748;outline:0;transition:box-shadow .15s ease}.add-buttons li button.fieldPlaceholder{border:1px dashed #d5d7e9}.add-buttons li button.fieldPlaceholder div{opacity:0}button.ui-draggable-dragging{align-items:stretch;background:#fff;border:1px solid #d5d7e9;border-radius:3px;box-shadow:0 0 1px rgba(18,25,97,.24),0 24px 24px rgba(18,25,97,.03),0 2px 2px rgba(18,25,97,.03),0 4px 4px rgba(18,25,97,.03),0 8px 8px rgba(18,25,97,.03),0 16px 16px rgba(18,25,97,.03);color:#242748;cursor:grabbing;display:flex;flex-direction:column;font-size:.8125rem;justify-content:space-around;padding:.5rem 0 0 0;text-align:center;white-space:normal;z-index:999999999}html[dir=rtl] div.gforms_edit_form button.ui-draggable-dragging,html[dir=rtl] div.gforms_edit_form button.ui-draggable-dragging div{text-align:center!important}button .button-text{font-size:.75rem;font-weight:400;line-height:1rem;padding:0 0 .5rem;text-align:center}.button-icon{border-radius:3px;font-family:dashicons;font-size:1.3125rem;height:1.5rem;text-align:center;width:100%}.button-icon img{color:#242748;fill:#242748;width:1.3125rem}.button-icon::before{line-height:1.5rem;transition:all .1s ease-in-out}.button-icon.icon-default::before{content:"\f111"}.gform_debug .hovering,.gform_debug .spacer{border-color:red!important;border-style:dashed}.gform_debug .gfield::after,.gform_debug button::after{content:attr(data-groupId);display:block;font-family:monospace;font-size:.875rem;text-align:center;width:100%}#indicator{background-color:#0f3d6c;border-radius:3px;height:.25rem;position:absolute;width:100%}.dropzone-loader-visible #indicator{opacity:0}.gform_editor{-webkit-font-smoothing:antialiased;line-height:1.5;margin-inline:auto;max-width:1000px;padding-block:55px 7px;padding-inline:0;position:relative;width:100%}.gform_editor .gfield_description:empty{display:none}.gform_editor .gform_hidden,.gform_editor .gform_hidden label{display:block}.gform_editor .gform_hidden input,.gform_editor .gform_hidden label{opacity:.6}.gform_editor .gform_hidden .ui-resizable-handle{display:none!important}.gform_editor .gfield_radio li{list-style-type:none;margin:0}.gform_editor .gfield_radio li input{height:1rem;margin:.1875rem .25rem 0;width:1rem}div.wrap.gf_browser_chrome .gform_editor .gfield_radio li input{margin-left:0!important}.gform_editor .gfield_radio li label{display:inline-block;font-size:.9375rem;margin:0}.gform_editor .gform_fields>li{list-style-type:none}.gform_editor .gfield_checkbox li label{margin:-.5625rem 0 0}#gform_fields{position:relative}.gform_fields .gfield{margin:0}.gform_editor .gpage::before{top:3rem}.gform_editor h2.gsection_title,.gform_editor h3.gsection_title{margin-top:0}html[dir=rtl] #no-fields{text-align:center!important}html[dir=rtl] #no-fields p{text-align:center!important}.gfield{border:1px solid transparent;border-radius:1px;box-sizing:content-box!important;font-size:1rem;grid-column:1/-1;margin:0;padding:16px;position:relative}.gfield.field_selected,.gfield:focus-within{border-color:transparent}.gfield::before{background-color:#d5d7e9;border-radius:5px;bottom:1.063rem;content:"";left:-5px;opacity:0;position:absolute;top:1.1rem;transition:opacity ease-in-out .2s;width:.25rem;z-index:1}html[dir=rtl] .gfield::before{left:auto;right:-.3125rem}#field_submit::before,.gfield.spacer::before{display:none}.gfield.field_selected:not(.placeholder):not(.ui-draggable-dragging)::before,.gfield:focus-within::before,.gfield:hover::before{opacity:1}.gfield.gfield_html.field_selected:not(.placeholder):not(.ui-draggable-dragging)::before,.gfield.gfield_html:hover::before,.gfield.gform_hidden.field_selected:not(.placeholder):not(.ui-draggable-dragging)::before,.gfield.gform_hidden:hover::before{bottom:1rem}.gfield textarea{display:block}.gfield:focus{outline:0}.dragging .gfield:not(.clone):not(.placeholder):not(.spacer){border-radius:5px}.gfield.ui-draggable-dragging{background:0 0;border:0}.gfield.ui-draggable-dragging .gf-html-container,.gfield.ui-draggable-dragging .gfield_description,.gfield.ui-draggable-dragging .ginput_container,.gfield.ui-draggable-dragging .gsection_description,.gfield.ui-draggable-dragging .gsection_title,.gfield.ui-draggable-dragging .hour_minute_colon,.gfield.ui-draggable-dragging .ui-resizable-handle.ui-resizable-e,.gfield.ui-draggable-dragging input,.gfield.ui-draggable-dragging label,.gfield.ui-draggable-dragging legend,.gfield.ui-draggable-dragging::before{opacity:0!important}.spacer{padding:1rem 0}.spacer::after{content:""}.gfield.custom{grid-column:span 1}.ui-sortable-helper{opacity:.5}.gfield .ui-resizable-e,.gfield .ui-resizable-w{background-color:#0f3d6c;border-radius:5px;bottom:1.063rem;cursor:ew-resize;opacity:0;pointer-events:none;right:-.25rem;top:1.1rem;transition:all ease-in-out .2s;width:.25rem}.gpage .ui-resizable-handle,.gsection .ui-resizable-handle{display:none!important}.gfield.field_selected:not(.ui-draggable-dragging):not(.placeholder) .ui-resizable-e,.gfield.field_selected:not(.ui-draggable-dragging):not(.placeholder) .ui-resizable-w,.gfield:focus-within .ui-resizable-e,.gfield:focus-within .ui-resizable-w,.gfield:hover .ui-resizable-e,.gfield:hover .ui-resizable-w,.ui-resizable-resizing .ui-resizable-e,.ui-resizable-resizing .ui-resizable-w{opacity:1;pointer-events:initial}.gfield .ui-resizable-w{display:none!important}html[dir=rtl] .gfield:not(.ui-draggable-dragging) .ui-resizable-w{display:block!important;left:-.25rem;right:auto}html[dir=rtl] .gfield .ui-resizable-e{display:none!important}.gf-drag-handle{background-color:#000;height:1rem;left:-1rem;position:absolute;top:-1rem;width:1rem}.gfield-admin-wrapper{width:100%}.gform-compact-view .gfield-admin-wrapper,.left_label .gfield-admin-wrapper,.right_label .gfield-admin-wrapper{display:flex;inline-size:100%}.form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield-admin-wrapper,.form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield-admin-wrapper{flex-wrap:wrap;justify-content:flex-end}.gfield-admin-icons{background:#fff;border:1px solid #d5d7e9;border-radius:3px;box-shadow:0 16px 16px 0 rgba(18,25,97,.03),0 8px 8px 0 rgba(18,25,97,.03),0 4px 4px 0 rgba(18,25,97,.03),0 2px 2px 0 rgba(18,25,97,.03),0 24px 24px 0 rgba(18,25,97,.03),0 0 1px 0 rgba(18,25,97,.24);display:flex;flex-direction:row;inset-inline-start:16px;opacity:0;pointer-events:none;position:absolute;top:calc((3rem + 2px) * -1);transition:all ease-in-out .2s;z-index:2}.gfield-admin-icons .gfield-field-action{align-items:center;background-color:#fff;border:0;border-left:1px solid #d5d7e9;color:#242748;cursor:pointer;display:flex;font-size:0;height:3rem;justify-content:center;outline:0;position:relative;width:3rem}.gfield-admin-icons .gfield-field-action .gform-icon--drag-indicator,.gfield-admin-icons .gfield-field-action .gform-icon--duplicate,.gfield-admin-icons .gfield-field-action .gform-icon--settings,.gfield-admin-icons .gfield-field-action .gform-icon--trash{font-size:1.625rem}.gfield-admin-icons .gfield-field-action .dashicons{line-height:3rem}.gfield-admin-icons .gfield-field-action:first-child{border-left:0;border-radius:3px 0 0 3px}.gfield-admin-icons .gfield-field-action svg{outline:0;vertical-align:middle}.gfield-admin-icons .gfield-field-action .gfield-field-action__description{background:#242748;border-radius:2px;color:#fff;font-size:.625rem;font-weight:500;left:50%;line-height:1.5;opacity:0;padding:.35rem .75rem;position:absolute;text-align:center;top:-1.25rem;transform:translateX(-50%);transition:all ease-in-out .2s;z-index:100}.gfield-admin-icons .gfield-field-action:focus .gfield-field-action__description,.gfield-admin-icons .gfield-field-action:hover .gfield-field-action__description{opacity:1;transition-delay:0.3s}.gfield-admin-icons .gfield-delete:focus,.gfield-admin-icons .gfield-delete:hover,.gfield-admin-icons .gfield-edit:focus,.gfield-admin-icons .gfield-edit:hover{color:#3e7da6}.gfield-admin-icons .gfield-delete:focus path,.gfield-admin-icons .gfield-delete:hover path,.gfield-admin-icons .gfield-edit:hover path{fill:#3e7da6}.gfield-admin-icons .gfield-duplicate{border-radius:0}.gfield-admin-icons .gfield-duplicate:focus .stroke,.gfield-admin-icons .gfield-duplicate:hover .stroke{stroke:#3e7da6}.gfield-admin-icons .gfield-duplicate:focus .fill,.gfield-admin-icons .gfield-duplicate:hover .fill{fill:#3e7da6}.gfield-admin-icons .gfield-drag:active path{fill:#fff}.gfield-admin-icons .gfield-drag{cursor:grab}.gfield-admin-icons .gfield-drag:focus i::before,.gfield-admin-icons .gfield-drag:hover i::before{color:#3e7da6}.gfield-admin-icons .gfield-delete{border-radius:0 3px 3px 0;outline:0;position:relative;z-index:20}.gfield-admin-icons .gfield-delete svg{position:relative;top:-1px}.gfield-admin-icons .gfield-icon{background:#242748;color:#fff;font-size:1.5em;opacity:0;position:absolute;right:0;top:0;z-index:10}.gfield-compact-icons{display:none}.gfield-sidebar-message-icon.gform-icon{display:none}#gform_fields.top_label fieldset.gfield{padding:16px}body:not(:-moz-handler-blocked) #gform_fields fieldset.gfield{display:table-cell}.gform_wrapper fieldset.gfield .gfield-admin-icons{margin-bottom:1rem;top:calc((3.15rem) * -1)}.gform_wrapper fieldset.gfield legend{position:relative}_::-webkit-full-page-media,_:future,:root #gform_fields fieldset.gfield .gfield-admin-icons{top:calc((3.15rem) * -1)}.gf_browser_gecko #gform_fields fieldset.gfield .gfield-admin-icons{top:calc((3.15rem) * -1)}.gfield.field_selected:not(.placeholder) .gfield-admin-icons,.gfield:focus-within .gfield-admin-icons,.gfield:hover .gfield-admin-icons,.ui-draggable-dragging .gfield-admin-icons{opacity:1;pointer-events:initial}.ui-draggable-dragging .gfield-admin-icons{background:#242748;border-color:#242748;box-shadow:0 0 1px rgba(18,25,97,.24),0 24px 24px rgba(18,25,97,.03),0 2px 2px rgba(18,25,97,.03),0 4px 4px rgba(18,25,97,.03),0 8px 8px rgba(18,25,97,.03),0 16px 16px rgba(18,25,97,.03)}.ui-draggable-dragging .gfield-admin-icons .gfield-field-action{border-color:#242748}.ui-draggable-dragging .gfield-admin-icons .gfield-drag,.ui-draggable-dragging .gfield-admin-icons .gfield-edit{background-color:#242748;color:#fff}.ui-draggable-dragging .gfield-admin-icons .gfield-duplicate,.ui-draggable-dragging .gfield-admin-icons .gfield-edit{display:none}.ui-draggable-dragging .gfield-admin-icons .gfield-delete{opacity:0}.ui-draggable-dragging .gfield-admin-icons .gfield-delete path,.ui-draggable-dragging .gfield-admin-icons .gfield-drag rect,.ui-draggable-dragging .gfield-admin-icons .gfield-edit path{fill:#fff}.ui-draggable-dragging .gfield-admin-icons .gfield-field-action__description{display:none!important}.ui-draggable-dragging .gfield-admin-icons .gfield-icon{opacity:1}.ui-draggable-dragging .gfield-admin-icons .gform-compact-view-overflow-menu{opacity:0}.ui-draggable-dragging .gform-compact-view-overflow-menu{opacity:0}html[dir=rtl] .gfield-admin-icons button.gfield-delete{border-left:none;border-radius:3px 0 0 3px}html[dir=rtl] .gfield-admin-icons .gfield-drag{border-left:1px solid #d5d7e9;border-radius:0 3px 3px 0;z-index:20}html[dir=rtl] .gfield-admin-icons .gfield-icon{left:0;right:auto}html[dir=rtl] body .gform_wrapper .gfield-drag,html[dir=rtl] body .gform_wrapper .gfield-icon,html[dir=rtl] body .gform_wrapper button.gfield-delete,html[dir=rtl] body .gform_wrapper button.gfield-duplicate{text-align:center!important}.gform_editor_status{background:#32373c;border-radius:4px;bottom:6.125rem;box-shadow:0 2px 4px rgba(0,0,0,.3);color:#fff;font-size:.8125rem;font-style:normal;font-weight:400;line-height:1.125rem;padding:1rem 1.5rem;position:fixed;z-index:99}.gform_editor_status a{color:#fff;margin-left:.375rem}#sidebar_field_label::after{background-color:#ecedf8;border:1px solid #d5d7e9;border-radius:40px;content:attr(data-fieldId-Label) attr(data-fieldId);float:right;font-size:.6875rem;font-weight:600;padding:.1125rem .4625rem}#sidebar_field_label.no-id::after{display:none}html[dir=rtl] #sidebar_field_label::after{float:left;margin:0 1rem .5rem}#sidebar_field_info #sidebar_field_description #sidebar_field_text{color:#555d66;font-size:.8125rem;height:auto;line-height:1rem;padding-right:4.375rem;padding-top:.375rem;text-align:left}.gf-html-container{width:70%}#form_editor_fields_container:where(:not(.gform-compact-view)) .gform_editor .left_label fieldset.gfield,#form_editor_fields_container:where(:not(.gform-compact-view)) .gform_editor .right_label fieldset.gfield{padding:16px}#form_editor_fields_container:where(:not(.gform-compact-view)) .gform_editor .left_label fieldset.gfield .gfield-admin-icons,#form_editor_fields_container:where(:not(.gform-compact-view)) .gform_editor .right_label fieldset.gfield .gfield-admin-icons{top:calc((3rem + 2px) * -1)}#form_editor_fields_container:where(:not(.gform-compact-view)) .gform_editor .left_label legend.gfield_label,#form_editor_fields_container:where(:not(.gform-compact-view)) .gform_editor .right_label legend.gfield_label{top:0}#form_editor_fields_container:where(:not(.gform-compact-view)) #gform_fields.left_label,#form_editor_fields_container:where(:not(.gform-compact-view)) #gform_fields.right_label{-moz-column-gap:3.125rem;column-gap:3.125rem}.gforms_edit_form{line-height:1.188rem}.gforms_edit_form button:focus,.gforms_edit_form li:focus{outline:0}#gf-admin-notices-wrapper,.gform-settings__wrapper,.gforms_edit_form,.gforms_form_settings_wrap,.gforms_help{font-family:inter,-apple-system,blinkmacsystemfont,"Segoe UI",roboto,oxygen-sans,ubuntu,cantarell,"Helvetica Neue",sans-serif;-webkit-font-smoothing:antialiased}.toplevel_page_gf_edit_forms{background-color:#fff}body.toplevel_page_gf_edit_forms #wpfooter{display:none}body.toplevel_page_gf_edit_forms #wpcontent{padding-left:0}body.toplevel_page_gf_edit_forms #wpbody-content{width:100%}body.toplevel_page_gf_edit_forms #gform-form-toolbar{border-bottom:1px solid #ecedf8;box-shadow:none;margin:0}body.toplevel_page_gf_edit_forms .gforms_edit_form{bottom:0;display:flex;flex-grow:1;height:100vh;margin:0!important;overscroll-behavior-y:none;position:fixed;top:46px;width:100%}@media (min-width:783px){body.toplevel_page_gf_edit_forms .gforms_edit_form{top:32px}}body.toplevel_page_gf_edit_forms .gforms_edit_form>table{background-color:#fff;padding-top:3.75rem}#wpbody-content>div:not(.gforms_edit_form):not(.gforms_force_editor_display){display:none!important}.gform-admin .gform-flyout.gform-flyout--choices-ui .gform-flyout__head{padding-block:1.25rem;padding-inline:1.5625rem}.gform-admin .gform-flyout.gform-flyout--choices-ui .gform-flyout__body{padding:1.25rem 1.5625rem}.gform-admin .choices-ui__trigger,.gform-admin .field-choice-clear-default{padding:0 16px 0 13px}.gform-admin .choices-ui__trigger{margin-bottom:20px}.gform-admin .choices-ui__trigger .choices-ui__trigger-icon{font-size:1.5rem}.gform-admin .field-choice-clear-default{margin-top:.65rem}.gform-admin .choices-ui__trigger-section{margin:0}.gform-admin .choices-ui__section{clear:both;padding:.5rem 0 0}.gform-admin .choices-ui__options-list>li{display:inline-block;margin:0 .5rem .5rem 0;vertical-align:top}.gform-admin .choices-ui__options-list>li label{margin-bottom:0}.gform-admin .choices-ui__section-label{color:#242748;font-family:inter,-apple-system,blinkmacsystemfont,"Segoe UI",roboto,oxygen-sans,ubuntu,cantarell,"Helvetica Neue",sans-serif;font-size:.8125rem;font-weight:500;margin:0 0 .8rem}.gform-admin .choices-ui__setting-image-choice{flex-wrap:wrap;inline-size:100%;margin-block-end:1.25rem}.gform-admin .choices-ui__setting-image-choice .gform-toggle__toggle{inline-size:auto}.gform-admin .choices-ui__setting-image-choice .gform-toggle__toggle:checked~#field_choice_image_choice_enabled_description{display:block}.gform-admin .choices-ui__setting-image-choice .gform-toggle__label{margin-bottom:0}.gform-admin .choices-ui__setting-image-choice #field_choice_image_choice_enabled_description{display:none;inline-size:100%;margin-block:0.5rem 0}.gform-admin .gform-flyout--choices-ui--multiselect .choices-ui__setting-image-choice,.gform-admin .gform-flyout--choices-ui--quantity .choices-ui__setting-image-choice,.gform-admin .gform-flyout--choices-ui--select .choices-ui__setting-image-choice{display:none;visibility:hidden}html[dir=rtl] .gform-admin .choices-ui__trigger{padding:0 13px 0 16px}html[dir=rtl] .gform-admin .choices-ui__options-list>li{margin:0 0 .5rem .5rem}.gform-admin-screen #adminmenuback{z-index:9990}.gform-dialog.gform-dialog--editor-button{max-width:400px}.gform-dialog.gform-dialog--editor-button .gform-toggle--with-icons .gform-toggle__label{font-weight:500}.gform-dialog.gform-dialog--editor-button .gform-dialog__content-toggle-text{margin-block-end:1rem;margin-inline-start:3.15rem}body.toplevel_page_gf_edit_forms .gform-form-toolbar{margin:0;max-height:4rem;max-width:100%;padding:0 .9375rem 0 0;position:fixed;width:calc(100% - 160px)}body.toplevel_page_gf_edit_forms .gform-form-switcher__container{top:87px!important}body.toplevel_page_gf_edit_forms.gform-jetpack-admin-menu .gform-form-toolbar{width:calc(100% - 272px)}body.toplevel_page_gf_edit_forms.folded .gform-form-toolbar{width:calc(100% - 36px)}.gf-popover{animation:components-animate__appear-animation .1s cubic-bezier(0,0,.2,1) 0s;animation-fill-mode:forwards;display:none;position:absolute;top:45px}@keyframes components-animate__appear-animation{from{transform:translateY(-2em) scaleY(0) scaleX(0)}to{transform:translateY(0) scaleY(1) scaleX(1)}}.gf-popover::after{border-bottom-color:#fff;border-bottom-style:solid;border-bottom-width:8px;border-image-outset:0;border-image-repeat:stretch;border-image-slice:100%;border-image-source:none;border-image-width:1;border-left-color:transparent;border-left-style:solid;border-left-width:8px;border-right-color:transparent;border-right-style:solid;border-right-width:8px;border-top-color:#444;border-top-style:none;border-top-width:0;content:"";display:block;height:0;left:18.5px;line-height:0;margin-left:-10px;position:absolute;top:-6px;width:0}.gf-popover::before{border-bottom-color:#e2e4e7;border-bottom-style:solid;border-bottom-width:8px;border-image-outset:0;border-image-repeat:stretch;border-image-slice:100%;border-image-source:none;border-image-width:1;border-left-color:transparent;border-left-style:solid;border-left-width:8px;border-right-color:transparent;border-right-style:solid;border-right-width:8px;border-top-color:#444;border-top-style:none;border-top-width:0;content:"";display:block;height:0;left:18.5px;line-height:0;margin-left:-10px;position:absolute;top:-8px;width:0}.gf-popover__content{background:#fff;border:1px solid #e2e4e7;border-bottom:1px solid #e2e4e7;box-shadow:0 3px 30px rgba(25,30,35,.1);height:auto;overflow-y:auto;padding-bottom:.5rem;padding-top:.5rem;width:200px}.gf-popover__button:focus{border:none;box-shadow:none;color:#1e1e1e;outline:1px dotted #1e1e1e;outline-offset:-2px}.gf-popover__button:hover{background:#f3f4f5;border:none;box-shadow:none;color:#1e1e1e}.gf-popover__button{background:0 0;border:none;border-radius:4px;box-shadow:none;color:#40464d;cursor:pointer;display:flex;font-size:.8125rem;padding:8px 15px;text-align:left;text-decoration:none;transition:box-shadow .1s linear;width:100%}.gf-popover__button :first-child{margin-right:4px}div.ui-tabs div.ui-tabs-panel{border:none}div.ui-widget-content{background-color:#fff}.editor-sidebar{height:100%;position:fixed;right:0;top:110px;z-index:1}@media (min-width:783px){.editor-sidebar{top:96px}}html[dir=rtl] .editor-sidebar{left:0;right:unset}.sidebar{background-color:#fff;border-bottom:1px solid #ecedf8;border-left:1px solid #ecedf8;max-height:calc(100% - 85px);min-height:calc(100% - 85px);padding:0;width:370px}.sidebar .sidebar__nav-wrapper{background-color:inherit;border-bottom:1px solid #ecedf8!important;height:8.0625rem;padding:0;position:fixed;width:16.875rem;z-index:1000}@media (min-width:960px){.sidebar .sidebar__nav-wrapper{width:23.125rem}}.sidebar .sidebar__nav-wrapper .search-button{box-sizing:border-box;padding:1rem;position:relative;width:100%}.sidebar .sidebar__nav-wrapper .sidebar__nav{border-bottom:0!important;display:flex;width:100%}.sidebar .sidebar__nav-wrapper .sidebar__nav li.sidebar__nav__item{flex-grow:1;height:3rem}.sidebar .sidebar__nav-wrapper .sidebar__nav li.sidebar__nav__item .sidebar__nav__item-text{align-items:center;box-sizing:border-box;display:flex;height:2.8125rem;justify-content:center;line-height:.875rem;width:100%}.sidebar .sidebar__nav-wrapper .sidebar__nav li.sidebar__nav__item .sidebar__nav__item-text-inner{-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:3;overflow:hidden;text-overflow:ellipsis}.sidebar .sidebar-instructions{padding:21px 23px 21px 57px;position:relative}.sidebar .sidebar-instructions p{margin:0}.sidebar .sidebar-instructions::before{content:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTMiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxMyAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTUgMTIuMjVINC44OTA1NEw0LjgxNjMgMTIuMzMwNEwwLjI1IDE3LjI3NzNWMC42MTgzMjFMMTEuNDk0IDEyLjI1SDVaTTEuNDE2NjcgMTMuNjY2N1YxNC4zMTE5TDEuODUxNDEgMTMuODM1MUw0LjM2MDM3IDExLjA4MzNIOC4xNjY2N0g4Ljc1NDQ3TDguMzQ2NzUgMTAuNjU5OUwxLjg0Njc1IDMuOTA5OTJMMS40MTY2NyAzLjQ2MzNWNC4wODMzM1YxMy42NjY3WiIgZmlsbD0iIzI0Mjc0OCIgc3Ryb2tlPSIjMjQyNzQ4IiBzdHJva2Utd2lkdGg9IjAuNSIvPgo8L3N2Zz4K);height:1.0625rem;left:1.6875rem;line-height:1.25rem;position:absolute;top:50%;transform:translateY(-50%);width:1.0625rem}.sidebar .field_settings,.sidebar .panel-block-tabs{overflow-y:auto}.sidebar .field_settings .simplebar-wrapper,.sidebar .panel-block-tabs .simplebar-wrapper{height:100%}.sidebar .field_settings{padding-bottom:10px}.sidebar .sidebar__nav li.sidebar__nav__item.ui-state-default,.sidebar .sidebar__nav li.sidebar__nav__item.ui-state-default.ui-state-active{background-color:transparent;border:none;border-radius:0;margin:0;min-width:6.6875rem}.sidebar__nav__item-text{padding:0 .625rem}#gform-form-toolbar_links li a,.sidebar .sidebar__nav li.sidebar__nav__item.ui-state-default a{box-shadow:none;color:#242748;display:block;font-size:.8125rem;font-weight:400;line-height:45px;outline:0;padding:0;text-align:center;vertical-align:middle;width:100%}.sidebar .sidebar__nav li.sidebar__nav__item a::after{transition:all .15s linear}.sidebar .sidebar__nav li.sidebar__nav__item.ui-state-default a::after{background:0 0;border-radius:0;content:" ";display:block;height:2px;transition:all .15s linear;width:100%}.sidebar .sidebar__nav li.sidebar__nav__item.ui-state-hover a::after{background:#d5d7e9;border-radius:0;content:" ";display:block;height:3px;transition:all .15s linear;width:100%}.sidebar .sidebar__nav li.sidebar__nav__item.ui-state-default.ui-state-active a::after{background:#0f3d6c;border-radius:0;content:" ";display:block;height:3px;width:100%}.sidebar .sidebar__nav li.sidebar__nav__item.ui-state-default.ui-state-active a:active,.sidebar .sidebar__nav li.sidebar__nav__item.ui-state-default.ui-state-active a:focus{box-shadow:none}.panel-block-tabs__toggle .ui-accordion-header-active{margin-bottom:5px}.panel-block-tabs__toggle i::before{color:#9092b2;content:"\f347"}.panel-block-tabs__toggle:hover{background:#f6f9fc}.panel-block-tabs__toggle:hover i::before{color:#242748}.panel-block-tabs__toggle:focus{border-color:#3985b7;outline:0}.panel-block-tabs__toggle.ui-accordion-header-active i::before{content:"\f343"}.panel-block-tabs__toggle i{color:#9092b2;font-family:dashicons;font-size:18px;font-style:normal;position:absolute;right:1rem}.search-button span{content:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjQ5OTYgMTAuOTk5NkgxMS43MDk2TDExLjQyOTYgMTAuNzI5NkMxMi42Mjk2IDkuMzI5NjUgMTMuMjQ5NiA3LjQxOTY1IDEyLjkwOTYgNS4zODk2NUMxMi40Mzk2IDIuNjA5NjUgMTAuMTE5NiAwLjM4OTY0OSA3LjMxOTY1IDAuMDQ5NjQ5QzMuMDg5NjUgLTAuNDcwMzUxIC0wLjQ3MDM1MSAzLjA4OTY1IDAuMDQ5NjQ5IDcuMzE5NjVDMC4zODk2NDkgMTAuMTE5NiAyLjYwOTY1IDEyLjQzOTYgNS4zODk2NSAxMi45MDk2QzcuNDE5NjUgMTMuMjQ5NiA5LjMyOTY1IDEyLjYyOTYgMTAuNzI5NiAxMS40Mjk2TDEwLjk5OTYgMTEuNzA5NlYxMi40OTk2TDE1LjI0OTYgMTYuNzQ5NkMxNS42NTk2IDE3LjE1OTYgMTYuMzI5NiAxNy4xNTk2IDE2LjczOTYgMTYuNzQ5NkMxNy4xNDk2IDE2LjMzOTYgMTcuMTQ5NiAxNS42Njk2IDE2LjczOTYgMTUuMjU5NkwxMi40OTk2IDEwLjk5OTZaTTYuNDk5NjUgMTAuOTk5NkM0LjAwOTY1IDEwLjk5OTYgMS45OTk2NSA4Ljk4OTY1IDEuOTk5NjUgNi40OTk2NUMxLjk5OTY1IDQuMDA5NjUgNC4wMDk2NSAxLjk5OTY1IDYuNDk5NjUgMS45OTk2NUM4Ljk4OTY1IDEuOTk5NjUgMTAuOTk5NiA0LjAwOTY1IDEwLjk5OTYgNi40OTk2NUMxMC45OTk2IDguOTg5NjUgOC45ODk2NSAxMC45OTk2IDYuNDk5NjUgMTAuOTk5NloiIGZpbGw9IiM5MDkyQjIiLz4KPC9zdmc+Cg==);height:1.0625rem;line-height:1.25rem;position:absolute;right:2rem;top:50%;transform:translateY(-50%);width:1.0625rem}.search-button:hover span{content:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjQ5OTYgMTAuOTk5NkgxMS43MDk2TDExLjQyOTYgMTAuNzI5NkMxMi42Mjk2IDkuMzI5NjUgMTMuMjQ5NiA3LjQxOTY1IDEyLjkwOTYgNS4zODk2NUMxMi40Mzk2IDIuNjA5NjUgMTAuMTE5NiAwLjM4OTY0OSA3LjMxOTY1IDAuMDQ5NjQ5QzMuMDg5NjUgLTAuNDcwMzUxIC0wLjQ3MDM1MSAzLjA4OTY1IDAuMDQ5NjQ5IDcuMzE5NjVDMC4zODk2NDkgMTAuMTE5NiAyLjYwOTY1IDEyLjQzOTYgNS4zODk2NSAxMi45MDk2QzcuNDE5NjUgMTMuMjQ5NiA5LjMyOTY1IDEyLjYyOTYgMTAuNzI5NiAxMS40Mjk2TDEwLjk5OTYgMTEuNzA5NlYxMi40OTk2TDE1LjI0OTYgMTYuNzQ5NkMxNS42NTk2IDE3LjE1OTYgMTYuMzI5NiAxNy4xNTk2IDE2LjczOTYgMTYuNzQ5NkMxNy4xNDk2IDE2LjMzOTYgMTcuMTQ5NiAxNS42Njk2IDE2LjczOTYgMTUuMjU5NkwxMi40OTk2IDEwLjk5OTZaTTYuNDk5NjUgMTAuOTk5NkM0LjAwOTY1IDEwLjk5OTYgMS45OTk2NSA4Ljk4OTY1IDEuOTk5NjUgNi40OTk2NUMxLjk5OTY1IDQuMDA5NjUgNC4wMDk2NSAxLjk5OTY1IDYuNDk5NjUgMS45OTk2NUM4Ljk4OTY1IDEuOTk5NjUgMTAuOTk5NiA0LjAwOTY1IDEwLjk5OTYgNi40OTk2NUMxMC45OTk2IDguOTg5NjUgOC45ODk2NSAxMC45OTk2IDYuNDk5NjUgMTAuOTk5NloiIGZpbGw9IiMzRTdEQTYiLz4KPC9zdmc+Cg==)}.search-button.clearable .clear-button{content:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00LjU4NTc5IDZMMC4yOTI4OTMgMS43MDcxMUMtMC4wOTc2MzExIDEuMzE2NTggLTAuMDk3NjMxMSAwLjY4MzQxOCAwLjI5Mjg5MyAwLjI5Mjg5M0MwLjY4MzQxOCAtMC4wOTc2MzExIDEuMzE2NTggLTAuMDk3NjMxMSAxLjcwNzExIDAuMjkyODkzTDYgNC41ODU3OUwxMC4yOTI5IDAuMjkyODkzQzEwLjY4MzQgLTAuMDk3NjMxMSAxMS4zMTY2IC0wLjA5NzYzMTEgMTEuNzA3MSAwLjI5Mjg5M0MxMi4wOTc2IDAuNjgzNDE4IDEyLjA5NzYgMS4zMTY1OCAxMS43MDcxIDEuNzA3MTFMNy40MTQyMSA2TDExLjcwNzEgMTAuMjkyOUMxMi4wOTc2IDEwLjY4MzQgMTIuMDk3NiAxMS4zMTY2IDExLjcwNzEgMTEuNzA3MUMxMS4zMTY2IDEyLjA5NzYgMTAuNjgzNCAxMi4wOTc2IDEwLjI5MjkgMTEuNzA3MUw2IDcuNDE0MjFMMS43MDcxMSAxMS43MDcxQzEuMzE2NTggMTIuMDk3NiAwLjY4MzQxOCAxMi4wOTc2IDAuMjkyODkzIDExLjcwNzFDLTAuMDk3NjMwOSAxMS4zMTY2IC0wLjA5NzYzMDkgMTAuNjgzNCAwLjI5Mjg5MyAxMC4yOTI5TDQuNTg1NzkgNloiIGZpbGw9IiMyNDI3NDgiLz4KPC9zdmc+Cg==);max-height:.75rem;max-width:.75rem;padding-right:.3175rem}.search-button.clearable:hover .clear-button{content:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00LjU4NTc5IDZMMC4yOTI4OTMgMS43MDcxMUMtMC4wOTc2MzExIDEuMzE2NTggLTAuMDk3NjMxMSAwLjY4MzQxOCAwLjI5Mjg5MyAwLjI5Mjg5M0MwLjY4MzQxOCAtMC4wOTc2MzExIDEuMzE2NTggLTAuMDk3NjMxMSAxLjcwNzExIDAuMjkyODkzTDYgNC41ODU3OUwxMC4yOTI5IDAuMjkyODkzQzEwLjY4MzQgLTAuMDk3NjMxMSAxMS4zMTY2IC0wLjA5NzYzMTEgMTEuNzA3MSAwLjI5Mjg5M0MxMi4wOTc2IDAuNjgzNDE4IDEyLjA5NzYgMS4zMTY1OCAxMS43MDcxIDEuNzA3MTFMNy40MTQyMSA2TDExLjcwNzEgMTAuMjkyOUMxMi4wOTc2IDEwLjY4MzQgMTIuMDk3NiAxMS4zMTY2IDExLjcwNzEgMTEuNzA3MUMxMS4zMTY2IDEyLjA5NzYgMTAuNjgzNCAxMi4wOTc2IDEwLjI5MjkgMTEuNzA3MUw2IDcuNDE0MjFMMS43MDcxMSAxMS43MDcxQzEuMzE2NTggMTIuMDk3NiAwLjY4MzQxOCAxMi4wOTc2IDAuMjkyODkzIDExLjcwNzFDLTAuMDk3NjMwOSAxMS4zMTY2IC0wLjA5NzYzMDkgMTAuNjgzNCAwLjI5Mjg5MyAxMC4yOTI5TDQuNTg1NzkgNloiIGZpbGw9IiMyNDI3NDgiLz4KPC9zdmc+Cg==)}html[dir=rtl] .search-button span{left:2rem;right:auto;right:initial}html[dir=rtl] .search-button::after{left:1.75rem;right:auto}.gf-field-group__no-results{font-size:.75rem;font-style:italic;padding:0 1rem}.panel-block-tabs__body--nopadding{padding:1rem 0 0 0}.panel-block-tabs__body--settings label{position:static}.panel-block-tabs__body--settings .field-choice-row label{margin-bottom:0}.sidebar .sidebar__panel{padding:131px 0 0}.sidebar__panel.ui-tabs-panel{display:flex;flex-flow:column;height:calc(100vh - 220px);text-align:left}.panel-block--flex{display:flex;flex-shrink:0}.panel-block--hidden{display:none}#nothing_selected{padding:25px;text-align:center}.rules_container li,div.field_setting,li.field_setting,li.pagination_setting{margin-bottom:.9375rem;position:relative}div.field_setting.conditional_logic_page_setting{margin-bottom:0}li.pagination_setting .percentage_style_setting~.percentage_custom_container{margin-top:.9375rem}.input_active_icon{margin:.3rem 0 0 0}.field-choice-row{align-items:center;display:flex;flex-flow:row nowrap;padding:0;width:100%}.field-choice-row+.field-choice-row{margin-top:.5rem}.field-choice-row button:not(.field-choice-button),.field-choice-row button:not(.field-choice-button):hover{background:0 0;border:none;color:#000;cursor:pointer;vertical-align:middle}.field-choice-button,.gf_delete_field_choice,.gf_insert_field_choice{background:transparent center center no-repeat;border:0 none;cursor:pointer;height:.625rem;width:.625rem}.field-choice-button::before{content:none}.field-choice-button--insert,.gf_insert_field_choice{background-image:url(../../../images/icon-add.svg);margin:0 .25rem}.field-choice-button--delete,.gf_delete_field_choice{background-image:url(../../../images/icon-close-small.svg);margin-left:.25rem}.gf_delete_field_choice,.gf_insert_field_choice{font-family:dashicons}html:not([dir=rtl]) .field-choice-row .field-choice-button--delete{margin-left:.25rem}html[dir=rtl] .field-choice-row .field-choice-button--delete{margin-right:.25rem}.field-choice-button--delete,.field-choice-button--insert{margin:0;padding:.65rem}.field-choice-button--delete:focus,.field-choice-button--insert:focus{box-shadow:0 0 0 2px #bed8ed}.field-choice-button--delete{margin-right:.1rem}.field-choice-handle{background:url(../../../images/icon-handle.svg) center center no-repeat;cursor:move;display:inline-block;height:.75rem;vertical-align:middle;width:.625rem}html:not([dir=rtl]) .field-choice-handle{margin-left:.3125rem;width:.588rem}html[dir=rtl] .field-choice-handle{margin-right:.3125rem;width:.588rem}#sidebar_field_info{color:#242748;font-size:.8125rem;padding:15px 0 17px 0;text-align:center;width:100%}#sidebar_field_info #sidebar_field_description #sidebar_field_text{color:#242748;font-size:.8125rem;height:auto;line-height:1.188rem;margin:0;padding-top:6px;text-align:left}#sidebar_field_info #sidebar_field_description #sidebar_field_label{color:#242748;font-size:13px;font-weight:500;height:18px;line-height:1.188rem;text-align:left}#sidebar_field_info #sidebar_field_description{width:75%}#sidebar_field_info #sidebar_field_icon{align-items:center;display:flex;font-size:1.5rem;justify-content:center;width:68px}#sidebar_field_message_container{margin-block-end:0.5rem;padding-inline:1.25rem}.sidebar legend{margin-bottom:.7rem;padding-inline-end:0;padding-inline-start:0;width:100%}.sidebar .gf-color-picker-wrapper{align-items:center;display:flex}.sidebar .gf-color-picker-wrapper img{display:inline-block;height:22px;margin-inline-start:.375rem;width:22px}.gform-sidebar-setting-grid-header{font-weight:700}.sidebar .gform-sidebar-setting-grid-wrapper__two-column>div{display:grid;grid-template-columns:120px 1fr;margin-block-end:0.25rem}.sidebar .gform-sidebar-setting-grid-wrapper__three-column>div{display:grid;grid-template-columns:55px 110px 1fr;margin-block-end:0.25rem}.sidebar .gform-sidebar-setting-grid-wrapper div>div,.sidebar .gform-sidebar-setting-grid-wrapper div>label{align-items:center;align-self:center;display:grid}.sidebar .choice_min_max_setting label{display:block;margin-bottom:.7rem}.sidebar .choice_min_max_setting input[type=number]{width:100%}.sidebar .choice_min_max_setting #choice_number_min_max_wrapper{display:flex;justify-content:space-between;margin-block-start:0.9375rem}.sidebar .choice_min_max_setting #choice_number_min_max_wrapper div{width:calc(50% - .46875rem)}#form_editor_fields_container:where(:not(.gform-compact-view)) .admin-hidden-markup{line-height:0}#form_editor_fields_container:where(:not(.gform-compact-view)) .admin-hidden-markup .gform-icon--hidden{font-family:gform-icons-common!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:none;text-transform:none;color:#6a6a80;font-size:20px;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}#form_editor_fields_container:where(:not(.gform-compact-view)) .admin-hidden-markup .gform-icon--hidden::before{content:"\e913"}#form_editor_fields_container:where(:not(.gform-compact-view)) .admin-hidden-markup span{font-size:0}#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where([\:has\(label\:not\(\:empty\)\)]) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where([\:has\(.gsection_title\:not\(\:empty\)\)]) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:not(.gfield_html)) .gfield-admin-wrapper[\:has\(\%3E\%20label\:not\(.screen-reader-text\)\)] .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where([\:has\(legend\:not\(.screen-reader-text\)\)]) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup{position:absolute}#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where(:has(label:not(:empty))) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where(:has(.gsection_title:not(:empty))) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:has(legend:not(.screen-reader-text))) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:not(.gfield_html)) .gfield-admin-wrapper:has(> label:not(.screen-reader-text)) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where(:not(.gsection:has(.gsection_title:empty))) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where(:not(.gsection:has(.gsection_title:empty))) .admin-hidden-markup{position:absolute}#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where([\:has\(label\:not\(\:empty\)\)]) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where([\:has\(.gsection_title\:not\(\:empty\)\)]) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:not(.gfield_html)) .gfield-admin-wrapper[\:has\(\%3E\%20label\:not\(.screen-reader-text\)\)] .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where([\:has\(legend\:not\(.screen-reader-text\)\)]) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup span{border:0;clip:rect(0,0,0,0);height:1px;margin:-1px;overflow:hidden;word-wrap:normal;padding:0;position:absolute;white-space:nowrap;width:1px}#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where([\:has\(label\:not\(\:empty\)\)]) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where([\:has\(.gsection_title\:not\(\:empty\)\)]) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:not(.gfield_html)) .gfield-admin-wrapper[\:has\(\%3E\%20label\:not\(.screen-reader-text\)\)] .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where([\:has\(legend\:not\(.screen-reader-text\)\)]) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup span{border:0;clip:rect(0,0,0,0);height:1px;margin:-1px;overflow:hidden;word-wrap:normal;padding:0;position:absolute;white-space:nowrap;width:1px}#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where(:has(label:not(:empty))) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where(:has(.gsection_title:not(:empty))) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:has(legend:not(.screen-reader-text))) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:not(.gfield_html)) .gfield-admin-wrapper:has(> label:not(.screen-reader-text)) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where(:not(.gsection:has(.gsection_title:empty))) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where(:not(.gsection:has(.gsection_title:empty))) .admin-hidden-markup span{border:0;clip:rect(0,0,0,0);height:1px;margin:-1px;overflow:hidden;word-wrap:normal;padding:0;position:absolute;white-space:nowrap;width:1px}#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where([\:has\(label\:not\(\:empty\)\)]) .admin-hidden-markup+.gsection_title,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where([\:has\(label\:not\(\:empty\)\)]) .admin-hidden-markup+label,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where([\:has\(label\:not\(\:empty\)\)]) .admin-hidden-markup+legend,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where([\:has\(.gsection_title\:not\(\:empty\)\)]) .admin-hidden-markup+.gsection_title,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where([\:has\(.gsection_title\:not\(\:empty\)\)]) .admin-hidden-markup+label,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where([\:has\(.gsection_title\:not\(\:empty\)\)]) .admin-hidden-markup+legend,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:not(.gfield_html)) .gfield-admin-wrapper[\:has\(\%3E\%20label\:not\(.screen-reader-text\)\)] .admin-hidden-markup+.gsection_title,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:not(.gfield_html)) .gfield-admin-wrapper[\:has\(\%3E\%20label\:not\(.screen-reader-text\)\)] .admin-hidden-markup+label,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:not(.gfield_html)) .gfield-admin-wrapper[\:has\(\%3E\%20label\:not\(.screen-reader-text\)\)] .admin-hidden-markup+legend,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where([\:has\(legend\:not\(.screen-reader-text\)\)]) .admin-hidden-markup+.gsection_title,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where([\:has\(legend\:not\(.screen-reader-text\)\)]) .admin-hidden-markup+label,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where([\:has\(legend\:not\(.screen-reader-text\)\)]) .admin-hidden-markup+legend,#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup+.gsection_title,#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup+label,#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup+legend,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup+.gsection_title,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup+label,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup+legend{padding-inline-start:26px}#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where(:has(label:not(:empty))) .admin-hidden-markup+.gsection_title,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where(:has(label:not(:empty))) .admin-hidden-markup+label,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where(:has(label:not(:empty))) .admin-hidden-markup+legend,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where(:has(.gsection_title:not(:empty))) .admin-hidden-markup+.gsection_title,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where(:has(.gsection_title:not(:empty))) .admin-hidden-markup+label,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where(:has(.gsection_title:not(:empty))) .admin-hidden-markup+legend,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:has(legend:not(.screen-reader-text))) .admin-hidden-markup+.gsection_title,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:has(legend:not(.screen-reader-text))) .admin-hidden-markup+label,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:has(legend:not(.screen-reader-text))) .admin-hidden-markup+legend,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:not(.gfield_html)) .gfield-admin-wrapper:has(> label:not(.screen-reader-text)) .admin-hidden-markup+.gsection_title,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:not(.gfield_html)) .gfield-admin-wrapper:has(> label:not(.screen-reader-text)) .admin-hidden-markup+label,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:not(.gfield_html)) .gfield-admin-wrapper:has(> label:not(.screen-reader-text)) .admin-hidden-markup+legend,#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where(:not(.gsection:has(.gsection_title:empty))) .admin-hidden-markup+.gsection_title,#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where(:not(.gsection:has(.gsection_title:empty))) .admin-hidden-markup+label,#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where(:not(.gsection:has(.gsection_title:empty))) .admin-hidden-markup+legend,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where(:not(.gsection:has(.gsection_title:empty))) .admin-hidden-markup+.gsection_title,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where(:not(.gsection:has(.gsection_title:empty))) .admin-hidden-markup+label,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where(:not(.gsection:has(.gsection_title:empty))) .admin-hidden-markup+legend{padding-inline-start:26px}#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden .gfield-admin-wrapper:where([\:has\(\%3E\%20label.screen-reader-text\)]) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where([\:has\(label\:empty\)]) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where([\:has\(.gsection_title\:empty\)]) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where([\:has\(legend.screen-reader-text\)]) .admin-hidden-markup{margin-block-end:7px}#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden .gfield-admin-wrapper:where(:has(> label.screen-reader-text)) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where(:has(label:empty)) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where(:has(.gsection_title:empty)) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:has(legend.screen-reader-text)) .admin-hidden-markup{margin-block-end:7px}#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden .gfield-admin-wrapper:where([\:has\(\%3E\%20label.screen-reader-text\)]) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where([\:has\(label\:empty\)]) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where([\:has\(.gsection_title\:empty\)]) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where([\:has\(legend.screen-reader-text\)]) .admin-hidden-markup span{opacity:0;visibility:hidden}#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden .gfield-admin-wrapper:where(:has(> label.screen-reader-text)) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where(:has(label:empty)) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where(:has(.gsection_title:empty)) .admin-hidden-markup span,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:has(legend.screen-reader-text)) .admin-hidden-markup span{opacity:0;visibility:hidden}#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where([\:has\(label\:empty\)]) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where([\:has\(.gsection_title\:empty\)]) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where([\:has\(legend.screen-reader-text\)]) .admin-hidden-markup{margin-block-end:0}#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gfield_html:where(:has(label:empty)) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden.gsection:where(:has(.gsection_title:empty)) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .gfield.admin-hidden:where(:has(legend.screen-reader-text)) .admin-hidden-markup{margin-block-end:0}#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where(.gsection[\:has\(.gsection_title\:empty\)]) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where(.gsection[\:has\(.gsection_title\:empty\)]) .admin-hidden-markup{inline-size:100%}#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where(.gsection:has(.gsection_title:empty)) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where(.gsection:has(.gsection_title:empty)) .admin-hidden-markup{inline-size:100%}#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where([\:not-has\(.gsection_title\:empty\)]) .admin-hidden-markup{inset-inline-start:16px}#form_editor_fields_container:where(:not(.gform-compact-view)) .left_label .gfield.admin-hidden:where(:not(.gsection:has(.gsection_title:empty))) .admin-hidden-markup,#form_editor_fields_container:where(:not(.gform-compact-view)) .right_label .gfield.admin-hidden:where(:not(.gsection:has(.gsection_title:empty))) .admin-hidden-markup{inset-inline-start:16px}.gf_editor_status:active,.gf_editor_status:focus{background-color:#1e1e1e;box-shadow:0 0 0 1px #fff,0 0 0 3px #007cba}.gf_editor_status:hover{background-color:#1e1e1e}.gf_editor_status a:hover{color:#007cba}.gf_editor_status{background-color:#242748;border-radius:4px;bottom:0;box-shadow:0 2px 4px rgba(0,0,0,.3);box-sizing:border-box;color:#fff;cursor:pointer;font-family:inter,-apple-system,blinkmacsystemfont,"Segoe UI",roboto,oxygen-sans,ubuntu,cantarell,"Helvetica Neue",sans-serif;font-size:.8125rem;height:3.125rem;left:11rem;max-width:600px;opacity:0;padding:1rem 1.5rem;position:fixed;width:-moz-fit-content;width:fit-content}.gf_editor_status a{color:#fff;display:inline-block;height:auto;line-height:1.4;margin-left:2rem;padding:0}.gf_editor_status a:focus{box-shadow:none;outline:0}.rtl .gf_editor_status{left:auto;right:11rem}.rtl .gf_editor_status a{margin-left:0;margin-right:2rem}.folded .gf_editor_status{left:52px}#gfield_settings_category_container,.gfield_settings_input_choices_container{margin-bottom:.5rem;margin-top:.9rem;max-height:222px;overflow-y:auto}#gfield_settings_choices_container label:not(.gform-choice__selected-label){margin-bottom:.5rem;padding:0}html:not([dir=rtl]) #gfield_settings_choices_container label:first-child{margin-left:3.325rem}html[dir=rtl] #gfield_settings_choices_container label:first-child{margin-right:3.325rem}#field_columns li input.field-choice-text{margin-bottom:.5rem}#gfield_settings_choices_container .gfield_choice_header_label{display:inline-block!important;width:calc(100% - 6rem)}.limits-enabled #gfield_settings_choices_container .gfield_choice_header_label{width:calc(100% - 8.5rem)}#gfield_settings_choices_container.choice_with_value .gfield_choice_header_label{width:calc(50% - 3rem)}.limits-enabled #gfield_settings_choices_container.choice_with_value .gfield_choice_header_label{width:calc(50% - 4.4375rem)}#gfield_settings_choices_container .gfield_choice_header_value{width:calc(50% - 3rem)}.limits-enabled #gfield_settings_choices_container .gfield_choice_header_value{width:calc(50% - 4.4375rem)}#gfield_settings_choices_container{margin-bottom:.5rem}.limits-enabled #gfield_settings_choices_container .gfield_choice_header_limit{float:none;margin-right:0;width:2.5rem}#gfield_settings_choices_container.choice_with_price>label{width:calc(50% - 3rem)}.limits-enabled #gfield_settings_choices_container.choice_with_price>label{width:calc(50% - 4.4375rem)}.limits-enabled #gfield_settings_choices_container.choice_with_price .gfield_choice_header_limit{width:2.5rem}#gfield_settings_choices_container.choice_with_price .field-choice-input{flex-basis:calc(50% - 3rem);width:calc(50% - 3rem)}.limits-enabled #gfield_settings_choices_container.choice_with_price .field-choice-input{flex-basis:auto;width:calc(50% - 4.25rem)}#gfield_settings_choices_container.choice_with_value_and_price>label{width:calc(33.332% - 2rem)}.limits-enabled #gfield_settings_choices_container.choice_with_value_and_price>label{width:calc(33.332% - 2.8125rem)}#gfield_settings_choices_container.choice_with_value_and_price .field-choice-price{margin-left:.3125rem}#gfield_settings_choices_container.choice_with_value_and_price .field-choice-input:not(.field-choice-limit){flex-basis:calc(33.332% - 2rem);width:calc(33.332% - 2rem)}.limits-enabled #gfield_settings_choices_container.choice_with_value_and_price .field-choice-input:not(.field-choice-limit){width:calc(33.332% - 2.8125rem)}html[dir=rtl] #gfield_settings_choices_container.choice_with_value_and_price .field-choice-price{margin-left:0;margin-right:.3125rem}html[dir=rtl] #gfield_settings_choices_container.choice_with_value_and_price>label{width:calc(33.332% - 2.3rem)}.gfield_rule_input,.gfield_rule_value_dropdown{display:none}.gfield_rule_input.active,.gfield_rule_value_dropdown.active{display:inline-block}#legacy_field_settings_container{display:none!important;left:0;position:absolute;top:0}.gf_calculation_buttons{align-items:center;background-color:#f5f5f5;border-radius:5px 5px 0 0;box-shadow:inset 0 0 0 1px #ddd;display:flex;float:none;height:30px;margin-right:0;margin-top:7px;padding-bottom:5px;padding-top:5px;width:100%}.gf_calculation_trigger{display:inline-block;margin:.25rem 0 .75rem}.gf_calculation_buttons input[type=button]{background-color:#f5f5f5;border:1px solid #cfcfcf;border-radius:5px;cursor:pointer;float:none;font-weight:600;height:27px;margin-left:5px;padding:0;text-align:center;width:27px}.field_calculation_rounding label{display:block;margin-bottom:.75rem}#field_calculation_formula{border-radius:0 0 5px 5px}.notice{display:none}#gform_input_mask{padding-top:.9375rem}.maxlen_setting.field_setting{min-height:4.1875rem}.gform_inline_options{display:flex;justify-content:flex-start}.gform_inline_options div{padding-right:.5rem}html[dir=rtl] .gform_inline_options div{padding-left:.5rem;padding-right:0}.post_custom_field_setting>fieldset{margin-bottom:12px}#form_editor_fields_container .gf_invisible,#form_editor_fields_container .gfield_visibility_hidden{left:unset;position:relative;visibility:visible}.field_edit_icon,.form_edit_icon{display:none;float:right;margin-left:6px}.field_duplicate_icon{display:none;float:right;margin:0 0 0 8px}.field_duplicate_icon i{color:#185d7c!important}.field_delete_icon,.form_delete_icon{display:none;float:right;margin:-1px 0 0 6px!important}#gfcs-container #gfcs-drop{width:auto}html[dir=rtl] body.toplevel_page_gf_edit_forms #wpcontent{padding-left:0;padding-right:0}html[dir=rtl] .sidebar{border-left:none;border-right:1px solid #9092b2}html[dir=rtl] .ui-tabs .ui-tabs-nav li{float:right}html[dir=rtl] #sidebar_field_info{flex-flow:row-reverse;justify-content:space-between}html[dir=rtl] #sidebar_field_info #sidebar_field_description #sidebar_field_label{text-align:right}html[dir=rtl] #sidebar_field_info #sidebar_field_description #sidebar_field_text{padding-right:0;text-align:right}html[dir=rtl] #sidebar_field_info #sidebar_field_icon{order:3}html[dir=rtl] .panel-block-tabs__toggle,html[dir=rtl] .section_label{text-align:right}html[dir=rtl] .panel-block-tabs__toggle i{left:27px;right:inherit}html[dir=rtl] .panel-block-tabs__toggle{padding-left:0;padding-right:10px}html[dir=rtl] #gppa>li,html[dir=rtl] .panel-block-tabs__body>li{text-align:right}html[dir=rtl] .panel-block-tabs__body--settings input[type=checkbox]+label::before,html[dir=rtl] .panel-block-tabs__body--settings input[type=checkbox]:not(.gform-field__toggle-input):not(.field-choice-type)+label::before{margin-left:6px;margin-right:0}html[dir=rtl] .right ul#gf__tag_list{left:0}html[dir=rtl] .right ul#gf_merge_tag_list{left:0;right:auto;right:initial}@media only screen and (max-width:960px){.editor-sidebar{width:270px}.sidebar{width:270px}.sidebar .sidebar__nav{width:270px}.add-buttons li button{height:5.3125rem;white-space:normal;width:90%}.add-buttons,.panel-block-tabs__body{padding:0 8px 16px 8px}.sidebar .ui-tabs .ui-tabs-nav{width:270px}body.toplevel_page_gf_edit_forms.auto-fold #gform-form-toolbar{width:calc(100% - 36px)}}@media only screen and (max-width:782px){.wp-responsive-open .editor-sidebar{right:-207px}html[dir=rtl] .wp-responsive-open .editor-sidebar{left:-207px}body.toplevel_page_gf_edit_forms.auto-fold #gform-form-toolbar{width:100%}.sidebar{max-height:calc(100% - 79px);min-height:calc(100% - 79px)}}.gforms_edit_form .gform-compact-view .gfield-admin-wrapper{container-name:fieldContainer;container-type:inline-size}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer){border:1px solid #d5d7e9;border-radius:3px;box-shadow:0 1px 2px rgba(0,0,0,.05);display:flex;margin-block-end:0;padding-block:0.563rem 0.5rem;padding-inline:0 1rem}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .ginput_container,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .ginput_container{display:none!important}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gf-pagebreak,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gsection_title,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater label.gfield_label,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater legend.gfield_label>span,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater p,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gf-pagebreak,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gsection_title,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) label.gfield_label,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) legend.gfield_label>span,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) p{background-color:#f6f9fc;block-size:25px;border:1px solid #d5d7e9;border-radius:3px;display:block;font-size:.688rem;font-weight:600;line-height:1.563rem;margin-block-end:0;overflow:hidden;padding-block:0;padding-inline:0.625rem;text-overflow:ellipsis;white-space:nowrap}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gf-pagebreak:empty,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gsection_title:empty,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater label.gfield_label:empty,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater legend.gfield_label>span:empty,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater p:empty,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gf-pagebreak:empty,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gsection_title:empty,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) label.gfield_label:empty,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) legend.gfield_label>span:empty,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) p:empty{border:none;padding:0}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gf-pagebreak>.gfield_required,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gsection_title>.gfield_required,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater label.gfield_label>.gfield_required,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater legend.gfield_label>span>.gfield_required,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater p>.gfield_required,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gf-pagebreak>.gfield_required,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gsection_title>.gfield_required,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) label.gfield_label>.gfield_required,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) legend.gfield_label>span>.gfield_required,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) p>.gfield_required{display:inline;margin-inline-start:6px}.rtl .gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gf-pagebreak>.gfield_required,.rtl .gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gsection_title>.gfield_required,.rtl .gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater label.gfield_label>.gfield_required,.rtl .gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater legend.gfield_label>span>.gfield_required,.rtl .gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater p>.gfield_required,.rtl .gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gf-pagebreak>.gfield_required,.rtl .gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gsection_title>.gfield_required,.rtl .gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) label.gfield_label>.gfield_required,.rtl .gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) legend.gfield_label>span>.gfield_required,.rtl .gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) p>.gfield_required{margin-inline-end:6px;margin-inline-start:0}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater p,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) p{margin-block-start:0}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater legend.gfield_label,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) legend.gfield_label{display:contents}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-admin-icons,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-admin-icons{block-size:1.5rem;border:none;box-shadow:none;opacity:1;position:static}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-admin-icons .gfield-icon,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-admin-icons .gfield-icon{color:#242748;font-size:1.3125rem;opacity:1;position:static}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-admin-icons .gfield-field-action,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-admin-icons .gfield-field-action{background:0 0;block-size:1.5rem}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-admin-icons .gfield-field-action .gfield-field-action__description,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-admin-icons .gfield-field-action .gfield-field-action__description{inset-block-start:-2rem}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-admin-icons .gfield-delete,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-admin-icons .gfield-duplicate,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-admin-icons .gfield-edit,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-admin-icons .gfield-delete,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-admin-icons .gfield-duplicate,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-admin-icons .gfield-edit{border:none;display:none;opacity:0;transition:all ease-in-out .2s}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-admin-icons .gfield-sidebar-message-icon,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-admin-icons .gfield-sidebar-message-icon{display:none}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater:hover,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer):hover{box-shadow:0 1px 2px 0 rgba(18,25,97,.06),0 1px 3px 0 rgba(18,25,97,.1)}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater:hover::after,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer):hover::after{content:"";display:block;inset-block-end:0;inset-block-start:0;inset-inline-end:-1rem;position:absolute;width:1rem}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .admin-hidden-markup,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-compact-icons,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .admin-hidden-markup,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-compact-icons{display:flex;gap:.625rem;order:1;padding-block:0;padding-inline:0.875rem 0.6rem}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .admin-hidden-markup>*,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-compact-icons>*,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .admin-hidden-markup>*,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-compact-icons>*{background-color:#f6f9fc;block-size:25px;border:1px solid #d5d7e9;border-radius:3px;display:inline-block;font-size:11px;font-weight:600;line-height:1.563rem;padding-block:0;padding-inline:0.625rem}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .admin-hidden-markup .gfield-compact-icon--id,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-compact-icons .gfield-compact-icon--id,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .admin-hidden-markup .gfield-compact-icon--id,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-compact-icons .gfield-compact-icon--id{display:none;margin-inline-end:.25rem;white-space:nowrap}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .admin-hidden-markup .gfield-compact-icon--conditional,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-compact-icons .gfield-compact-icon--conditional,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .admin-hidden-markup .gfield-compact-icon--conditional,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-compact-icons .gfield-compact-icon--conditional{display:none;font-size:20px;margin-inline-end:.875rem;padding-block-start:1.5px}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater::before,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer)::before{inset-block-end:0;inset-block-start:0;inset-inline-start:-0.625rem}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .ui-resizable-e,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .ui-resizable-w,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .ui-resizable-e,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .ui-resizable-w{inset-block-end:0;inset-block-start:0;inset-inline-end:-0.625rem}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater h2.gsection_title,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater h3.gsection_title,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) h2.gsection_title,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) h3.gsection_title{color:inherit;inline-size:auto;padding-inline-end:0.625rem!important;padding-inline-start:0.625rem!important}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .admin-hidden-markup,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .admin-hidden-markup{margin-inline-start:-.875rem;order:1;padding-inline:0;position:static}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .admin-hidden-markup .gform-icon--hidden,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .admin-hidden-markup .gform-icon--hidden{display:inline-block;font-size:1rem;margin-inline-end:12px}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .admin-hidden-markup span,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .copy_values_option_container,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .copy_values_option_container[style],.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gf-html-container,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield_description,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield_password_strength,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .ginput_amount,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gsection_description,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .admin-hidden-markup span,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .copy_values_option_container,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .copy_values_option_container[style],.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gf-html-container,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield_description,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield_password_strength,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .ginput_amount,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gsection_description{display:none!important}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater.gfield--type-page label,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer).gfield--type-page label{display:none}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater.gfield--type-page .gf-pagebreak,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer).gfield--type-page .gf-pagebreak{color:#242748;text-transform:none;width:auto}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater.gfield--type-page .gf-pagebreak::after,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater.gfield--type-page .gf-pagebreak::before,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer).gfield--type-page .gf-pagebreak::after,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer).gfield--type-page .gf-pagebreak::before{background:0 0;color:#5b5e80;content:" - - ";display:inline;height:auto}@container fieldContainer (max-width:250px){.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .admin-hidden-markup,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-compact-icons,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .admin-hidden-markup,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-compact-icons{display:none}}@container fieldContainer (max-width:160px){.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-admin-icons .gfield-icon,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-admin-icons .gfield-icon{display:none}}@container fieldContainer (max-width:100px){.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-admin-icons .gfield-delete,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-admin-icons .gfield-duplicate,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-admin-icons .gfield-delete,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-admin-icons .gfield-duplicate{display:none!important}:is(.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer),.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater)::after{display:none!important}}@container fieldContainer (max-width:80px){.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield_label,.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater legend.gfield_label span,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield_label,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) legend.gfield_label span{display:none}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gfield-admin-icons .gfield-icon,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-admin-icons .gfield-icon{padding-inline-start:0.4rem;width:auto}}.gforms_edit_form .gform-compact-view #gform_fields .gfield--type-repeater .gform-icon.gform-icon--ellipsis.gform-button__icon,.gforms_edit_form .gform-compact-view #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gform-icon.gform-icon--ellipsis.gform-button__icon{transform:rotate(90deg)}.gforms_edit_form .gform-compact-view #gform_fields .gfield.trigger-reflow{display:none}.gforms_edit_form .gform-compact-view .gfield-admin-icons .gfield-field-action .dashicons{line-height:normal;line-height:initial}.gforms_edit_form .gform-compact-view.gform-compact-view--show-id #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-compact-icons .gfield-compact-icon--id{display:inline-block}.gforms_edit_form .gform-compact-view.gform-compact-view--show-id #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .admin-hidden-markup{margin-inline-start:0}.gforms_edit_form .gform-compact-view.gform-compact-view--show-id #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .gfield-compact-icon--conditional{margin-inline-end:.25rem}@container fieldContainer (max-width:340px){.gforms_edit_form .gform-compact-view.gform-compact-view--show-id #gform_fields .gfield:where(:not(#field_submit):not(.ui-draggable-dragging)):not(.spacer) .admin-hidden-markup{display:none}}.gforms_edit_form .gform-compact-view #gform_fields .gfield.ui-draggable-dragging .admin-hidden-markup{display:none}.gforms_edit_form .gform-compact-view #gform_fields .gfield.ui-draggable-dragging .gfield-admin-icons{inset-block-start:0;position:absolute;transition:none}.gforms_edit_form .gform-compact-view #field_submit.field_selected:not(.placeholder) .gfield-admin-icons,.gforms_edit_form .gform-compact-view #field_submit:focus-within .gfield-admin-icons{display:none}.gforms_edit_form .gform-compact-view #field_submit .ui-resizable-e,.gforms_edit_form .gform-compact-view #field_submit .ui-resizable-w{inset-block-end:0;inset-block-start:0.25rem;inset-inline-end:-0.65rem}.gforms_edit_form .gform-compact-view #gform_fields{grid-auto-rows:minmax(0,2.813rem)}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-error,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-warning{border:1px solid #ffc7bb}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-error .gfield-admin-icons .gfield-field-action,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-error .gfield-admin-icons .gfield-icon,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-error .gfield-admin-icons .gform-icon--drag-indicator,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-warning .gfield-admin-icons .gfield-field-action,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-warning .gfield-admin-icons .gfield-icon,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-warning .gfield-admin-icons .gform-icon--drag-indicator{color:#c02b0a}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-error .gfield-admin-icons .gfield-sidebar-message-icon,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-warning .gfield-admin-icons .gfield-sidebar-message-icon{border-inline-start-color:#ffc7bb}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-error .admin-hidden-markup .gform-icon--hidden,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-error .gfield-compact-icon--conditional,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-error .gfield-compact-icon--id,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-error label.gform-field-label,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-error legend.gform-field-label>span,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-warning .admin-hidden-markup .gform-icon--hidden,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-warning .gfield-compact-icon--conditional,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-warning .gfield-compact-icon--id,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-warning label.gform-field-label,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-warning legend.gform-field-label>span{background-color:#fff9f9;border:1px solid #ffc7bb;color:#c02b0a}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-info{border:1px solid #d2d5db}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-info .gfield-admin-icons .gfield-field-action,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-info .gfield-admin-icons .gfield-icon,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-info .gfield-admin-icons .gform-icon--drag-indicator{color:#585e6a}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-info .gfield-admin-icons .gfield-sidebar-message-icon{border-inline-start-color:#d2d5db;color:#242748}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-info .gfield-admin-icons .gfield-sidebar-message-icon::after{background-color:#f2f3f5;border-color:#d2d5db}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-info .admin-hidden-markup .gform-icon--hidden,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-info .gfield-compact-icon--conditional,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-info .gfield-compact-icon--id,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-info label.gform-field-label,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-info legend.gform-field-label>span{background-color:#fff;border:1px solid #d2d5db;color:#585e6a}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-notice{border:1px solid #c3d9ff}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-notice .gfield-admin-icons .gfield-field-action,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-notice .gfield-admin-icons .gfield-icon,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-notice .gfield-admin-icons .gform-icon--drag-indicator{color:#044ad3}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-notice .gfield-admin-icons .gfield-sidebar-message-icon{border-inline-start-color:#c3d9ff}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-notice .admin-hidden-markup .gform-icon--hidden,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-notice .gfield-compact-icon--conditional,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-notice .gfield-compact-icon--id,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-notice label.gform-field-label,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-notice legend.gform-field-label>span{background-color:#f4f8ff;border:1px solid #c3d9ff;color:#044ad3}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-success{border:1px solid #31c48d}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-success .gfield-admin-icons .gfield-field-action,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-success .gfield-admin-icons .gfield-icon,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-success .gfield-admin-icons .gform-icon--drag-indicator{color:#2f833d}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-success .gfield-admin-icons .gfield-sidebar-message-icon{border-inline-start-color:#31c48d}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-success .admin-hidden-markup .gform-icon--hidden,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-success .gfield-compact-icon--conditional,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-success .gfield-compact-icon--id,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-success label.gform-field-label,.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer).gfield-has-sidebar-message--type-success legend.gform-field-label>span{background-color:#fbfffb;border:1px solid #31c48d;color:#2f833d}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer) .gfield-admin-icons .gfield-icon{border:unset;margin-inline-end:1.25rem;margin-inline-start:.5rem;width:0}.gforms_edit_form .gform-compact-view #gform_fields .gfield.gfield-has-sidebar-message:not(.ui-draggable-dragging):not(.spacer) .gfield-admin-icons .gfield-sidebar-message-icon{border-inline-start-style:solid;display:flex;height:unset;width:3rem}.gforms_edit_form .gform-compact-view #gform_fields fieldset.gfield:where(:not(#field_submit)){padding-block-end:0.5rem}.gforms_edit_form .gform-compact-view #gform_fields fieldset.gfield:where(:not(#field_submit)) legend{float:left;inset-block-start:0;inset-inline-start:1px}.gforms_edit_form .gform-compact-view #gform_fields fieldset.gfield:where(:not(#field_submit)) .gfield-admin-icons{margin-block-end:0;margin-block-start:1px}.gforms_edit_form .gform-compact-view #gform_fields .gfield#field_submit:hover .gfield-admin-icons{display:none}.gforms_edit_form .gform-compact-view #gform_fields .dropzone__loader{padding:0}.gforms_edit_form .gform-compact-view #gform_fields .dropzone__loader .dropzone__loader-label{display:none}.gforms_edit_form .gform-compact-view #gform_fields .dropzone__loader .dropzone__loader-content{block-size:2.188rem}.gforms_edit_form .gform-compact-view #gform_fields .captcha_message{display:none}.gforms_edit_form .gform_wrapper.gform_editor .gform-dropdown .gform-button.gform-button--size-height-s{padding:.375rem .3rem .375rem .2rem}.gforms_edit_form .gform_wrapper.gform_editor .gform-dropdown--action .gform-dropdown__trigger-icon{margin-inline-end:8px}.gforms_edit_form .gform-compact-view .gform_wrapper.gform_editor{padding-block-end:0;padding-block-start:16px}.gform-compact-view #gform_last_page_settings,.gform-compact-view #gform_pagination{position:relative;text-align:center}.gform-compact-view #gform_last_page_settings .gf-pagebreak,.gform-compact-view #gform_pagination .gf-pagebreak{background-color:#f6f9fc;block-size:25px;border:1px solid #d5d7e9;border-radius:3px;box-shadow:10px 1px 0 0 #fff,-10px 1px 0 0 #fff;color:#242748;display:inline-block;font-size:.688rem;font-weight:600;letter-spacing:normal;line-height:1.563rem;margin:0 auto .75rem auto;padding-block:0;padding-inline:0.625rem;position:relative;text-transform:none;z-index:1}.gform-compact-view #gform_last_page_settings .gf-pagebreak::after,.gform-compact-view #gform_last_page_settings .gf-pagebreak::before,.gform-compact-view #gform_pagination .gf-pagebreak::after,.gform-compact-view #gform_pagination .gf-pagebreak::before{display:none}.gform-compact-view #gform_last_page_settings .gf-pagebreak::after,.gform-compact-view #gform_pagination .gf-pagebreak::after{display:none}.gform-compact-view #gform_last_page_settings::before,.gform-compact-view #gform_pagination::before{background:repeating-linear-gradient(to left,#c3c5db 0,#c3c5db 9px,transparent 9px,transparent 13px);content:"";display:block;flex-grow:1;height:2px;inset-block-start:0.75rem;position:absolute;width:100%}.gform-compact-view #field_submit{margin:0;padding:0}.gform-compact-view #field_submit input[type=image]{max-height:38px}.gform-compact-view .gfield_captcha_container{display:none}.rtl .gform-compact-view #gform_fields.left_label fieldset.gfield,.rtl .gform-compact-view #gform_fields.right_label fieldset.gfield{padding-block-start:0.625rem}.rtl .gform-compact-view .gfield-admin-icons .gfield-field-action.gfield-icon{border-left:none}html[dir=rtl] body.wp-admin .gform-compact-view #gform_fields.top_label .gfield_label{margin-block-start:0}#alert-dialog-confirmation .gform-dialog__title.gform-dialog__title--has-icon,#alert-dialog-message .gform-dialog__title.gform-dialog__title--has-icon,#dialog-embed-form-unsaved-changes .gform-dialog__title.gform-dialog__title--has-icon{line-height:29px;padding-block-start:1px;padding-inline-start:30px}.gform-admin .gform-c-warning-text{color:#a16938}.gform-admin .gform-c-error-text{color:#dd301d}.gform-admin .gform-c-red{color:#dd301d}.gform-admin .gform-c-hunter{color:#276a52}.gform-admin .gform-c-orange{color:#f15a2b}.gform-admin .gform-c-blue-ribbon{color:#175cff}.gform-admin .gform-p-16{padding:1rem}.gform-admin .gform-visually-hidden{border:0;clip:rect(0,0,0,0);height:1px;margin:-1px;overflow:hidden;word-wrap:normal!important;padding:0;position:absolute;white-space:nowrap;width:1px}