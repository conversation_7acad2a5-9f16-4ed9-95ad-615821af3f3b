.gform-theme--api,.gform-theme--framework{--gf-radius:3px;--gf-radius-max-sm:2px;--gf-radius-max-md:3px;--gf-radius-max-lg:8px}.gform-theme--api,.gform-theme--framework{--gf-color-primary:#204ce5;--gf-color-primary-rgb:45,127,251;--gf-color-primary-contrast:#fff;--gf-color-primary-contrast-rgb:255,255,255;--gf-color-primary-darker:#044ad3;--gf-color-primary-lighter:#044ad3;--gf-color-secondary:#fff;--gf-color-secondary-rgb:255,255,255;--gf-color-secondary-contrast:#112337;--gf-color-secondary-contrast-rgb:17,35,55;--gf-color-secondary-darker:#f2f3f5;--gf-color-secondary-lighter:#f2f3f5;--gf-color-out-ctrl-dark:#585e6a;--gf-color-out-ctrl-dark-rgb:88,94,106;--gf-color-out-ctrl-dark-darker:#112337;--gf-color-out-ctrl-dark-lighter:#686e77;--gf-color-out-ctrl-light:#e5e7eb;--gf-color-out-ctrl-light-rgb:229,231,235;--gf-color-out-ctrl-light-darker:#d2d5db;--gf-color-out-ctrl-light-lighter:#f2f3f5;--gf-color-in-ctrl:#fff;--gf-color-in-ctrl-rgb:255,255,255;--gf-color-in-ctrl-contrast:#112337;--gf-color-in-ctrl-contrast-rgb:17,35,55;--gf-color-in-ctrl-darker:#f2f3f5;--gf-color-in-ctrl-lighter:#f2f3f5;--gf-color-in-ctrl-primary:var(--gf-color-primary);--gf-color-in-ctrl-primary-rgb:var(--gf-color-primary-rgb);--gf-color-in-ctrl-primary-contrast:var(--gf-color-primary-contrast);--gf-color-in-ctrl-primary-contrast-rgb:var(--gf-color-primary-contrast-rgb);--gf-color-in-ctrl-primary-darker:var(--gf-color-primary-darker);--gf-color-in-ctrl-primary-lighter:var(--gf-color-primary-lighter);--gf-color-in-ctrl-dark:#585e6a;--gf-color-in-ctrl-dark-rgb:88,94,106;--gf-color-in-ctrl-dark-darker:#112337;--gf-color-in-ctrl-dark-lighter:#686e77;--gf-color-in-ctrl-light:#e5e7eb;--gf-color-in-ctrl-light-rgb:229,231,235;--gf-color-in-ctrl-light-darker:#d2d5db;--gf-color-in-ctrl-light-lighter:#f2f3f5;--gf-color-danger:#c02b0a;--gf-color-danger-rgb:192,43,10;--gf-color-danger-contrast:#fff;--gf-color-danger-contrast-rgb:255,255,255;--gf-color-success:#399f4b;--gf-color-success-rgb:57,159,75;--gf-color-success-contrast:#fff;--gf-color-success-contrast-rgb:255,255,255}.gform-theme--api,.gform-theme--framework{--gf-icon-font-family:"gform-icons-orbital";--gf-icon-font-size:20px;--gf-icon-ctrl-checkbox:"\e900";--gf-icon-ctrl-select-down:"\e901";--gf-icon-ctrl-select-up:"\e902";--gf-icon-ctrl-select:url("data:image/svg+xml,%3Csvg width='10' height='6' viewBox='0 0 10 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M0.292893 0.292893C0.683417 -0.097631 1.31658 -0.097631 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893C10.0976 0.683417 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.31658 6.09763 4.68342 6.09763 4.29289 5.70711L0.292893 1.70711C-0.0976311 1.31658 -0.0976311 0.683418 0.292893 0.292893Z' fill='%23686E77'/%3E%3C/svg%3E");--gf-icon-ctrl-search:url("data:image/svg+xml,%3Csvg width='640' height='640' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M256 128c-70.692 0-128 57.308-128 128 0 70.691 57.308 128 128 128 70.691 0 128-57.309 128-128 0-70.692-57.309-128-128-128zM64 256c0-106.039 85.961-192 192-192s192 85.961 192 192c0 41.466-13.146 79.863-35.498 111.248l154.125 154.125c12.496 12.496 12.496 32.758 0 45.254s-32.758 12.496-45.254 0L367.248 412.502C335.862 434.854 297.467 448 256 448c-106.039 0-192-85.962-192-192z' fill='%23686E77'/%3E%3C/svg%3E");--gf-icon-ctrl-cancel:"\e918";--gf-icon-ctrl-number:url("data:image/svg+xml,%3Csvg width='8' height='14' viewBox='0 0 8 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M4 0C4.26522 5.96046e-08 4.51957 0.105357 4.70711 0.292893L7.70711 3.29289C8.09763 3.68342 8.09763 4.31658 7.70711 4.70711C7.31658 5.09763 6.68342 5.09763 6.29289 4.70711L4 2.41421L1.70711 4.70711C1.31658 5.09763 0.683417 5.09763 0.292893 4.70711C-0.0976311 4.31658 -0.097631 3.68342 0.292893 3.29289L3.29289 0.292893C3.48043 0.105357 3.73478 0 4 0ZM0.292893 9.29289C0.683417 8.90237 1.31658 8.90237 1.70711 9.29289L4 11.5858L6.29289 9.29289C6.68342 8.90237 7.31658 8.90237 7.70711 9.29289C8.09763 9.68342 8.09763 10.3166 7.70711 10.7071L4.70711 13.7071C4.31658 14.0976 3.68342 14.0976 3.29289 13.7071L0.292893 10.7071C-0.0976311 10.3166 -0.0976311 9.68342 0.292893 9.29289Z' fill='%23686E77'/%3E%3C/svg%3E");--gf-icon-ctrl-pwd-hidden:"\e90a";--gf-icon-ctrl-pwd-visible:"\e909";--gf-icon-ctrl-list-item-add:"\e90f";--gf-icon-ctrl-list-item-remove:"\e90e";--gf-icon-ctrl-save-continue:"\e910";--gf-icon-ctrl-pg-numbers-complete:"\e90b";--gf-icon-ctrl-file:"\e911";--gf-icon-ctrl-file-completed:"\e90c";--gf-icon-ctrl-file-cancel:"\e904";--gf-icon-ctrl-file-remove:"\e919";--gf-icon-ctrl-datepicker:"\e91a";--gf-icon-ctrl-datepicker-left:"\e91b";--gf-icon-ctrl-datepicker-right:"\e91c";--gf-icon-ctrl-img-choice-placeholder:"\e922";--gf-icon-tooltip-error:"\e906"}.gform-theme--api,.gform-theme--framework{--gf-padding-x:12px;--gf-padding-y:12px;--gf-label-space-primary:8px;--gf-label-choice-field-space-primary:12px;--gf-label-space-x-secondary:12px;--gf-label-space-y-sm-secondary:-1px;--gf-label-space-y-md-secondary:0;--gf-label-space-y-lg-secondary:1px;--gf-label-space-y-xl-secondary:4px;--gf-label-space-y-secondary:var(--gf-label-space-y-md-secondary);--gf-label-space-tertiary:8px;--gf-desc-space:8px;--gf-desc-choice-field-space:12px}.gform-theme--api,.gform-theme--framework{--gf-transition-duration:0.15s;--gf-transition-ctrl:var(--gf-transition-duration)}.gform-theme--api,.gform-theme--framework{--gf-font-family-base:initial;--gf-font-style-base:normal;--gf-font-family-primary:var(--gf-font-family-base);--gf-font-size-primary:14px;--gf-font-style-primary:var(--gf-font-style-base);--gf-font-weight-primary:400;--gf-letter-spacing-primary:0;--gf-line-height-primary:1.5;--gf-font-family-secondary:var(--gf-font-family-base);--gf-font-size-secondary:14px;--gf-font-style-secondary:var(--gf-font-style-base);--gf-font-weight-secondary:500;--gf-letter-spacing-secondary:0;--gf-line-height-secondary:1.43;--gf-font-family-tertiary:var(--gf-font-family-base);--gf-font-size-tertiary:14px;--gf-font-style-tertiary:var(--gf-font-style-base);--gf-font-weight-tertiary:400;--gf-letter-spacing-tertiary:0;--gf-line-height-tertiary:1.43}.gform-theme--api,.gform-theme--framework{--gf-ctrl-bg-color:var(--gf-color-in-ctrl);--gf-ctrl-bg-color-hover:var(--gf-ctrl-bg-color);--gf-ctrl-bg-color-focus:var(--gf-ctrl-bg-color);--gf-ctrl-bg-color-disabled:var(--gf-color-in-ctrl-light-lighter);--gf-ctrl-bg-color-error:var(--gf-ctrl-bg-color);--gf-ctrl-border-color:var(--gf-color-in-ctrl-dark-lighter);--gf-ctrl-border-color-hover:var(--gf-ctrl-border-color);--gf-ctrl-border-color-focus:var(--gf-color-primary);--gf-ctrl-border-color-disabled:var(--gf-color-in-ctrl-light-darker);--gf-ctrl-border-color-error:var(--gf-color-danger);--gf-ctrl-border-style:solid;--gf-ctrl-border-width:1px;--gf-ctrl-radius:var(--gf-radius);--gf-ctrl-radius-max-sm:min(var(--gf-ctrl-radius), var(--gf-radius-max-sm));--gf-ctrl-radius-max-md:min(var(--gf-ctrl-radius), var(--gf-radius-max-md));--gf-ctrl-radius-max-lg:min(var(--gf-ctrl-radius), var(--gf-radius-max-lg));--gf-ctrl-outline-color:transparent;--gf-ctrl-outline-color-focus:rgba(var(--gf-color-primary-rgb), 0.65);--gf-ctrl-outline-offset:1px;--gf-ctrl-outline-style:solid;--gf-ctrl-outline-width:0;--gf-ctrl-outline-width-focus:3px;--gf-ctrl-color:var(--gf-color-in-ctrl-contrast);--gf-ctrl-color-hover:var(--gf-ctrl-color);--gf-ctrl-color-focus:var(--gf-ctrl-color);--gf-ctrl-color-disabled:rgba(var(--gf-color-in-ctrl-contrast-rgb), 0.6);--gf-ctrl-color-error:var(--gf-ctrl-color);--gf-ctrl-icon-color:var(--gf-color-in-ctrl-dark-lighter);--gf-ctrl-icon-color-hover:var(--gf-color-in-ctrl-dark-darker);--gf-ctrl-icon-color-focus:var(--gf-ctrl-icon-color-hover);--gf-ctrl-icon-color-disabled:var(--gf-ctrl-icon-color);--gf-ctrl-shadow:0 1px 4px rgba(18, 25, 97, 0.0779552);--gf-ctrl-accent-color:var(--gf-color-in-ctrl-primary);--gf-ctrl-appearance:none;--gf-ctrl-size-sm:35px;--gf-ctrl-size-md:38px;--gf-ctrl-size-lg:47px;--gf-ctrl-size-xl:54px;--gf-ctrl-size:var(--gf-ctrl-size-md);--gf-ctrl-padding-x:var(--gf-padding-x);--gf-ctrl-padding-y:0;--gf-ctrl-transition:var(--gf-transition-ctrl);--gf-ctrl-font-family:var(--gf-font-family-primary);--gf-ctrl-font-size:var(--gf-font-size-primary);--gf-ctrl-font-style:var(--gf-font-style-base);--gf-ctrl-font-weight:var(--gf-font-weight-primary);--gf-ctrl-letter-spacing:var(--gf-letter-spacing-primary);--gf-ctrl-line-height:var(--gf-ctrl-size);--gf-ctrl-placeholder-color:rgba(var(--gf-color-in-ctrl-contrast-rgb), 0.7);--gf-ctrl-placeholder-font-family:var(--gf-ctrl-font-family);--gf-ctrl-placeholder-font-size:var(--gf-ctrl-font-size);--gf-ctrl-placeholder-font-style:var(--gf-ctrl-font-style);--gf-ctrl-placeholder-font-weight:var(--gf-ctrl-font-weight);--gf-ctrl-placeholder-letter-spacing:var(--gf-ctrl-letter-spacing);--gf-ctrl-placeholder-opacity:1}.gform-theme--api,.gform-theme--framework{--gf-ctrl-readonly-color:var(--gf-ctrl-color);--gf-ctrl-readonly-font-family:var(--gf-ctrl-font-family);--gf-ctrl-readonly-font-size:var(--gf-ctrl-font-size);--gf-ctrl-readonly-font-style:var(--gf-ctrl-font-style);--gf-ctrl-readonly-font-weight:500;--gf-ctrl-readonly-letter-spacing:var(--gf-ctrl-letter-spacing);--gf-ctrl-readonly-line-height:1}.gform-theme--api,.gform-theme--framework{--gf-ctrl-btn-radius:var(--gf-radius);--gf-ctrl-btn-shadow:0 1px 4px rgba(18, 25, 97, 0.0779552);--gf-ctrl-btn-shadow-hover:var(--gf-ctrl-btn-shadow);--gf-ctrl-btn-shadow-focus:var(--gf-ctrl-btn-shadow);--gf-ctrl-btn-shadow-disabled:var(--gf-ctrl-btn-shadow);--gf-ctrl-btn-opacity:1;--gf-ctrl-btn-opacity-disabled:0.5;--gf-ctrl-btn-size-xs:30px;--gf-ctrl-btn-size-sm:var(--gf-ctrl-size-sm);--gf-ctrl-btn-size-md:var(--gf-ctrl-size-md);--gf-ctrl-btn-size-lg:var(--gf-ctrl-size-lg);--gf-ctrl-btn-size-xl:var(--gf-ctrl-size-xl);--gf-ctrl-btn-size:var(--gf-ctrl-btn-size-md);--gf-ctrl-btn-padding-x-xs:8px;--gf-ctrl-btn-padding-x-sm:12px;--gf-ctrl-btn-padding-x-md:16px;--gf-ctrl-btn-padding-x-lg:20px;--gf-ctrl-btn-padding-x-xl:24px;--gf-ctrl-btn-padding-x:var(--gf-ctrl-btn-padding-x-md);--gf-ctrl-btn-padding-y:0;--gf-ctrl-btn-font-family:var(--gf-font-family-base);--gf-ctrl-btn-font-size-xs:12px;--gf-ctrl-btn-font-size-sm:14px;--gf-ctrl-btn-font-size-md:14px;--gf-ctrl-btn-font-size-lg:16px;--gf-ctrl-btn-font-size-xl:16px;--gf-ctrl-btn-font-size:var(--gf-ctrl-btn-font-size-md);--gf-ctrl-btn-font-style:var(--gf-font-style-base);--gf-ctrl-btn-font-weight:500;--gf-ctrl-btn-letter-spacing:var(--gf-letter-spacing-primary);--gf-ctrl-btn-line-height:1;--gf-ctrl-btn-text-decoration:none;--gf-ctrl-btn-text-transform:none;--gf-ctrl-btn-icon:none;--gf-ctrl-btn-icon-font-size:var(--gf-icon-font-size);--gf-ctrl-btn-icon-gap:6px;--gf-ctrl-btn-icon-transition:var(--gf-ctrl-transition);--gf-ctrl-btn-bg-color-primary:var(--gf-color-primary);--gf-ctrl-btn-bg-color-hover-primary:var(--gf-color-primary-darker);--gf-ctrl-btn-bg-color-focus-primary:var(--gf-ctrl-btn-bg-color-primary);--gf-ctrl-btn-bg-color-disabled-primary:var(--gf-ctrl-btn-bg-color-primary);--gf-ctrl-btn-border-color-primary:transparent;--gf-ctrl-btn-border-color-hover-primary:transparent;--gf-ctrl-btn-border-color-focus-primary:var(--gf-ctrl-btn-bg-color-hover-primary);--gf-ctrl-btn-border-color-disabled-primary:transparent;--gf-ctrl-btn-border-style-primary:solid;--gf-ctrl-btn-border-width-primary:1px;--gf-ctrl-btn-color-primary:var(--gf-color-primary-contrast);--gf-ctrl-btn-color-hover-primary:var(--gf-ctrl-btn-color-primary);--gf-ctrl-btn-color-focus-primary:var(--gf-ctrl-btn-color-primary);--gf-ctrl-btn-color-disabled-primary:var(--gf-ctrl-btn-color-primary);--gf-ctrl-btn-icon-color-primary:var(--gf-ctrl-btn-color-primary);--gf-ctrl-btn-icon-color-hover-primary:var(--gf-ctrl-btn-icon-color-primary);--gf-ctrl-btn-icon-color-focus-primary:var(--gf-ctrl-btn-icon-color-primary);--gf-ctrl-btn-icon-color-disabled-primary:var(--gf-ctrl-btn-icon-color-primary);--gf-ctrl-btn-bg-color-secondary:var(--gf-color-secondary);--gf-ctrl-btn-bg-color-hover-secondary:var(--gf-color-secondary-darker);--gf-ctrl-btn-bg-color-focus-secondary:var(--gf-ctrl-btn-bg-color-secondary);--gf-ctrl-btn-bg-color-disabled-secondary:var(--gf-ctrl-btn-bg-color-secondary);--gf-ctrl-btn-border-color-secondary:var(--gf-color-in-ctrl-light-darker);--gf-ctrl-btn-border-color-hover-secondary:var(--gf-ctrl-btn-border-color-secondary);--gf-ctrl-btn-border-color-focus-secondary:var(--gf-ctrl-btn-bg-color-hover-primary);--gf-ctrl-btn-border-color-disabled-secondary:var(--gf-ctrl-btn-border-color-secondary);--gf-ctrl-btn-border-style-secondary:solid;--gf-ctrl-btn-border-width-secondary:1px;--gf-ctrl-btn-color-secondary:var(--gf-color-secondary-contrast);--gf-ctrl-btn-color-hover-secondary:var(--gf-ctrl-btn-color-secondary);--gf-ctrl-btn-color-focus-secondary:var(--gf-ctrl-btn-color-secondary);--gf-ctrl-btn-color-disabled-secondary:var(--gf-ctrl-btn-color-secondary);--gf-ctrl-btn-icon-color-secondary:var(--gf-ctrl-icon-color);--gf-ctrl-btn-icon-color-hover-secondary:var(--gf-ctrl-btn-icon-color-secondary);--gf-ctrl-btn-icon-color-focus-secondary:var(--gf-ctrl-btn-icon-color-secondary);--gf-ctrl-btn-icon-color-disabled-secondary:var(--gf-ctrl-btn-icon-color-secondary);--gf-ctrl-btn-bg-color-ctrl:var(--gf-color-in-ctrl-primary);--gf-ctrl-btn-bg-color-hover-ctrl:var(--gf-color-in-ctrl-primary-darker);--gf-ctrl-btn-bg-color-focus-ctrl:var(--gf-ctrl-btn-bg-color-ctrl);--gf-ctrl-btn-bg-color-disabled-ctrl:var(--gf-ctrl-btn-bg-color-ctrl);--gf-ctrl-btn-border-color-ctrl:transparent;--gf-ctrl-btn-border-color-hover-ctrl:transparent;--gf-ctrl-btn-border-color-focus-ctrl:var(--gf-ctrl-btn-bg-color-hover-ctrl);--gf-ctrl-btn-border-color-disabled-ctrl:transparent;--gf-ctrl-btn-border-style-ctrl:solid;--gf-ctrl-btn-border-width-ctrl:1px;--gf-ctrl-btn-color-ctrl:var(--gf-color-in-ctrl-primary-contrast);--gf-ctrl-btn-color-hover-ctrl:var(--gf-ctrl-btn-color-ctrl);--gf-ctrl-btn-color-focus-ctrl:var(--gf-ctrl-btn-color-ctrl);--gf-ctrl-btn-color-disabled-ctrl:var(--gf-ctrl-btn-color-ctrl);--gf-ctrl-btn-icon-color-ctrl:var(--gf-ctrl-btn-color-ctrl);--gf-ctrl-btn-icon-color-hover-ctrl:var(--gf-ctrl-btn-icon-color-ctrl);--gf-ctrl-btn-icon-color-focus-ctrl:var(--gf-ctrl-btn-icon-color-ctrl);--gf-ctrl-btn-icon-color-disabled-ctrl:var(--gf-ctrl-btn-icon-color-ctrl);--gf-ctrl-btn-bg-color-simple:transparent;--gf-ctrl-btn-bg-color-hover-simple:var(--gf-ctrl-btn-bg-color-simple);--gf-ctrl-btn-bg-color-focus-simple:var(--gf-ctrl-btn-bg-color-simple);--gf-ctrl-btn-bg-color-disabled-simple:var(--gf-ctrl-btn-bg-color-simple);--gf-ctrl-btn-border-color-simple:transparent;--gf-ctrl-btn-border-color-hover-simple:var(--gf-ctrl-btn-border-color-simple);--gf-ctrl-btn-border-color-focus-simple:var(--gf-ctrl-border-color-focus);--gf-ctrl-btn-border-color-disabled-simple:var(--gf-ctrl-btn-border-color-simple);--gf-ctrl-btn-border-style-simple:solid;--gf-ctrl-btn-border-width-simple:1px;--gf-ctrl-btn-color-simple:rgba(var(--gf-color-out-ctrl-dark-rgb), 0.65);--gf-ctrl-btn-color-hover-simple:var(--gf-color-out-ctrl-dark);--gf-ctrl-btn-color-focus-simple:var(--gf-ctrl-btn-color-hover-simple);--gf-ctrl-btn-color-disabled-simple:var(--gf-ctrl-btn-color-simple);--gf-ctrl-btn-shadow-simple:none;--gf-ctrl-btn-shadow-hover-simple:var(--gf-ctrl-btn-shadow-simple);--gf-ctrl-btn-shadow-focus-simple:var(--gf-ctrl-btn-shadow-simple);--gf-ctrl-btn-shadow-disabled-simple:var(--gf-ctrl-btn-shadow-simple);--gf-ctrl-btn-size-simple:24px;--gf-ctrl-btn-icon-color-simple:var(--gf-ctrl-btn-color-simple);--gf-ctrl-btn-icon-color-hover-simple:var(--gf-ctrl-btn-color-hover-simple);--gf-ctrl-btn-icon-color-focus-simple:var(--gf-ctrl-btn-color-focus-simple);--gf-ctrl-btn-icon-color-disabled-simple:var(--gf-ctrl-btn-color-disabled-simple)}.gform-theme--api,.gform-theme--framework{--gf-ctrl-choice-check-color:var(--gf-color-in-ctrl-primary);--gf-ctrl-choice-check-color-disabled:rgba(var(--gf-color-in-ctrl-contrast-rgb), 0.2);--gf-ctrl-choice-size-sm:18px;--gf-ctrl-choice-size-md:20px;--gf-ctrl-choice-size-lg:22px;--gf-ctrl-choice-size-xl:28px;--gf-ctrl-choice-size:var(--gf-ctrl-choice-size-md);--gf-ctrl-checkbox-check-radius:var(--gf-ctrl-radius-max-sm);--gf-ctrl-checkbox-check-size-sm:12px;--gf-ctrl-checkbox-check-size-md:initial;--gf-ctrl-checkbox-check-size-lg:15px;--gf-ctrl-checkbox-check-size-xl:19px;--gf-ctrl-checkbox-check-size:var(--gf-ctrl-checkbox-check-size-md);--gf-ctrl-radio-check-radius:50%;--gf-ctrl-radio-check-content:"";--gf-ctrl-radio-check-size-sm:6px;--gf-ctrl-radio-check-size-md:7px;--gf-ctrl-radio-check-size-lg:8px;--gf-ctrl-radio-check-size-xl:10px;--gf-ctrl-radio-check-size:var(--gf-ctrl-radio-check-size-md)}.gform-theme--api,.gform-theme--framework{--gf-ctrl-date-picker-bg-color:var(--gf-ctrl-bg-color);--gf-ctrl-date-picker-shadow:0 0 1px rgba(18, 25, 97, 0.24),0 24px 24px rgba(18, 25, 97, 0.03),0 2px 2px rgba(18, 25, 97, 0.03),0 4px 4px rgba(18, 25, 97, 0.03),0 8px 8px rgba(18, 25, 97, 0.03),0 16px 16px rgba(18, 25, 97, 0.03);--gf-ctrl-date-picker-padding-y:16px 12px;--gf-ctrl-date-picker-padding-y-viewport-sm:16px;--gf-ctrl-date-picker-padding-x:12px;--gf-ctrl-date-picker-padding-x-viewport-sm:16px;--gf-ctrl-date-picker-margin-y-start:12px;--gf-ctrl-date-picker-radius:var(--gf-ctrl-radius-max-md);--gf-ctrl-date-picker-width:250px;--gf-ctrl-date-picker-width-viewport-sm:300px;--gf-ctrl-date-picker-header-icons-width:20px;--gf-ctrl-date-picker-header-icons-color:var(--gf-ctrl-icon-color);--gf-ctrl-date-picker-header-icons-color-hover:var(--gf-ctrl-icon-color-hover);--gf-ctrl-date-picker-header-icons-font-size:20px;--gf-ctrl-date-picker-title-color:var(--gf-color-secondary-contrast);--gf-ctrl-date-picker-title-font-size:12px;--gf-ctrl-date-picker-title-font-size-viewport-sm:14px;--gf-ctrl-date-picker-title-font-weight:500;--gf-ctrl-date-picker-title-gap:4px;--gf-ctrl-date-picker-title-gap-viewport-sm:8px;--gf-ctrl-date-picker-title-line-height:1.5;--gf-ctrl-date-picker-title-margin-x:4px;--gf-ctrl-date-picker-title-margin-x-viewport-sm:8px;--gf-ctrl-date-picker-dropdown-bg-img:var(--gf-icon-ctrl-select);--gf-ctrl-date-picker-dropdown-bg-position:var(--gf-ctrl-select-icon-position);--gf-ctrl-date-picker-dropdown-bg-size:var(--gf-ctrl-select-icon-size);--gf-ctrl-date-picker-dropdown-border-color:var(--gf-color-in-ctrl-light-darker);--gf-ctrl-date-picker-dropdown-border-style:var(--gf-ctrl-border-style);--gf-ctrl-date-picker-dropdown-border-width:var(--gf-ctrl-border-width);--gf-ctrl-date-picker-dropdown-shadow:0 1px 2px rgba(0, 0, 0, 0.05);--gf-ctrl-date-picker-dropdown-text-align:start;--gf-ctrl-date-picker-table-margin-y-start:16px;--gf-ctrl-date-picker-table-margin-y-end:0;--gf-ctrl-date-picker-head-cell-font-size:12px;--gf-ctrl-date-picker-head-cell-font-weight:600;--gf-ctrl-date-picker-head-cell-line-height:1.33;--gf-ctrl-date-picker-cell-padding:1px;--gf-ctrl-date-picker-cell-padding-y:6px;--gf-ctrl-date-picker-cell-padding-y-viewport-sm:var(--gf-ctrl-date-picker-cell-padding);--gf-ctrl-date-picker-cell-height:29px;--gf-ctrl-date-picker-cell-height-viewport-sm:40px;--gf-ctrl-date-picker-cell-font-size:14px;--gf-ctrl-date-picker-cell-font-weight:400;--gf-ctrl-date-picker-cell-line-height:1.43;--gf-ctrl-date-picker-cell-content-align-items:center;--gf-ctrl-date-picker-cell-content-bg-color-disabled:transparent;--gf-ctrl-date-picker-cell-content-bg-color-hover:#f4f8ff;--gf-ctrl-date-picker-cell-content-bg-color-selected:var(--gf-color-in-ctrl-primary);--gf-ctrl-date-picker-cell-content-border:var(--gf-ctrl-border-width) var(--gf-ctrl-border-style) var(--gf-color-in-ctrl-primary);--gf-ctrl-date-picker-cell-content-radius:var(--gf-ctrl-radius-max-md);--gf-ctrl-date-picker-cell-content-color:var(--gf-color-secondary-contrast);--gf-ctrl-date-picker-cell-content-color-disabled:#cfd3d9;--gf-ctrl-date-picker-cell-content-color-hover:var(--gf-ctrl-date-picker-cell-content-color);--gf-ctrl-date-picker-cell-content-color-selected:var(--gf-color-in-ctrl-primary-contrast);--gf-ctrl-date-picker-cell-content-width:27px;--gf-ctrl-date-picker-cell-content-width-viewport-sm:100%}.gform-theme--api,.gform-theme--framework{--gf-ctrl-desc-color:var(--gf-color-out-ctrl-dark);--gf-ctrl-desc-font-family:var(--gf-font-family-tertiary);--gf-ctrl-desc-font-size:var(--gf-font-size-tertiary);--gf-ctrl-desc-font-style:var(--gf-font-style-tertiary);--gf-ctrl-desc-font-weight:var(--gf-font-weight-tertiary);--gf-ctrl-desc-letter-spacing:var(--gf-letter-spacing-tertiary);--gf-ctrl-desc-line-height:var(--gf-line-height-tertiary);--gf-ctrl-desc-color-error:var(--gf-color-danger);--gf-ctrl-desc-font-family-error:var(--gf-ctrl-desc-font-family);--gf-ctrl-desc-font-size-error:var(--gf-ctrl-desc-font-size);--gf-ctrl-desc-font-style-error:var(--gf-ctrl-desc-font-style);--gf-ctrl-desc-font-weight-error:var(--gf-ctrl-desc-font-weight);--gf-ctrl-desc-letter-spacing-error:var(--gf-ctrl-desc-letter-spacing);--gf-ctrl-desc-line-height-error:var(--gf-ctrl-desc-line-height);--gf-ctrl-desc-border-color-consent:var(--gf-color-out-ctrl-light-darker);--gf-ctrl-desc-border-color-consent-focus:var(--gf-ctrl-border-color-focus);--gf-ctrl-desc-border-style-consent:solid;--gf-ctrl-desc-border-width-consent:1px;--gf-ctrl-desc-max-height-consent:456px}.gform-theme--api,.gform-theme--framework{--gf-ctrl-file-padding-x:0 var(--gf-ctrl-padding-x);--gf-ctrl-file-btn-bg-color:var(--gf-color-secondary-darker);--gf-ctrl-file-btn-bg-color-hover:var(--gf-color-secondary);--gf-ctrl-file-btn-bg-color-focus:var(--gf-ctrl-file-btn-bg-color);--gf-ctrl-file-btn-bg-color-disabled:var(--gf-ctrl-file-btn-bg-color);--gf-ctrl-file-btn-border-inline-end-width:1px;--gf-ctrl-file-btn-border-inline-end-style:solid;--gf-ctrl-file-btn-border-inline-end-color:var(--gf-ctrl-border-color);--gf-ctrl-file-btn-border-inline-end-color-hover:var(--gf-ctrl-file-btn-border-inline-end-color);--gf-ctrl-file-btn-border-inline-end-color-focus:var(--gf-ctrl-file-btn-border-inline-end-color);--gf-ctrl-file-btn-border-inline-end-color-disabled:var(--gf-ctrl-file-btn-border-inline-end-color);--gf-ctrl-file-btn-radius:var(--gf-ctrl-radius);--gf-ctrl-file-btn-color:rgba(var(--gf-color-secondary-contrast-rgb), 0.725);--gf-ctrl-file-btn-color-hover:var(--gf-ctrl-file-btn-color);--gf-ctrl-file-btn-color-focus:var(--gf-ctrl-file-btn-color);--gf-ctrl-file-btn-color-disabled:var(--gf-ctrl-file-btn-color);--gf-ctrl-file-btn-font-family:var(--gf-font-family-base);--gf-ctrl-file-btn-font-size:14px;--gf-ctrl-file-btn-font-style:var(--gf-font-style-base);--gf-ctrl-file-btn-font-weight:500;--gf-ctrl-file-btn-letter-spacing:var(--gf-letter-spacing-primary);--gf-ctrl-file-btn-line-height:1.43;--gf-ctrl-file-btn-text-decoration:none;--gf-ctrl-file-btn-text-transform:none;--gf-ctrl-file-btn-margin-x:0 12px;--gf-ctrl-file-btn-padding-x:12px;--gf-ctrl-file-btn-transition:var(--gf-ctrl-transition);--gf-ctrl-file-zone-border-style:dashed;--gf-ctrl-file-zone-radius:var(--gf-ctrl-radius-max-lg);--gf-ctrl-file-zone-color:rgba(var(--gf-color-in-ctrl-contrast-rgb), 0.725);--gf-ctrl-file-zone-height:auto;--gf-ctrl-file-zone-padding-x:40px;--gf-ctrl-file-zone-padding-y:40px;--gf-ctrl-file-zone-instructions-margin-y-end:12px;--gf-ctrl-file-zone-font-weight:500;--gf-ctrl-file-zone-line-height:1;--gf-ctrl-file-zone-icon-color:var(--gf-color-in-ctrl-primary);--gf-ctrl-file-zone-icon-font-size:36px;--gf-ctrl-file-zone-icon-margin-y-end:8px;--gf-ctrl-file-prog-ui-gap:12px;--gf-ctrl-file-prog-ui-size:var(--gf-icon-font-size);--gf-ctrl-file-prog-bar-bg-color:var(--gf-color-out-ctrl-light);--gf-ctrl-file-prog-bar-bg-color-loading:var(--gf-color-primary);--gf-ctrl-file-prog-bar-height:6px;--gf-ctrl-file-prog-bar-radius:var(--gf-radius);--gf-ctrl-file-prog-bar-transition:var(--gf-transition-ctrl);--gf-ctrl-file-prog-text-color:var(--gf-ctrl-desc-color);--gf-ctrl-file-prog-text-min-width:33px;--gf-ctrl-file-prog-text-font-size:12px;--gf-ctrl-file-prog-btn-inset-y-start:-2px;--gf-ctrl-file-prog-btn-inset-x-end:-2px;--gf-ctrl-file-prog-btn-position:absolute;--gf-ctrl-file-prog-btn-font-size-cancel:0;--gf-ctrl-file-prog-btn-icon-size:var(--gf-icon-font-size);--gf-ctrl-file-prog-btn-icon-color-complete:var(--gf-color-success);--gf-ctrl-file-prev-area-gap:16px;--gf-ctrl-file-prev-area-margin-y-start:16px;--gf-ctrl-file-prev-font-family:var(--gf-font-family-secondary);--gf-ctrl-file-prev-font-size:var(--gf-font-size-secondary);--gf-ctrl-file-prev-font-style:var(--gf-font-style-secondary);--gf-ctrl-file-prev-font-weight:var(--gf-font-weight-secondary);--gf-ctrl-file-prev-letter-spacing:var(--gf-letter-spacing-secondary);--gf-ctrl-file-prev-line-height:1;--gf-ctrl-file-prev-gap:4px;--gf-ctrl-file-prev-name-color:var(--gf-ctrl-label-color-primary);--gf-ctrl-file-prev-name-line-height:var(--gf-line-height-secondary);--gf-ctrl-file-prev-name-overflow:hidden;--gf-ctrl-file-prev-name-padding-x-end:calc(var(--gf-ctrl-file-prog-btn-icon-size) + var(--gf-ctrl-file-prog-text-min-width) + calc(var(--gf-ctrl-file-prog-ui-gap) * 2));--gf-ctrl-file-prev-name-text-overflow:ellipsis;--gf-ctrl-file-prev-name-white-space:nowrap;--gf-ctrl-file-prev-size-color:var(--gf-ctrl-desc-color)}.gform-theme--api,.gform-theme--framework{--gf-ctrl-label-color-primary:var(--gf-color-out-ctrl-dark-darker);--gf-ctrl-label-font-family-primary:var(--gf-font-family-secondary);--gf-ctrl-label-font-size-primary:var(--gf-font-size-secondary);--gf-ctrl-label-font-style-primary:var(--gf-font-style-secondary);--gf-ctrl-label-font-weight-primary:var(--gf-font-weight-secondary);--gf-ctrl-label-letter-spacing-primary:var(--gf-letter-spacing-secondary);--gf-ctrl-label-line-height-primary:var(--gf-line-height-secondary);--gf-ctrl-label-color-secondary:var(--gf-color-out-ctrl-dark-darker);--gf-ctrl-label-font-family-secondary:var(--gf-font-family-secondary);--gf-ctrl-label-font-size-secondary:var(--gf-font-size-secondary);--gf-ctrl-label-font-style-secondary:var(--gf-font-style-secondary);--gf-ctrl-label-font-weight-secondary:400;--gf-ctrl-label-letter-spacing-secondary:var(--gf-letter-spacing-secondary);--gf-ctrl-label-line-height-secondary:var(--gf-line-height-secondary);--gf-ctrl-label-color-tertiary:var(--gf-color-out-ctrl-dark);--gf-ctrl-label-font-family-tertiary:var(--gf-font-family-tertiary);--gf-ctrl-label-font-size-tertiary:var(--gf-font-size-tertiary);--gf-ctrl-label-font-style-tertiary:var(--gf-font-style-tertiary);--gf-ctrl-label-font-weight-tertiary:var(--gf-font-weight-tertiary);--gf-ctrl-label-letter-spacing-tertiary:var(--gf-letter-spacing-tertiary);--gf-ctrl-label-line-height-tertiary:var(--gf-line-height-tertiary);--gf-ctrl-label-color-quaternary:var(--gf-color-out-ctrl-dark);--gf-ctrl-label-font-family-quaternary:var(--gf-font-family-tertiary);--gf-ctrl-label-font-size-quaternary:var(--gf-font-size-secondary);--gf-ctrl-label-font-style-quaternary:var(--gf-font-style-tertiary);--gf-ctrl-label-font-weight-quaternary:var(--gf-font-weight-secondary);--gf-ctrl-label-letter-spacing-quaternary:var(--gf-letter-spacing-tertiary);--gf-ctrl-label-line-height-quaternary:var(--gf-line-height-tertiary);--gf-ctrl-label-color-req:var(--gf-color-danger);--gf-ctrl-label-font-family-req:var(--gf-ctrl-label-font-family-primary);--gf-ctrl-label-font-size-req:12px;--gf-ctrl-label-font-style-req:var(--gf-ctrl-label-font-style-primary);--gf-ctrl-label-font-weight-req:var(--gf-ctrl-label-font-weight-primary);--gf-ctrl-label-letter-spacing-req:var(--gf-ctrl-label-letter-spacing-primary);--gf-ctrl-label-line-height-req:var(--gf-ctrl-label-line-height-primary)}.gform-theme--api,.gform-theme--framework{--gf-ctrl-number-spin-btn-appearance:var(--gf-ctrl-appearance);--gf-ctrl-number-spin-btn-bg-position:center center;--gf-ctrl-number-spin-btn-bg-size:8px 14px;--gf-ctrl-number-spin-btn-width:8px;--gf-ctrl-number-spin-btn-opacity:1}.gform-theme--api,.gform-theme--framework{--gf-ctrl-select-icon:var(--gf-icon-ctrl-select);--gf-ctrl-select-icon-hover:var(--gf-ctrl-select-icon);--gf-ctrl-select-icon-focus:var(--gf-ctrl-select-icon);--gf-ctrl-select-icon-disabled:var(--gf-ctrl-select-icon);--gf-ctrl-select-icon-position:calc(100% - var(--gf-ctrl-padding-x)) center;--gf-ctrl-select-icon-size:10px;--gf-ctrl-select-ms-expand:none;--gf-ctrl-select-padding-x:var(--gf-ctrl-padding-x) calc(var(--gf-ctrl-select-search-icon-size) + var(--gf-ctrl-padding-x));--gf-ctrl-multiselect-height:130px;--gf-ctrl-multiselect-radius:var(--gf-ctrl-radius-max-lg);--gf-ctrl-multiselect-line-height:1.5;--gf-ctrl-multiselect-padding-y:var(--gf-padding-y);--gf-ctrl-select-dropdown-border-color:transparent;--gf-ctrl-select-dropdown-radius:var(--gf-ctrl-radius-max-md);--gf-ctrl-select-dropdown-shadow:0 0 1px rgba(18, 25, 97, 0.24),0 24px 24px rgba(18, 25, 97, 0.03),0 2px 2px rgba(18, 25, 97, 0.03),0 4px 4px rgba(18, 25, 97, 0.03),0 8px 8px rgba(18, 25, 97, 0.03),0 16px 16px rgba(18, 25, 97, 0.03);--gf-ctrl-select-dropdown-option-bg-color-hover:var(--gf-color-in-ctrl-light-lighter);--gf-ctrl-select-dropdown-option-shadow-hover:inset 3px 0 0 var(--gf-color-in-ctrl-primary);--gf-ctrl-select-search-icon-size:var(--gf-icon-font-size);--gf-ctrl-select-search-icon-position:var(--gf-ctrl-padding-x) center;--gf-ctrl-select-search-padding-x:calc(var(--gf-ctrl-select-search-icon-size) + var(--gf-ctrl-padding-x) + 8px) var(--gf-ctrl-padding-x);--gf-ctrl-multiselect-close-icon-size:var(--gf-icon-font-size);--gf-ctrl-multiselect-close-icon-inset-y-start:calc(50% - (var(--gf-ctrl-multiselect-close-icon-size) / 2));--gf-ctrl-multiselect-close-icon-inset-x-end:calc((var(--gf-ctrl-padding-x) / 2) + 2px);--gf-ctrl-multiselect-selected-item-bg-color:var(--gf-color-in-ctrl-primary);--gf-ctrl-multiselect-selected-item-radius:33px;--gf-ctrl-multiselect-selected-item-color:var(--gf-color-in-ctrl-primary-contrast);--gf-ctrl-multiselect-selected-item-font-size:var(--gf-ctrl-font-size);--gf-ctrl-multiselect-selected-item-font-weight:600;--gf-ctrl-multiselect-selected-item-remove-icon-color:var(--gf-color-in-ctrl-primary-contrast)}html[dir=rtl] .gform-theme--api,html[dir=rtl] .gform-theme--framework{--gf-ctrl-select-icon-position:var(--gf-ctrl-padding-x) center;--gf-ctrl-select-search-icon-position:calc(100% - var(--gf-padding-x)) center}.gform-theme--api,.gform-theme--framework{--gf-ctrl-textarea-height:130px;--gf-ctrl-textarea-radius:var(--gf-ctrl-radius-max-lg);--gf-ctrl-textarea-line-height:1.5;--gf-ctrl-textarea-padding-y:var(--gf-padding-y);--gf-ctrl-textarea-resize:vertical}.gform-theme--api,.gform-theme--framework{--gf-field-date-ctrl-padding-x-end:calc(var(--gf-ctrl-padding-x) + var(--gf-icon-font-size) + 4px);--gf-field-date-icon-color:var(--gf-ctrl-icon-color);--gf-field-date-icon-color-hover:var(--gf-ctrl-icon-color-hover);--gf-field-date-icon-transition:var(--gf-ctrl-transition);--gf-field-date-custom-icon-max-height:16px;--gf-field-date-custom-icon-max-width:16px;--gf-field-date-custom-icon-opacity:0.6;--gf-field-date-custom-icon-opacity-hover:1}.gform-theme--api,.gform-theme--framework{--gf-field-choice-gap:var(--gf-label-space-x-secondary);--gf-field-choice-align-x-gap-y:var(--gf-field-choice-gap);--gf-field-choice-align-x-gap-x:16px;--gf-field-choice-meta-margin-y-start:4px;--gf-field-choice-meta-space:16px;--gf-field-choice-other-ctrl-max-width:256px;--gf-field-img-choice-aspect-ratio:1/1;--gf-field-img-choice-gap:var(--gf-field-gap-x);--gf-field-img-choice-margin-y-end:12px;--gf-field-img-choice-placeholder-icon-font-size:60px;--gf-field-img-choice-radius-square:var(--gf-ctrl-radius-max-sm);--gf-field-img-choice-radius-round:50%;--gf-field-img-choice-shadow:0 0 0 rgba(18, 25, 97, 0.05),0 2px 5px rgba(18, 25, 97, 0.1),0 1px 1px rgba(18, 25, 97, 0.15);--gf-field-img-choice-shadow-hover:0 0 1px rgba(18, 25, 97, 0.24),0 24px 24px rgba(18, 25, 97, 0.03),0 2px 2px rgba(18, 25, 97, 0.03),0 4px 4px rgba(18, 25, 97, 0.03),0 8px 8px rgba(18, 25, 97, 0.03),0 16px 16px rgba(18, 25, 97, 0.03);--gf-field-img-choice-size-sm:125px;--gf-field-img-choice-size-md:200px;--gf-field-img-choice-size-lg:300px;--gf-field-img-choice-size:var(--gf-field-img-choice-size-md);--gf-field-img-choice-card-placeholder-bg-color:rgba(var(--gf-color-in-ctrl-light-rgb), 0.05);--gf-field-img-choice-card-placeholder-color:rgba(var(--gf-color-in-ctrl-dark-rgb), 0.4);--gf-field-img-choice-card-check-ind-bg-color:var(--gf-color-in-ctrl-primary);--gf-field-img-choice-card-check-ind-icon-color:var(--gf-color-in-ctrl-primary-contrast);--gf-field-img-choice-card-space-sm:8px;--gf-field-img-choice-card-space-md:12px;--gf-field-img-choice-card-space-lg:16px;--gf-field-img-choice-card-space:var(--gf-field-img-choice-card-space-md);--gf-field-img-choice-no-card-placeholder-bg-color:rgba(var(--gf-color-out-ctrl-light-rgb), 0.05);--gf-field-img-choice-no-card-placeholder-color:rgba(var(--gf-color-out-ctrl-dark-rgb), 0.4);--gf-field-img-choice-no-card-check-ind-bg-color:var(--gf-color-in-ctrl-primary);--gf-field-img-choice-no-card-check-ind-icon-color:var(--gf-color-in-ctrl-primary-contrast);--gf-field-img-choice-check-ind-icon:var(--gf-icon-ctrl-checkbox);--gf-field-img-choice-check-ind-radius:50%;--gf-field-img-choice-check-ind-shadow:drop-shadow(0 1px 1px rgba(18, 25, 97, 0.15)) drop-shadow(0 2px 5px rgba(18, 25, 97, 0.1)) drop-shadow(0 0 0 rgba(18, 25, 97, 0.05));--gf-field-img-choice-check-ind-size-sm:24px;--gf-field-img-choice-check-ind-size-md:38px;--gf-field-img-choice-check-ind-size-lg:64px;--gf-field-img-choice-check-ind-size:var(--gf-field-img-choice-check-ind-size-md);--gf-field-img-choice-check-ind-icon-size-sm:12px;--gf-field-img-choice-check-ind-icon-size-md:var(--gf-icon-font-size);--gf-field-img-choice-check-ind-icon-size-lg:30px;--gf-field-img-choice-check-ind-icon-size:var(--gf-field-img-choice-check-ind-icon-size-md);--gf-field-img-choice-ctrl-opacity:1;--gf-field-img-choice-ctrl-opacity-disabled:0.5;--gf-field-img-choice-other-ctrl-margin-y-start:16px}.gform-theme--api,.gform-theme--framework{--gf-field-list-btn-size:16px;--gf-field-list-btn-radius:50%;--gf-field-list-btn-font-size:0;--gf-field-list-btn-padding-y:0;--gf-field-list-btn-padding-x:0}.gform-theme--api,.gform-theme--framework{--gf-field-pg-prog-color:var(--gf-color-out-ctrl-dark);--gf-field-pg-prog-margin-y-end:24px;--gf-field-pg-prog-title-margin-y-end:16px;--gf-field-pg-prog-font-family:var(--gf-font-family-base);--gf-field-pg-prog-font-size:14px;--gf-field-pg-prog-font-style:var(--gf-font-style-base);--gf-field-pg-prog-font-weight:600;--gf-field-pg-prog-letter-spacing:0;--gf-field-pg-prog-line-height:1;--gf-field-pg-prog-text-transform:uppercase;--gf-field-pg-prog-bar-bg-color:var(--gf-color-out-ctrl-light);--gf-field-pg-prog-bar-bg-color-blue:#204ce5;--gf-field-pg-prog-bar-bg-color-gray:var(--gf-color-out-ctrl-dark);--gf-field-pg-prog-bar-bg-color-green:#31c48d;--gf-field-pg-prog-bar-bg-color-orange:#ff5a1f;--gf-field-pg-prog-bar-bg-color-red:#c02b0a;--gf-field-pg-prog-bar-bg-gradient-spring:linear-gradient(270deg, #9cd790 0%, #76d7db 100%);--gf-field-pg-prog-bar-bg-gradient-blues:linear-gradient(270deg, #00c2ff 0%, #7838e2 100%);--gf-field-pg-prog-bar-bg-gradient-rainbow:linear-gradient(274.73deg, #74b551 -5.58%, #f3ca30 44.81%, #cd302b 93.15%);--gf-field-pg-prog-bar-radius:100px;--gf-field-pg-prog-bar-height:10px;--gf-field-pg-steps-number-bg-color:transparent;--gf-field-pg-steps-number-bg-color-active:var(--gf-color-out-ctrl-light);--gf-field-pg-steps-number-bg-color-complete:var(--gf-color-primary);--gf-field-pg-steps-number-border-color:var(--gf-color-out-ctrl-light-darker);--gf-field-pg-steps-number-border-color-active:transparent;--gf-field-pg-steps-number-border-color-complete:var(--gf-color-primary);--gf-field-pg-steps-number-border-style:solid;--gf-field-pg-steps-number-border-width:2px;--gf-field-pg-steps-number-radius:50%;--gf-field-pg-steps-number-color:var(--gf-color-out-ctrl-dark);--gf-field-pg-steps-number-color-active:var(--gf-field-pg-steps-number-color);--gf-field-pg-steps-number-color-complete:var(--gf-color-primary-contrast);--gf-field-pg-steps-icon-font-size:var(--gf-icon-font-size);--gf-field-pg-steps-number-size:32px;--gf-field-pg-steps-step-gap:12px}.gform-theme--api,.gform-theme--framework{--gf-field-pwd-ctrl-padding-x-end:calc(var(--gf-ctrl-padding-x) + var(--gf-icon-font-size) + 8px);--gf-field-pwd-str-bg-color:transparent;--gf-field-pwd-str-bg-color-mismatch:transparent;--gf-field-pwd-str-bg-color-short:transparent;--gf-field-pwd-str-bg-color-bad:transparent;--gf-field-pwd-str-bg-color-good:transparent;--gf-field-pwd-str-bg-color-strong:transparent;--gf-field-pwd-str-border-color:transparent;--gf-field-pwd-str-border-color-mismatch:transparent;--gf-field-pwd-str-border-color-short:transparent;--gf-field-pwd-str-border-color-bad:transparent;--gf-field-pwd-str-border-color-good:transparent;--gf-field-pwd-str-border-color-strong:transparent;--gf-field-pwd-str-border-style:var(--gf-ctrl-border-style);--gf-field-pwd-str-border-width:0;--gf-field-pwd-str-radius:0;--gf-field-pwd-str-color:var(--gf-color-out-ctrl-dark);--gf-field-pwd-str-color-mismatch:#c02b0a;--gf-field-pwd-str-color-short:#c02b0a;--gf-field-pwd-str-color-bad:#ff5a1f;--gf-field-pwd-str-color-good:#8b6c32;--gf-field-pwd-str-color-strong:#399f4b;--gf-field-pwd-str-margin-y-start:16px;--gf-field-pwd-str-padding-y:0;--gf-field-pwd-str-padding-x:calc(65px + 8px) 0;--gf-field-pwd-str-font-family:var(--gf-font-family-secondary);--gf-field-pwd-str-font-size:var(--gf-font-size-primary);--gf-field-pwd-str-font-style:var(--gf-font-style-secondary);--gf-field-pwd-str-font-weight:var(--gf-font-weight-secondary);--gf-field-pwd-str-letter-spacing:var(--gf-letter-spacing-secondary);--gf-field-pwd-str-line-height:1;--gf-field-pwd-str-text-align:start;--gf-field-pwd-str-transition:var(--gf-transition-ctrl);--gf-field-pwd-str-ind-bg-color:var(--gf-color-out-ctrl-light);--gf-field-pwd-str-ind-bg-color-mismatch:var(--gf-field-pwd-str-color-mismatch);--gf-field-pwd-str-ind-bg-color-short:var(--gf-field-pwd-str-color-short);--gf-field-pwd-str-ind-bg-color-bad:var(--gf-field-pwd-str-color-bad);--gf-field-pwd-str-ind-bg-color-good:var(--gf-field-pwd-str-color-good);--gf-field-pwd-str-ind-bg-color-strong:var(--gf-field-pwd-str-color-strong);--gf-field-pwd-str-ind-radius:var(--gf-radius);--gf-field-pwd-str-ind-display:inline-block;--gf-field-pwd-str-ind-inset-y-start:50%;--gf-field-pwd-str-ind-inset-x-start:0;--gf-field-pwd-str-ind-position:absolute;--gf-field-pwd-str-ind-height:6px;--gf-field-pwd-str-ind-width:65px;--gf-field-pwd-str-ind-width-blank:0;--gf-field-pwd-str-ind-width-mismatch:65px;--gf-field-pwd-str-ind-width-short:22px;--gf-field-pwd-str-ind-width-bad:37px;--gf-field-pwd-str-ind-width-good:46px;--gf-field-pwd-str-ind-width-strong:65px;--gf-field-pwd-str-ind-content:"";--gf-field-pwd-str-ind-transform:translateY(-50%);--gf-field-pwd-str-ind-transition:var(--gf-transition-ctrl)}.gform-theme--api,.gform-theme--framework{--gf-field-prod-price-color:var(--gf-ctrl-label-color-primary);--gf-field-prod-quant-margin-y-end:var(--gf-field-gap-y);--gf-field-prod-quant-width:150px}.gform-theme--api,.gform-theme--framework{--gf-field-repeater-gap-y:var(--gf-form-gap-y);--gf-field-repeater-btn-inline-gap:var(--gf-form-gap-x);--gf-field-repeater-separator-color:var(--gf-color-out-ctrl-light-darker);--gf-field-repeater-separator-size:1px;--gf-field-repeater-nested-border-color:var(--gf-color-out-ctrl-light-darker);--gf-field-repeater-nested-border-size:1px;--gf-field-repeater-nested-border-style:solid;--gf-field-repeater-nested-padding-x-start:20px}.gform-theme--api,.gform-theme--framework{--gf-field-section-border-color:var(--gf-color-out-ctrl-light-darker);--gf-field-section-border-style:solid;--gf-field-section-border-width:1px;--gf-field-section-padding-y-end:8px}.gform-theme--api,.gform-theme--framework{--gf-form-validation-bg-color:rgba(var(--gf-color-danger-rgb), 0.03);--gf-form-validation-border-color:rgba(var(--gf-color-danger-rgb), 0.25);--gf-form-validation-border-color-focus:var(--gf-color-danger);--gf-form-validation-border-width:1px;--gf-form-validation-border-style:solid;--gf-form-validation-radius:var(--gf-ctrl-radius-max-md);--gf-form-validation-outline-color-focus:rgba(var(--gf-color-danger-rgb), 0.65);--gf-form-validation-outline-focus:var(--gf-ctrl-outline-width-focus) var(--gf-ctrl-outline-style) var(--gf-form-validation-outline-color-focus);--gf-form-validation-shadow:0 1px 4px rgba(18, 25, 97, 0.0779552);--gf-form-validation-color:var(--gf-color-danger);--gf-form-validation-font-family:var(--gf-font-family-primary);--gf-form-validation-font-size:var(--gf-font-size-primary);--gf-form-validation-line-height:1.43;--gf-form-validation-gap:8px;--gf-form-validation-margin-y:0 var(--gf-form-gap-y);--gf-form-validation-padding-y:20px;--gf-form-validation-padding-x:16px;--gf-form-validation-heading-color:var(--gf-form-validation-color);--gf-form-validation-heading-font-family:var(--gf-form-validation-font-family);--gf-form-validation-heading-font-size:var(--gf-form-validation-font-size);--gf-form-validation-heading-font-weight:500;--gf-form-validation-heading-line-height:var(--gf-form-validation-line-height);--gf-form-validation-heading-gap:12px;--gf-form-validation-heading-icon-bg-color:rgba(var(--gf-color-danger-rgb), 0.05);--gf-form-validation-heading-icon-border-color:var(--gf-form-validation-border-color);--gf-form-validation-heading-icon-border-width:2px;--gf-form-validation-heading-icon-border-style:var(--gf-form-validation-border-style);--gf-form-validation-heading-icon-radius:50%;--gf-form-validation-heading-icon-color:var(--gf-form-validation-heading-color);--gf-form-validation-heading-icon-font-size:18px;--gf-form-validation-heading-icon-size:20px;--gf-form-validation-summary-color:var(--gf-form-validation-color);--gf-form-validation-summary-font-family:var(--gf-form-validation-font-family);--gf-form-validation-summary-font-size:var(--gf-form-validation-font-size);--gf-form-validation-summary-font-weight:400;--gf-form-validation-summary-line-height:var(--gf-form-validation-line-height);--gf-form-validation-summary-margin-y-start:4px;--gf-form-validation-summary-padding-x:48px;--gf-form-validation-summary-item-link-text-decoration:underline}.gform-theme--api,.gform-theme--framework{--gf-form-spinner-fg-color:var(--gf-color-primary);--gf-form-spinner-bg-color:rgba(var(--gf-color-primary-rgb), 0.1)}.gform-theme--framework :where(
:not(html):not(iframe):not(canvas):not(img):not(svg):not(video)
:not(svg *):not(symbol *)
:not(.gform-theme__no-reset--el):not(.gform-theme__no-reset--children *):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-reset):not(.gform-theme__disable-reset *):not(.gform_heading *):not(.gfield--type-html *):not(.gfield--type-section *):not(.form_saved_message > *):not(.form_saved_message_sent > *):not(.gform_confirmation_message *):not(.wp-editor-container):not(.mce-tinymce):not(.mce-tinymce *):not(.wp-editor-area):not(.gfield_description > *):not(.gform-field-label--type-inline > :not(span)):not(.ui-resizable-handle):not(.hidden)
){all:unset;display:revert}.gform-theme--framework *,.gform-theme--framework ::after,.gform-theme--framework ::before{box-sizing:border-box}.gform-theme--framework a,.gform-theme--framework button{cursor:revert}.gform-theme--framework menu,.gform-theme--framework ol:where(:not(.gform_heading *):not(.gfield--type-html *):not(.gfield--type-section *):not(.form_saved_message > *):not(.form_saved_message_sent > *):not(.gform_confirmation_message *)),.gform-theme--framework ul:where(:not(.gform_heading *):not(.gfield--type-html *):not(.gfield--type-section *):not(.form_saved_message > *):not(.form_saved_message_sent > *):not(.gform_confirmation_message *)){list-style:none}.gform-theme--framework img{max-inline-size:100%;max-block-size:100%}.gform-theme--framework table{border-collapse:collapse}.gform-theme--framework input,.gform-theme--framework textarea{-webkit-user-select:auto}.gform-theme--framework textarea{white-space:revert}.gform-theme--framework meter{-webkit-appearance:revert;-moz-appearance:revert;appearance:revert}.gform-theme--framework :where(pre){all:revert}.gform-theme--framework ::-moz-placeholder{color:unset}.gform-theme--framework ::placeholder{color:unset}.gform-theme--framework :where([hidden]){display:none}.gform-theme--framework :where([contenteditable]:not([contenteditable=false])){-moz-user-modify:read-write;-webkit-user-modify:read-write;word-wrap:break-word;-webkit-line-break:after-white-space;-webkit-user-select:auto}.gform-theme--framework :where([draggable=true]){-webkit-user-drag:element}.gform-theme--framework :where(dialog:modal){all:revert}.gform-theme--framework input[type=checkbox]::before,.gform-theme--framework input[type=radio]::before{height:auto;margin-block:0;margin-inline:0;position:static;width:auto}.gform-theme--framework input[type=checkbox]::after,.gform-theme--framework input[type=radio]::after{content:none}@font-face{font-family:gform-icons-orbital;src:url('../../../fonts/gform-icons-orbital.woff2?gxy8zs') format('woff2'),url('../../../fonts/gform-icons-orbital.ttf?gxy8zs') format('truetype'),url('../../../fonts/gform-icons-orbital.woff?gxy8zs') format('woff'),url('../../../fonts/gform-icons-orbital.svg?gxy8zs#gform-icons-orbital') format('svg');font-weight:400;font-style:normal;font-display:block}.gform-orbital-icon{font-family:var(--gf-icon-font-family)!important;speak:never;font-style:normal;font-weight:400;font-feature-settings:normal;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.gform-orbital-icon--photograph:before{content:"\e922"}.gform-orbital-icon--arrow-back:before{content:"\e921"}.gform-orbital-icon--calendar-alt:before{content:"\e91a"}.gform-orbital-icon--selector:before{content:"\e90d"}.gform-orbital-icon--search:before{content:"\e917"}.gform-orbital-icon--trash:before{content:"\e919"}.gform-orbital-icon--cloud-upload-alt:before{content:"\e911"}.gform-orbital-icon--arrow-up:before{content:"\e912"}.gform-orbital-icon--arrow-down:before{content:"\e914"}.gform-orbital-icon--arrow-left:before{content:"\e915"}.gform-orbital-icon--arrow-right:before{content:"\e916"}.gform-orbital-icon--arrow-narrow-right:before{content:"\e913"}.gform-orbital-icon--arrow-sm-left:before{content:"\e91f"}.gform-orbital-icon--arrow-sm-right:before{content:"\e920"}.gform-orbital-icon--save-as:before{content:"\e910"}.gform-orbital-icon--minus-alt:before{content:"\e90e"}.gform-orbital-icon--plus-alt:before{content:"\e90f"}.gform-orbital-icon--eye-off:before{content:"\e90a"}.gform-orbital-icon--eye:before{content:"\e909"}.gform-orbital-icon--check-circle:before{content:"\e90c"}.gform-orbital-icon--check-mark:before{content:"\e900"}.gform-orbital-icon--check:before{content:"\e90b"}.gform-orbital-icon--check-mark-simple:before{content:"\e905"}.gform-orbital-icon--exclamation-simple:before{content:"\e906"}.gform-orbital-icon--information-simple:before{content:"\e907"}.gform-orbital-icon--question-mark-simple:before{content:"\e908"}.gform-orbital-icon--chevron-down:before{content:"\e901"}.gform-orbital-icon--chevron-up:before{content:"\e902"}.gform-orbital-icon--chevron-left:before{content:"\e91b"}.gform-orbital-icon--chevron-right:before{content:"\e91c"}.gform-orbital-icon--chevron-double-left:before{content:"\e91d"}.gform-orbital-icon--chevron-double-right:before{content:"\e91e"}.gform-orbital-icon--minus:before{content:"\e903"}.gform-orbital-icon--x-circle:before{content:"\e918"}.gform-orbital-icon--x:before{content:"\e904"}.gform-theme--framework ::-moz-placeholder:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){color:var(--gf-ctrl-placeholder-color);font-family:var(--gf-ctrl-placeholder-font-family);font-size:var(--gf-ctrl-placeholder-font-size);font-style:var(--gf-ctrl-placeholder-font-style);font-weight:var(--gf-ctrl-placeholder-font-weight);letter-spacing:var(--gf-ctrl-placeholder-letter-spacing);opacity:var(--gf-ctrl-placeholder-opacity)}.gform-theme--framework ::placeholder:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){color:var(--gf-ctrl-placeholder-color);font-family:var(--gf-ctrl-placeholder-font-family);font-size:var(--gf-ctrl-placeholder-font-size);font-style:var(--gf-ctrl-placeholder-font-style);font-weight:var(--gf-ctrl-placeholder-font-weight);letter-spacing:var(--gf-ctrl-placeholder-letter-spacing);opacity:var(--gf-ctrl-placeholder-opacity)}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-search input[type=text]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-single:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework .gform-theme-field-control:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework input[type]:where(:not(.gform-text-input-reset):not([type=hidden])):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework select[multiple]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework textarea:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework.gform-theme.gform_wrapper .button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework.gform-theme.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework.gform-theme.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework.gform-theme.gform_wrapper button.button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework.gform-theme.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework.gform-theme.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework.gform-theme.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-appearance:var(--gf-ctrl-appearance);--gf-local-bg-color:var(--gf-ctrl-bg-color);--gf-local-height:var(--gf-ctrl-size);--gf-local-radius:var(--gf-ctrl-radius);--gf-local-border-color:var(--gf-ctrl-border-color);--gf-local-border-width:var(--gf-ctrl-border-width);--gf-local-border-style:var(--gf-ctrl-border-style);--gf-local-border-block-start:var(--gf-local-border-width) var(--gf-local-border-style) var(--gf-local-border-color);--gf-local-border-block-end:var(--gf-local-border-width) var(--gf-local-border-style) var(--gf-local-border-color);--gf-local-border-inline-start:var(--gf-local-border-width) var(--gf-local-border-style) var(--gf-local-border-color);--gf-local-border-inline-end:var(--gf-local-border-width) var(--gf-local-border-style) var(--gf-local-border-color);--gf-local-color:var(--gf-ctrl-color);--gf-local-display:block;--gf-local-font-family:var(--gf-ctrl-font-family);--gf-local-font-size:var(--gf-ctrl-font-size);--gf-local-font-style:var(--gf-ctrl-font-style);--gf-local-font-weight:var(--gf-ctrl-font-weight);--gf-local-letter-spacing:var(--gf-ctrl-letter-spacing);--gf-local-line-height:var(--gf-ctrl-line-height);--gf-local-min-height:auto;--gf-local-outline-color:var(--gf-ctrl-outline-color);--gf-local-outline-offset:var(--gf-ctrl-outline-offset);--gf-local-outline-style:var(--gf-ctrl-outline-style);--gf-local-outline-width:var(--gf-ctrl-outline-width);--gf-local-padding-x:var(--gf-ctrl-padding-x);--gf-local-padding-y:var(--gf-ctrl-padding-y);--gf-local-shadow:var(--gf-ctrl-shadow);--gf-local-transition:var(--gf-ctrl-transition);--gf-local-width:100%;-webkit-appearance:var(--gf-local-appearance);-moz-appearance:var(--gf-local-appearance);appearance:var(--gf-local-appearance);background-color:var(--gf-local-bg-color);block-size:var(--gf-local-height);border-block-end:var(--gf-local-border-block-end);border-block-start:var(--gf-local-border-block-start);border-inline-end:var(--gf-local-border-inline-end);border-inline-start:var(--gf-local-border-inline-start);border-radius:var(--gf-local-radius);box-shadow:var(--gf-local-shadow);color:var(--gf-local-color);display:var(--gf-local-display);font-family:var(--gf-local-font-family);font-size:var(--gf-local-font-size);font-style:var(--gf-local-font-style);font-weight:var(--gf-local-font-weight);inline-size:var(--gf-local-width);letter-spacing:var(--gf-local-letter-spacing);line-height:var(--gf-local-line-height);margin-block:0;margin-inline:0;min-block-size:var(--gf-local-min-height);outline-color:var(--gf-local-outline-color);outline-offset:var(--gf-local-outline-offset);outline-style:var(--gf-local-outline-style);outline-width:var(--gf-local-outline-width);padding-block:var(--gf-local-padding-y);padding-inline:var(--gf-local-padding-x);transition:var(--gf-local-transition)}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-search input[type=text]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-single:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework .gform-theme-field-control:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework input[type]:where(:not(.gform-text-input-reset):not([type=hidden])):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework select[multiple]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework textarea:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework.gform-theme.gform_wrapper .button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework.gform-theme.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework.gform-theme.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework.gform-theme.gform_wrapper button.button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework.gform-theme.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework.gform-theme.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme--framework.gform-theme.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover{--gf-local-bg-color:var(--gf-ctrl-bg-color-hover);--gf-local-border-color:var(--gf-ctrl-border-color-hover);--gf-local-color:var(--gf-ctrl-color-hover)}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-search input[type=text]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-single:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework .gform-theme-field-control:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework input[type]:where(:not(.gform-text-input-reset):not([type=hidden])):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework select[multiple]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework textarea:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework.gform-theme.gform_wrapper .button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework.gform-theme.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework.gform-theme.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework.gform-theme.gform_wrapper button.button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework.gform-theme.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework.gform-theme.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework.gform-theme.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus{--gf-local-bg-color:var(--gf-ctrl-bg-color-focus);--gf-local-border-color:var(--gf-ctrl-border-color-focus);--gf-local-color:var(--gf-ctrl-color-focus);--gf-local-outline-color:var(--gf-ctrl-outline-color-focus);--gf-local-outline-width:var(--gf-ctrl-outline-width-focus)}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-search input[type=text]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-single:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework .gform-theme-field-control:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework input[type]:where(:not(.gform-text-input-reset):not([type=hidden])):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework select[multiple]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework textarea:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework.gform-theme.gform_wrapper .button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework.gform-theme.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework.gform-theme.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework.gform-theme.gform_wrapper button.button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework.gform-theme.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework.gform-theme.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme--framework.gform-theme.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework)):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled{--gf-local-bg-color:var(--gf-ctrl-bg-color-disabled);--gf-local-border-color:var(--gf-ctrl-border-color-disabled);--gf-local-color:var(--gf-ctrl-color-disabled)}.gform-theme--framework .gfield_error .gform-theme-field-control:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework .gfield_error input[type]:where(:not(.gform-text-input-reset):not([type=hidden])):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework .gfield_error select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework .gfield_error select[multiple]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework .gfield_error textarea:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-bg-color:var(--gf-ctrl-bg-color-error);--gf-local-border-color:var(--gf-ctrl-border-color-error);--gf-local-color:var(--gf-ctrl-color-error)}.gform-theme--framework .gfield_error .gform-theme-field-control:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework .gfield_error input[type]:where(:not(.gform-text-input-reset):not([type=hidden])):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework .gfield_error select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework .gfield_error select[multiple]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme--framework .gfield_error textarea:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus{--gf-local-bg-color:var(--gf-ctrl-bg-color-focus);--gf-local-border-color:var(--gf-ctrl-border-color-focus);--gf-local-color:var(--gf-ctrl-color-focus);--gf-local-outline-color:var(--gf-ctrl-outline-color-focus);--gf-local-outline-width:var(--gf-ctrl-outline-width-focus)}.gform-theme--framework input[type].gform-text-input-reset{--gf-local-border-color:transparent;--gf-local-height:auto;--gf-local-color:var(--gf-ctrl-readonly-color);--gf-local-font-family:var(--gf-ctrl-readonly-font-family);--gf-local-font-size:var(--gf-ctrl-readonly-font-size);--gf-local-font-style:var(--gf-ctrl-readonly-font-style);--gf-local-font-weight:var(--gf-ctrl-readonly-font-weight);--gf-local-letter-spacing:var(--gf-ctrl-readonly-letter-spacing);--gf-local-line-height:var(--gf-ctrl-readonly-line-height);--gf-local-min-height:auto;--gf-local-outline-color:var(--gf-ctrl-outline-color);--gf-local-outline-width:var(--gf-ctrl-outline-width);block-size:var(--gf-local-height);border:var(--gf-ctrl-border-width) var(--gf-ctrl-border-style) var(--gf-local-border-color);border-radius:var(--gf-ctrl-radius);color:var(--gf-local-color);font-family:var(--gf-local-font-family);font-size:var(--gf-local-font-size);font-style:var(--gf-local-font-style);font-weight:var(--gf-local-font-weight);letter-spacing:var(--gf-local-letter-spacing);line-height:var(--gf-local-line-height);min-block-size:var(--gf-local-min-height);outline-color:var(--gf-local-outline-color);outline-offset:var(--gf-ctrl-outline-offset);outline-style:var(--gf-ctrl-outline-style);outline-width:var(--gf-local-outline-width);transition:var(--gf-ctrl-transition)}.gform-theme--framework input[type].gform-text-input-reset:focus{--gf-local-border-color:var(--gf-ctrl-border-color-focus);--gf-local-outline-color:var(--gf-ctrl-outline-color-focus);--gf-local-outline-width:var(--gf-ctrl-outline-width-focus)}.gform-theme--framework input[type=number]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::-webkit-inner-spin-button,.gform-theme--framework input[type=number]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::-webkit-outer-spin-button{opacity:var(--gf-ctrl-number-spin-btn-opacity)}.gform-theme--framework input[type=number]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::-webkit-inner-spin-button{-webkit-appearance:var(--gf-ctrl-number-spin-btn-appearance);appearance:var(--gf-ctrl-number-spin-btn-appearance);background-image:var(--gf-icon-ctrl-number);background-position:var(--gf-ctrl-number-spin-btn-bg-position);background-repeat:no-repeat;background-size:var(--gf-ctrl-number-spin-btn-bg-size);cursor:pointer;width:var(--gf-ctrl-number-spin-btn-width)}.gform-theme--framework textarea:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-radius:var(--gf-ctrl-textarea-radius);--gf-local-height:var(--gf-ctrl-textarea-height);--gf-local-line-height:var(--gf-ctrl-textarea-line-height);--gf-local-padding-y:var(--gf-ctrl-textarea-padding-y);resize:var(--gf-ctrl-textarea-resize)}.gform-theme--framework .wp-editor-container:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-border-color:var(--gf-ctrl-border-color);--gf-local-outline-color:var(--gf-ctrl-outline-color);--gf-local-outline-width:var(--gf-ctrl-outline-width);border:var(--gf-ctrl-border-width) var(--gf-ctrl-border-style) var(--gf-local-border-color);border-radius:var(--gf-ctrl-textarea-radius);box-shadow:var(--gf-ctrl-shadow);outline-color:var(--gf-local-outline-color);outline-offset:var(--gf-ctrl-outline-offset);outline-style:var(--gf-ctrl-outline-style);outline-width:var(--gf-local-outline-width);overflow:hidden;transition:var(--gf-transition-ctrl)}.gform-theme--framework .wp-editor-container:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover{--gf-local-border-color:var(--gf-ctrl-border-color-hover)}.gform-theme--framework .wp-editor-container:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))[\:has\(.wp-editor-iframe-active\)]{--gf-local-border-color:var(--gf-ctrl-border-color-focus);--gf-local-outline-color:var(--gf-ctrl-outline-color-focus);--gf-local-outline-width:var(--gf-ctrl-outline-width-focus)}.gform-theme--framework .wp-editor-container:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):has(.wp-editor-iframe-active){--gf-local-border-color:var(--gf-ctrl-border-color-focus);--gf-local-outline-color:var(--gf-ctrl-outline-color-focus);--gf-local-outline-width:var(--gf-ctrl-outline-width-focus)}.gform-theme--framework .wp-editor-container textarea{border-color:transparent;border-width:0}.gform-theme--framework .gfield_error .wp-editor-container:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-border-color:var(--gf-ctrl-border-color-error)}.gform-theme--framework .gfield_error .wp-editor-container:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))[\:has\(.wp-editor-iframe-active\)]{--gf-local-border-color:var(--gf-ctrl-border-color-focus)}.gform-theme--framework .gfield_error .wp-editor-container:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):has(.wp-editor-iframe-active){--gf-local-border-color:var(--gf-ctrl-border-color-focus)}.gform-theme--framework input[type=checkbox]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework input[type=radio]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-height:var(--gf-ctrl-choice-size);--gf-local-display:inline-grid;--gf-local-line-height:var(--gf-ctrl-choice-size);--gf-local-padding-y:0;--gf-local-padding-x:0;--gf-local-width:var(--gf-ctrl-choice-size);accent-color:var(--gf-ctrl-accent-color);opacity:1;align-content:center;justify-content:center;place-content:center}.gform-theme--framework input[type=checkbox]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::before,.gform-theme--framework input[type=radio]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::before{opacity:0}.gform-theme--framework input[type=checkbox]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):checked::before,.gform-theme--framework input[type=radio]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):checked::before{opacity:1}.gform-theme--framework input[type=checkbox]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-radius:var(--gf-ctrl-checkbox-check-radius)}.gform-theme--framework input[type=checkbox]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::before{font-family:var(--gf-icon-font-family)!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:never;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;color:var(--gf-ctrl-choice-check-color);content:var(--gf-icon-ctrl-checkbox);font-size:var(--gf-ctrl-checkbox-check-size)}.gform-theme--framework input[type=checkbox]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::before{color:var(--gf-ctrl-choice-check-color-disabled)}.gform-theme--framework input[type=radio]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-radius:var(--gf-ctrl-radio-check-radius)}.gform-theme--framework input[type=radio]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::before{background-color:var(--gf-ctrl-choice-check-color);block-size:var(--gf-ctrl-radio-check-size);border-radius:var(--gf-ctrl-radio-check-radius);content:var(--gf-ctrl-radio-check-content);inline-size:var(--gf-ctrl-radio-check-size)}.gform-theme--framework input[type=radio]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::before{background-color:var(--gf-ctrl-choice-check-color-disabled)}.gform-theme--framework select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):where(:not([multiple])){--gf-local-padding-x:var(--gf-ctrl-select-padding-x);background-image:var(--gf-ctrl-select-icon);background-position:var(--gf-ctrl-select-icon-position);background-repeat:no-repeat;background-size:var(--gf-ctrl-select-icon-size)}.gform-theme--framework select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):where(:not([multiple])):hover{background-image:var(--gf-ctrl-select-icon-hover)}.gform-theme--framework select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):where(:not([multiple])):focus{background-image:var(--gf-ctrl-select-icon-focus)}.gform-theme--framework select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):where(:not([multiple])):disabled{background-image:var(--gf-ctrl-select-icon-disabled)}.gform-theme--framework select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::-ms-expand{display:var(--gf-ctrl-select-ms-expand)}.gform-theme--framework select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))[multiple]{--gf-local-height:var(--gf-ctrl-multiselect-height);--gf-local-radius:var(--gf-ctrl-multiselect-radius);--gf-local-line-height:var(--gf-ctrl-multiselect-line-height);--gf-local-padding-y:var(--gf-ctrl-multiselect-padding-y);overflow-y:auto}.gform-theme--framework select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))[multiple] option:checked{background:var(--gf-color-in-ctrl-light)}.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container-single .chosen-single{background-image:var(--gf-icon-ctrl-select);background-position:var(--gf-ctrl-select-icon-position);background-repeat:no-repeat;background-size:var(--gf-ctrl-select-icon-size)}.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container-single .chosen-single span{margin-inline-end:var(--gf-ctrl-select-search-icon-size)}.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container-single .chosen-single abbr,.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container-single .chosen-single div b{background-image:none!important}.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container-single.chosen-container-active .chosen-single,.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container-single.chosen-container-active.chosen-with-drop .chosen-single{--gf-local-bg-color:var(--gf-ctrl-bg-color-focus);--gf-local-border-color:var(--gf-ctrl-border-color-focus);--gf-local-color:var(--gf-ctrl-color-focus);--gf-local-outline-color:var(--gf-ctrl-outline-color-focus);--gf-local-outline-width:var(--gf-ctrl-outline-width-focus)}.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container-single.chosen-container-active.chosen-with-drop .chosen-single{border-bottom-left-radius:var(--gf-ctrl-radius);border-bottom-right-radius:var(--gf-ctrl-radius);border-color:var(--gf-ctrl-border-color-focus)}.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container-single .chosen-search{padding-block:var(--gf-padding-y) 0;padding-inline:var(--gf-ctrl-padding-x)}.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container-single .chosen-search input[type=text]{--gf-local-height:var(--gf-ctrl-size-md);--gf-local-padding-x:var(--gf-ctrl-select-search-padding-x);background-image:var(--gf-icon-ctrl-search)!important;background-position:var(--gf-ctrl-select-search-icon-position);background-size:var(--gf-ctrl-select-search-icon-size)!important}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi{--gf-local-height:auto;--gf-local-radius:var(--gf-ctrl-multiselect-radius);padding-inline:0}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi.chosen-container-active{border-color:var(--gf-ctrl-border-color-focus);outline-color:var(--gf-ctrl-outline-color-focus);outline-width:var(--gf-ctrl-outline-width-focus)}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi.chosen-container-active .chosen-choices{box-shadow:none}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi .chosen-choices{align-content:center;background:0 0;border:none;display:flex;flex-flow:row wrap;gap:calc(var(--gf-padding-y)/ 2);min-height:calc(var(--gf-ctrl-size) - 2px);padding-block:4px;padding-inline:var(--gf-ctrl-padding-x)}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi .chosen-choices li.search-field input[type=text].chosen-search-input{color:var(--gf-ctrl-placeholder-color);font-family:var(--gf-ctrl-placeholder-font-family);font-size:var(--gf-ctrl-placeholder-font-size);font-style:var(--gf-ctrl-placeholder-font-style);font-weight:var(--gf-ctrl-placeholder-font-weight);letter-spacing:var(--gf-ctrl-placeholder-letter-spacing);opacity:var(--gf-ctrl-placeholder-opacity)}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi .chosen-choices .search-choice+li.search-field input[type=text].chosen-search-input{color:var(--gf-ctrl-color-focus);font-family:var(--gf-ctrl-font-family);font-size:var(--gf-ctrl-font-size);font-style:var(--gf-ctrl-font-style);font-weight:var(--gf-ctrl-font-weight);letter-spacing:var(--gf-ctrl-letter-spacing);opacity:1}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi .chosen-choices li.search-choice{align-self:flex-start;background:0 0;background-color:var(--gf-ctrl-multiselect-selected-item-bg-color);border:none;border-radius:var(--gf-ctrl-multiselect-selected-item-radius);box-shadow:none;color:var(--gf-ctrl-multiselect-selected-item-color);flex-shrink:0;font-size:var(--gf-ctrl-multiselect-selected-item-font-size);font-weight:var(--gf-ctrl-multiselect-selected-item-font-weight);margin:0;padding-block:8px;padding-inline:var(--gf-ctrl-padding-x) calc((var(--gf-ctrl-padding-x) * 1.25) + var(--gf-ctrl-multiselect-close-icon-size))}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi .chosen-choices li.search-choice .search-choice-close{background:0 0;height:var(--gf-ctrl-multiselect-close-icon-size);inset-block-start:var(--gf-ctrl-multiselect-close-icon-inset-y-start);inset-inline-end:var(--gf-ctrl-multiselect-close-icon-inset-x-end);opacity:70%;transition:var(--gf-local-transition);width:var(--gf-ctrl-multiselect-close-icon-size)}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi .chosen-choices li.search-choice .search-choice-close::before{font-family:var(--gf-icon-font-family)!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:never;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;color:var(--gf-ctrl-multiselect-selected-item-remove-icon-color);content:var(--gf-icon-ctrl-cancel);display:inline-block;font-size:var(--gf-ctrl-multiselect-close-icon-size);height:100%;width:100%}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi .chosen-choices li.search-choice .search-choice-close:focus,.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container-multi .chosen-choices li.search-choice .search-choice-close:hover{opacity:1}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container .chosen-drop,.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container .chosen-drop{background-color:var(--gf-ctrl-bg-color);border:var(--gf-ctrl-select-dropdown-border-color);border-radius:var(--gf-ctrl-select-dropdown-radius);box-shadow:var(--gf-ctrl-select-dropdown-shadow);overflow:hidden;padding-block:0;padding-inline:0;top:calc(100% + 8px)}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container .chosen-results,.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container .chosen-results{margin-block:0;margin-inline:0;padding-block:var(--gf-padding-y);padding-inline:0}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container .chosen-results li,.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container .chosen-results li{color:var(--gf-ctrl-color);padding:var(--gf-padding-y) var(--gf-ctrl-padding-x)}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container .chosen-results li.highlighted,.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container .chosen-results li.highlighted{background:0 0;background-color:var(--gf-ctrl-select-dropdown-option-bg-color-hover)}.gform-theme--framework .gfield:where(.gfield--type-multiselect,.gfield--input-type-multiselect) .chosen-container .chosen-results li.result-selected,.gform-theme--framework .gfield:where(.gfield--type-select,.gfield--input-type-select) .chosen-container .chosen-results li.result-selected{background:0 0;background-color:var(--gf-ctrl-select-dropdown-option-bg-color-hover);box-shadow:var(--gf-ctrl-select-dropdown-option-shadow-hover)}.gform-theme--framework .gfield_list_group_item::before:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework .gform-field-label:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-color:var(--gf-ctrl-label-color-primary);--gf-local-display:flex;--gf-local-font-family:var(--gf-ctrl-label-font-family-primary);--gf-local-font-size:var(--gf-ctrl-label-font-size-primary);--gf-local-font-style:var(--gf-ctrl-label-font-style-primary);--gf-local-font-weight:var(--gf-ctrl-label-font-weight-primary);--gf-local-letter-spacing:var(--gf-ctrl-label-letter-spacing-primary);--gf-local-line-height:var(--gf-ctrl-label-line-height-primary);--gf-local-margin-y:0;--gf-local-margin-x:0;color:var(--gf-local-color);display:var(--gf-local-display);font-family:var(--gf-local-font-family);font-size:var(--gf-local-font-size);font-style:var(--gf-local-font-style);font-weight:var(--gf-local-font-weight);letter-spacing:var(--gf-local-letter-spacing);line-height:var(--gf-local-line-height);margin-block:var(--gf-local-margin-y);margin-inline:var(--gf-local-margin-x)}.gform-theme--framework .gform-field-label--type-inline:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-color:var(--gf-ctrl-label-color-secondary);--gf-local-display:block;--gf-local-font-family:var(--gf-ctrl-label-font-family-secondary);--gf-local-font-size:var(--gf-ctrl-label-font-size-secondary);--gf-local-font-style:var(--gf-ctrl-label-font-style-secondary);--gf-local-font-weight:var(--gf-ctrl-label-font-weight-secondary);--gf-local-letter-spacing:var(--gf-ctrl-label-letter-spacing-secondary);--gf-local-line-height:var(--gf-ctrl-label-line-height-secondary)}.gform-theme--framework .gform-field-label--type-sub:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-color:var(--gf-ctrl-label-color-tertiary);--gf-local-display:block;--gf-local-font-family:var(--gf-ctrl-label-font-family-tertiary);--gf-local-font-size:var(--gf-ctrl-label-font-size-tertiary);--gf-local-font-style:var(--gf-ctrl-label-font-style-tertiary);--gf-local-font-weight:var(--gf-ctrl-label-font-weight-tertiary);--gf-local-letter-spacing:var(--gf-ctrl-label-letter-spacing-tertiary);--gf-local-line-height:var(--gf-ctrl-label-line-height-tertiary)}.gform-theme--framework .gform-field-label--type-sub-large:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-color:var(--gf-ctrl-label-color-quaternary);--gf-local-display:inline-block;--gf-local-font-family:var(--gf-ctrl-label-font-family-quaternary);--gf-local-font-size:var(--gf-ctrl-label-font-size-quaternary);--gf-local-font-style:var(--gf-ctrl-label-font-style-quaternary);--gf-local-font-weight:var(--gf-ctrl-label-font-weight-quaternary);--gf-local-letter-spacing:var(--gf-ctrl-label-letter-spacing-quaternary);--gf-local-line-height:var(--gf-ctrl-label-line-height-quaternary)}.gform-theme--framework .gform-field-label>.gfield_required:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework.gform_editor legend.gform-field-label>span>.gfield_required:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){color:var(--gf-ctrl-label-color-req);display:inline-block;font-family:var(--gf-ctrl-label-font-family-req);font-size:var(--gf-ctrl-label-font-size-req);font-style:var(--gf-ctrl-label-font-style-req);font-weight:var(--gf-ctrl-label-font-weight-req);letter-spacing:var(--gf-ctrl-label-letter-spacing-req);line-height:var(--gf-ctrl-label-line-height-req)}.gform-theme--framework .gfield_description:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-color:var(--gf-ctrl-desc-color);--gf-local-font-family:var(--gf-ctrl-desc-font-family);--gf-local-font-size:var(--gf-ctrl-desc-font-size);--gf-local-font-style:var(--gf-ctrl-desc-font-style);--gf-local-font-weight:var(--gf-ctrl-desc-font-weight);--gf-local-letter-spacing:var(--gf-ctrl-desc-letter-spacing);--gf-local-line-height:var(--gf-ctrl-desc-line-height);color:var(--gf-local-color);display:block;font-family:var(--gf-local-font-family);font-size:var(--gf-local-font-size);font-style:var(--gf-local-font-style);font-weight:var(--gf-local-font-weight);letter-spacing:var(--gf-local-letter-spacing);line-height:var(--gf-local-line-height)}.gform-theme--framework .gfield_validation_message:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-color:var(--gf-ctrl-desc-color-error);--gf-local-font-family:var(--gf-ctrl-desc-font-family-error);--gf-local-font-size:var(--gf-ctrl-desc-font-size-error);--gf-local-font-style:var(--gf-ctrl-desc-font-style-error);--gf-local-font-weight:var(--gf-ctrl-desc-font-weight-error);--gf-local-letter-spacing:var(--gf-ctrl-desc-letter-spacing-error);--gf-local-line-height:var(--gf-ctrl-desc-line-height-error)}.gform-theme--framework .gfield_consent_description:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){border:var(--gf-ctrl-desc-border-width-consent) var(--gf-ctrl-desc-border-style-consent) var(--gf-ctrl-desc-border-color-consent);max-height:var(--gf-ctrl-desc-max-height-consent);outline-color:var(--gf-ctrl-outline-color);outline-offset:var(--gf-ctrl-outline-offset);outline-style:var(--gf-ctrl-outline-style);outline-width:var(--gf-ctrl-outline-width);overflow-y:auto;padding:16px;transition:var(--gf-ctrl-transition)}.gform-theme--framework .gfield_consent_description:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus{border-color:var(--gf-ctrl-desc-border-color-consent-focus);outline-color:var(--gf-ctrl-outline-color-focus);outline-width:var(--gf-ctrl-outline-width-focus)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-primary);--gf-local-radius:var(--gf-ctrl-btn-radius);--gf-local-border-color:var(--gf-ctrl-btn-border-color-primary);--gf-local-border-style:var(--gf-ctrl-btn-border-style-primary);--gf-local-border-width:var(--gf-ctrl-btn-border-width-primary);--gf-local-border-block-start:var(--gf-local-border-width) var(--gf-local-border-style) var(--gf-local-border-color);--gf-local-border-block-end:var(--gf-local-border-width) var(--gf-local-border-style) var(--gf-local-border-color);--gf-local-border-inline-start:var(--gf-local-border-width) var(--gf-local-border-style) var(--gf-local-border-color);--gf-local-border-inline-end:var(--gf-local-border-width) var(--gf-local-border-style) var(--gf-local-border-color);--gf-local-shadow:var(--gf-ctrl-btn-shadow);--gf-local-color:var(--gf-ctrl-btn-color-primary);--gf-local-column-gap:var(--gf-ctrl-btn-icon-gap);--gf-local-cursor:pointer;--gf-local-display:inline-flex;--gf-local-font-style:var(--gf-ctrl-btn-font-style);--gf-local-font-weight:var(--gf-ctrl-btn-font-weight);--gf-local-font-size:var(--gf-ctrl-btn-font-size);--gf-local-font-family:var(--gf-ctrl-btn-font-family);--gf-local-height:auto;--gf-local-letter-spacing:var(--gf-ctrl-btn-letter-spacing);--gf-local-line-height:var(--gf-ctrl-btn-line-height);--gf-local-min-height:var(--gf-ctrl-btn-size);--gf-local-min-width:auto;--gf-local-opacity:var(--gf-ctrl-btn-opacity);--gf-local-padding-y:var(--gf-ctrl-btn-padding-y);--gf-local-padding-x:var(--gf-ctrl-btn-padding-x);--gf-local-text-decoration:var(--gf-ctrl-btn-text-decoration);--gf-local-text-transform:var(--gf-ctrl-btn-text-transform);--gf-local-width:auto;align-items:center;-moz-column-gap:var(--gf-local-column-gap);column-gap:var(--gf-local-column-gap);cursor:var(--gf-local-cursor);justify-content:center;min-inline-size:var(--gf-local-min-width);opacity:var(--gf-local-opacity);text-align:center;-webkit-text-decoration:var(--gf-local-text-decoration);text-decoration:var(--gf-local-text-decoration);text-transform:var(--gf-local-text-transform);vertical-align:top}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-primary);--gf-local-content:var(--gf-ctrl-btn-icon);--gf-local-font-size:var(--gf-ctrl-btn-icon-font-size);--gf-local-transition:var(--gf-ctrl-btn-icon-transition);font-family:var(--gf-icon-font-family)!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:never;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;color:var(--gf-local-color);content:var(--gf-local-content);font-size:var(--gf-local-font-size);transition:var(--gf-local-transition)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-hover-primary);--gf-local-border-color:var(--gf-ctrl-btn-border-color-hover-primary);--gf-local-shadow:var(--gf-ctrl-btn-shadow-hover);--gf-local-color:var(--gf-ctrl-btn-color-hover-primary)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-hover-primary)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-focus-primary);--gf-local-border-color:var(--gf-ctrl-btn-border-color-focus-primary);--gf-local-shadow:var(--gf-ctrl-btn-shadow-focus);--gf-local-color:var(--gf-ctrl-btn-color-focus-primary)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-focus-primary)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-disabled-primary);--gf-local-border-color:var(--gf-ctrl-btn-border-color-disabled-primary);--gf-local-shadow:var(--gf-ctrl-btn-shadow-disabled);--gf-local-color:var(--gf-ctrl-btn-color-disabled-primary);--gf-local-cursor:default;--gf-local-opacity:var(--gf-ctrl-btn-opacity-disabled)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-disabled-primary)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-secondary);--gf-local-border-color:var(--gf-ctrl-btn-border-color-secondary);--gf-local-border-style:var(--gf-ctrl-btn-border-style-secondary);--gf-local-border-width:var(--gf-ctrl-btn-border-width-secondary);--gf-local-color:var(--gf-ctrl-btn-color-secondary)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-secondary)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-hover-secondary);--gf-local-border-color:var(--gf-ctrl-btn-border-color-hover-secondary);--gf-local-color:var(--gf-ctrl-btn-color-hover-secondary)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:hover::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-hover-secondary)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-focus-secondary);--gf-local-border-color:var(--gf-ctrl-btn-border-color-focus-secondary);--gf-local-color:var(--gf-ctrl-btn-color-focus-secondary)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:focus::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-focus-secondary)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-disabled-secondary);--gf-local-border-color:var(--gf-ctrl-btn-border-color-disabled-secondary);--gf-local-color:var(--gf-ctrl-btn-color-disabled-secondary)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--secondary:disabled::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-disabled-secondary)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-ctrl);--gf-local-border-color:var(--gf-ctrl-btn-border-color-ctrl);--gf-local-border-style:var(--gf-ctrl-btn-border-style-ctrl);--gf-local-border-width:var(--gf-ctrl-btn-border-width-ctrl);--gf-local-color:var(--gf-ctrl-btn-color-ctrl)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-ctrl)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-hover-ctrl);--gf-local-border-color:var(--gf-ctrl-btn-border-color-hover-ctrl);--gf-local-color:var(--gf-ctrl-btn-color-hover-ctrl)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:hover::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-hover-ctrl)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-focus-ctrl);--gf-local-border-color:var(--gf-ctrl-btn-border-color-focus-ctrl);--gf-local-color:var(--gf-ctrl-btn-color-focus-ctrl)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:focus::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-focus-ctrl)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-disabled-ctrl);--gf-local-border-color:var(--gf-ctrl-btn-border-color-disabled-ctrl);--gf-local-color:var(--gf-ctrl-btn-color-disabled-ctrl)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--control:disabled::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-disabled-ctrl)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-simple);--gf-local-border-color:var(--gf-ctrl-btn-border-color-simple);--gf-local-border-width:var(--gf-ctrl-btn-border-width-simple);--gf-local-shadow:var(--gf-ctrl-btn-shadow-simple);--gf-local-color:var(--gf-ctrl-btn-color-simple);--gf-local-column-gap:0;--gf-local-line-height:var(--gf-ctrl-btn-size-simple);--gf-local-min-height:var(--gf-ctrl-btn-size-simple);--gf-local-min-width:var(--gf-ctrl-btn-size-simple);--gf-local-padding-y:0;--gf-local-padding-x:0}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-simple)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-hover-simple);--gf-local-border-color:var(--gf-ctrl-btn-border-color-hover-simple);--gf-local-shadow:var(--gf-ctrl-btn-shadow-hover-simple);--gf-local-color:var(--gf-ctrl-btn-color-hover-simple)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:hover::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-hover-simple)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-focus-simple);--gf-local-border-color:var(--gf-ctrl-btn-border-color-focus-simple);--gf-local-shadow:var(--gf-ctrl-btn-shadow-focus-simple);--gf-local-color:var(--gf-ctrl-btn-color-focus-simple)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:focus::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-focus-simple)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled{--gf-local-bg-color:var(--gf-ctrl-btn-bg-color-disabled-simple);--gf-local-border-color:var(--gf-ctrl-btn-border-color-disabled-simple);--gf-local-shadow:var(--gf-ctrl-btn-shadow-disabled-simple);--gf-local-color:var(--gf-ctrl-btn-color-disabled-simple)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled .dashicons::before,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::after,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--simple:disabled::before{--gf-local-color:var(--gf-ctrl-btn-icon-color-disabled-simple)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xs,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xs,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xs,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xs,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xs,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xs,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xs{--gf-local-font-size:var(--gf-ctrl-btn-font-size-xs);--gf-local-min-height:var(--gf-ctrl-btn-size-xs);--gf-local-padding-x:var(--gf-ctrl-btn-padding-x-xs)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-sm,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-sm,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-sm,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-sm,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-sm,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-sm,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-sm{--gf-local-font-size:var(--gf-ctrl-btn-font-size-sm);--gf-local-min-height:var(--gf-ctrl-btn-size-sm);--gf-local-padding-x:var(--gf-ctrl-btn-padding-x-sm)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-md,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-md,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-md,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-md,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-md,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-md,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-md{--gf-local-font-size:var(--gf-ctrl-btn-font-size-md);--gf-local-min-height:var(--gf-ctrl-btn-size-md);--gf-local-padding-x:var(--gf-ctrl-btn-padding-x-md)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-lg,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-lg,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-lg,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-lg,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-lg,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-lg,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-lg{--gf-local-font-size:var(--gf-ctrl-btn-font-size-lg);--gf-local-min-height:var(--gf-ctrl-btn-size-lg);--gf-local-padding-x:var(--gf-ctrl-btn-padding-x-lg)}.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xl,.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xl,.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn))>button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xl,.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xl,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xl,.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit],[type=button],[type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xl,.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)).gform-theme-button--size-xl{--gf-local-font-size:var(--gf-ctrl-btn-font-size-xl);--gf-local-min-height:var(--gf-ctrl-btn-size-xl);--gf-local-padding-x:var(--gf-ctrl-btn-padding-x-xl)}.gform-theme--framework input[type=file]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-padding-x:var(--gf-ctrl-file-padding-x);text-overflow:ellipsis;white-space:nowrap}.gform-theme--framework input[type=file]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover::file-selector-button{--gf-local-bg-color:var(--gf-ctrl-file-btn-bg-color-hover);--gf-local-border-inline-end-color:var(--gf-ctrl-file-btn-border-inline-end-color-hover);--gf-local-color:var(--gf-ctrl-file-btn-color-hover)}.gform-theme--framework input[type=file]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):focus::file-selector-button{--gf-local-bg-color:var(--gf-ctrl-file-btn-bg-color-focus);--gf-local-border-inline-end-color:var(--gf-ctrl-file-btn-border-inline-end-color-focus);--gf-local-color:var(--gf-ctrl-file-btn-color-focus)}.gform-theme--framework input[type=file]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):disabled::file-selector-button{--gf-local-bg-color:var(--gf-ctrl-file-btn-bg-color-disabled);--gf-local-border-inline-end-color:var(--gf-ctrl-file-btn-border-inline-end-color-disabled);--gf-local-color:var(--gf-ctrl-file-btn-color-disabled)}.gform-theme--framework input[type=file]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *))::file-selector-button{--gf-local-bg-color:var(--gf-ctrl-file-btn-bg-color);--gf-local-border-inline-end-color:var(--gf-ctrl-file-btn-border-inline-end-color);--gf-local-border-inline-end-style:var(--gf-ctrl-file-btn-border-inline-end-style);--gf-local-border-inline-end-width:var(--gf-ctrl-file-btn-border-inline-end-width);--gf-local-border-inline-end:var(--gf-local-border-inline-end-width) var(--gf-local-border-inline-end-style) var(--gf-local-border-inline-end-color);--gf-local-color:var(--gf-ctrl-file-btn-color);background-color:var(--gf-local-bg-color);block-size:100%;border:0;border-end-start-radius:var(--gf-ctrl-file-btn-radius);border-inline-end:var(--gf-local-border-inline-end);border-start-start-radius:var(--gf-ctrl-file-btn-radius);color:var(--gf-local-color);font-family:var(--gf-ctrl-file-btn-font-family);font-size:var(--gf-ctrl-file-btn-font-size);font-style:var(--gf-ctrl-file-btn-font-style);font-weight:var(--gf-ctrl-file-btn-font-weight);inset-block-start:calc(var(--gf-ctrl-border-width) * -1);letter-spacing:var(--gf-ctrl-file-btn-letter-spacing);line-height:var(--gf-ctrl-file-btn-line-height);margin-inline:var(--gf-ctrl-file-btn-margin-x);padding-block:0;padding-inline:var(--gf-ctrl-file-btn-padding-x);position:relative;-webkit-text-decoration:var(--gf-ctrl-file-btn-text-decoration);text-decoration:var(--gf-ctrl-file-btn-text-decoration);text-transform:var(--gf-ctrl-file-btn-text-transform);transition:var(--gf-ctrl-file-btn-transition)}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .gform_drop_area{--gf-local-height:var(--gf-ctrl-file-zone-height);--gf-local-radius:var(--gf-ctrl-file-zone-radius);--gf-local-border-style:var(--gf-ctrl-file-zone-border-style);--gf-local-color:var(--gf-ctrl-file-zone-color);--gf-local-font-weight:var(--gf-ctrl-file-zone-font-weight);--gf-local-line-height:var(--gf-ctrl-file-zone-line-height);--gf-local-padding-x:var(--gf-ctrl-file-zone-padding-x);--gf-local-padding-y:var(--gf-ctrl-file-zone-padding-y)}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .gform_drop_area::before{font-family:var(--gf-icon-font-family)!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:never;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;color:var(--gf-ctrl-file-zone-icon-color);content:var(--gf-icon-ctrl-file);display:block;font-size:var(--gf-ctrl-file-zone-icon-font-size);margin-block-end:var(--gf-ctrl-file-zone-icon-margin-y-end)}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .gform_drop_instructions{margin-block-end:var(--gf-ctrl-file-zone-instructions-margin-y-end)}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview_list{display:flex;flex-direction:column;gap:var(--gf-ctrl-file-prev-area-gap);margin-block-start:var(--gf-ctrl-file-prev-area-margin-y-start);width:100%}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview_list:empty{margin-block-start:0}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview{display:flex;flex-direction:column;font-family:var(--gf-ctrl-file-prev-font-family);font-size:var(--gf-ctrl-file-prev-font-size);font-style:var(--gf-ctrl-file-prev-font-style);font-weight:var(--gf-ctrl-file-prev-font-weight);gap:var(--gf-ctrl-file-prev-gap);letter-spacing:var(--gf-ctrl-file-prev-letter-spacing);line-height:var(--gf-ctrl-file-prev-line-height);position:relative}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gfield_fileupload_filename{color:var(--gf-ctrl-file-prev-name-color);line-height:var(--gf-ctrl-file-prev-name-line-height);overflow:var(--gf-ctrl-file-prev-name-overflow);padding-inline-end:var(--gf-ctrl-file-prev-name-padding-x-end);text-overflow:var(--gf-ctrl-file-prev-name-text-overflow);white-space:var(--gf-ctrl-file-prev-name-white-space)}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gfield_fileupload_filesize{color:var(--gf-ctrl-file-prev-size-color)}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gfield_fileupload_progress{align-items:center;display:flex;gap:var(--gf-ctrl-file-prog-ui-gap)}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gfield_fileupload_progress::after{font-family:var(--gf-icon-font-family)!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:never;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;block-size:var(--gf-ctrl-file-prog-btn-icon-size);color:var(--gf-ctrl-file-prog-btn-icon-color-complete);content:var(--gf-icon-ctrl-file-completed);font-size:var(--gf-ctrl-file-prog-btn-icon-size);inline-size:var(--gf-ctrl-file-prog-btn-icon-size);min-inline-size:var(--gf-ctrl-file-prog-btn-icon-size);opacity:0}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gfield_fileupload_progress_complete::after{opacity:1}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gfield_fileupload_progressbar,.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gfield_fileupload_progressbar_progress{block-size:var(--gf-ctrl-file-prog-bar-height);border-radius:var(--gf-ctrl-file-prog-bar-radius)}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gfield_fileupload_progressbar{background-color:var(--gf-ctrl-file-prog-bar-bg-color);inline-size:100%;position:relative}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gfield_fileupload_progressbar_progress{background-color:var(--gf-ctrl-file-prog-bar-bg-color-loading);inline-size:1%;position:absolute;transition:var(--gf-ctrl-file-prog-bar-transition)}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gfield_fileupload_percent{color:var(--gf-ctrl-file-prog-text-color);font-size:var(--gf-ctrl-file-prog-text-font-size);min-inline-size:var(--gf-ctrl-file-prog-text-min-width)}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gfield_fileupload_cancel,.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gform_delete_file{inset-block-start:var(--gf-ctrl-file-prog-btn-inset-y-start);inset-inline-end:var(--gf-ctrl-file-prog-btn-inset-x-end);position:var(--gf-ctrl-file-prog-btn-position)}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gform_delete_file .dashicons::before{--gf-ctrl-btn-icon:var(--gf-icon-ctrl-file-remove)}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gfield_fileupload_cancel{--gf-local-font-size:var(--gf-ctrl-file-prog-btn-font-size-cancel)}.gform-theme--framework .gfield:where(.gfield--type-fileupload,.gfield--input-type-fileupload) .ginput_preview .gfield_fileupload_cancel::before{--gf-ctrl-btn-icon:var(--gf-icon-ctrl-file-cancel)}.gform-theme--framework.ui-datepicker{background-color:var(--gf-ctrl-date-picker-bg-color);border-radius:var(--gf-ctrl-date-picker-radius);box-shadow:var(--gf-ctrl-date-picker-shadow);display:none;inline-size:var(--gf-ctrl-date-picker-width);margin-block-start:var(--gf-ctrl-date-picker-margin-y-start);padding-block:var(--gf-ctrl-date-picker-padding-y);padding-inline:var(--gf-ctrl-date-picker-padding-x)}@media (min-width:640px){.gform-theme--framework.ui-datepicker{inline-size:var(--gf-ctrl-date-picker-width-viewport-sm);padding-block:var(--gf-ctrl-date-picker-padding-y-viewport-sm);padding-inline:var(--gf-ctrl-date-picker-padding-x-viewport-sm)}}.gform-theme--framework.ui-datepicker .ui-datepicker-header{align-items:stretch;display:flex;margin-block:unset;margin-inline:unset;padding-block:0;padding-inline:0;position:unset}.gform-theme--framework.ui-datepicker .ui-datepicker-next,.gform-theme--framework.ui-datepicker .ui-datepicker-prev{align-items:center;block-size:auto;cursor:pointer;display:flex;flex:none;inline-size:var(--gf-ctrl-date-picker-header-icons-width);inset:unset;justify-content:center;position:relative}.gform-theme--framework.ui-datepicker .ui-datepicker-next::before,.gform-theme--framework.ui-datepicker .ui-datepicker-prev::before{--gf-local-color:var(--gf-ctrl-date-picker-header-icons-color);font-family:var(--gf-icon-font-family)!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:never;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;background-image:none;border:none;color:var(--gf-local-color);font-size:var(--gf-ctrl-date-picker-header-icons-font-size);inset:unset;transform:unset}.gform-theme--framework.ui-datepicker .ui-datepicker-prev::before{content:var(--gf-icon-ctrl-datepicker-left)}.gform-theme--framework.ui-datepicker .ui-datepicker-next{order:1}.gform-theme--framework.ui-datepicker .ui-datepicker-next::before{content:var(--gf-icon-ctrl-datepicker-right)}.gform-theme--framework.ui-datepicker .ui-datepicker-next-hover::before,.gform-theme--framework.ui-datepicker .ui-datepicker-prev-hover::before{--gf-local-color:var(--gf-ctrl-date-picker-header-icons-color-hover)}.gform-theme--framework.ui-datepicker .ui-datepicker-title{align-items:center;color:var(--gf-ctrl-date-picker-title-color);display:flex;flex:auto;font-size:var(--gf-ctrl-date-picker-title-font-size);font-weight:var(--gf-ctrl-date-picker-title-font-weight);gap:var(--gf-ctrl-date-picker-title-gap);line-height:var(--gf-ctrl-date-picker-title-line-height);margin-block:0;margin-inline:var(--gf-ctrl-date-picker-title-margin-x)}@media (min-width:640px){.gform-theme--framework.ui-datepicker .ui-datepicker-title{font-size:var(--gf-ctrl-date-picker-title-font-size-viewport-sm);gap:var(--gf-ctrl-date-picker-title-gap-viewport-sm);margin-inline:var(--gf-ctrl-date-picker-title-margin-x-viewport-sm)}}.gform-theme--framework.ui-datepicker select.ui-datepicker-month,.gform-theme--framework.ui-datepicker select.ui-datepicker-year{--gf-local-height:var(--gf-ctrl-size-sm);--gf-local-line-height:var(--gf-ctrl-size-sm);background-image:var(--gf-icon-ctrl-select);background-position:var(--gf-ctrl-select-icon-position);background-repeat:no-repeat;background-size:var(--gf-ctrl-select-icon-size);box-shadow:var(--gf-local-shadow);flex:auto;inline-size:auto;margin:0;padding-inline:var(--gf-local-padding-x);text-align:var(--gf-ctrl-date-picker-dropdown-text-align)}.gform-theme--framework.ui-datepicker table{margin-block-end:0;margin-block-start:16px}.gform-theme--framework.ui-datepicker table td,.gform-theme--framework.ui-datepicker table th{block-size:var(--gf-ctrl-date-picker-cell-height);color:var(--gf-ctrl-date-picker-cell-content-color);padding-block:var(--gf-ctrl-date-picker-cell-padding);padding-inline:var(--gf-ctrl-date-picker-cell-padding)}@media (min-width:640px){.gform-theme--framework.ui-datepicker table td,.gform-theme--framework.ui-datepicker table th{block-size:var(--gf-ctrl-date-picker-cell-height-viewport-sm)}}.gform-theme--framework.ui-datepicker table th{font-size:var(--gf-ctrl-date-picker-head-cell-font-size);font-weight:var(--gf-ctrl-date-picker-head-cell-font-weight);line-height:var(--gf-ctrl-date-picker-head-cell-line-height);vertical-align:middle}.gform-theme--framework.ui-datepicker table td{font-size:var(--gf-ctrl-date-picker-cell-font-size);font-weight:var(--gf-ctrl-date-picker-cell-font-weight);line-height:var(--gf-ctrl-date-picker-cell-line-height);padding-block:var(--gf-ctrl-date-picker-cell-padding-y)}@media (min-width:640px){.gform-theme--framework.ui-datepicker table td{padding-block:var(--gf-ctrl-date-picker-cell-padding-y-viewport-sm)}}.gform-theme--framework.ui-datepicker table td:not(.ui-state-disabled){cursor:pointer}.gform-theme--framework.ui-datepicker table td a,.gform-theme--framework.ui-datepicker table td span{--gf-local-bg-color:transparent;--gf-local-border:0;--gf-local-color:var(--gf-ctrl-date-picker-cell-content-color);align-items:center;background-color:var(--gf-local-bg-color);block-size:100%;border:var(--gf-local-border);border-radius:var(--gf-ctrl-date-picker-cell-content-radius);color:var(--gf-local-color);display:flex;inline-size:var(--gf-ctrl-date-picker-cell-content-width);justify-content:center;text-decoration:none}@media (min-width:640px){.gform-theme--framework.ui-datepicker table td a,.gform-theme--framework.ui-datepicker table td span{inline-size:var(--gf-ctrl-date-picker-cell-content-width-viewport-sm)}}.gform-theme--framework.ui-datepicker table td:not(.ui-state-disabled):not(.ui-datepicker-current-day) a:hover{--gf-local-bg-color:var(--gf-ctrl-date-picker-cell-content-bg-color-hover);--gf-local-color:var(--gf-ctrl-date-picker-cell-content-color-hover)}.gform-theme--framework.ui-datepicker table td:not(.ui-state-disabled):not(.ui-datepicker-current-day) a:focus{--gf-local-border:var(--gf-ctrl-date-picker-cell-content-border)}.gform-theme--framework.ui-datepicker table .ui-state-disabled a,.gform-theme--framework.ui-datepicker table .ui-state-disabled span{--gf-local-bg-color:var(--gf-ctrl-date-picker-cell-content-bg-color-disabled);--gf-local-color:var(--gf-ctrl-date-picker-cell-content-color-disabled)}.gform-theme--framework.ui-datepicker table .ui-datepicker-today a,.gform-theme--framework.ui-datepicker table .ui-datepicker-today span{--gf-local-bg-color:var(--gf-ctrl-date-picker-cell-content-bg-color-hover);--gf-local-color:var(--gf-ctrl-date-picker-cell-content-color-hover)}.gform-theme--framework.ui-datepicker table .ui-datepicker-today.ui-state-disabled a,.gform-theme--framework.ui-datepicker table .ui-datepicker-today.ui-state-disabled span{--gf-local-bg-color:var(--gf-ctrl-date-picker-cell-content-bg-color-disabled);--gf-local-color:var(--gf-ctrl-date-picker-cell-content-color-disabled)}.gform-theme--framework.ui-datepicker table .ui-datepicker-current-day a,.gform-theme--framework.ui-datepicker table .ui-datepicker-current-day span{--gf-local-bg-color:var(--gf-ctrl-date-picker-cell-content-bg-color-selected);--gf-local-color:var(--gf-ctrl-date-picker-cell-content-color-selected)}.gform-theme--framework.ui-datepicker table .ui-datepicker-current-day a:focus,.gform-theme--framework.ui-datepicker table .ui-datepicker-current-day a:hover{--gf-local-color:var(--gf-ctrl-date-picker-cell-content-color-selected)}.gform-theme--framework.ui-datepicker table .ui-datepicker-current-day.ui-state-disabled a,.gform-theme--framework.ui-datepicker table .ui-datepicker-current-day.ui-state-disabled span{--gf-local-bg-color:var(--gf-ctrl-date-picker-cell-content-bg-color-disabled);--gf-local-color:var(--gf-ctrl-date-picker-cell-content-color-disabled)}.gform-theme--framework.ui-datepicker.ui-datepicker-rtl select.ui-datepicker-month,.gform-theme--framework.ui-datepicker.ui-datepicker-rtl select.ui-datepicker-year{background-position:var(--gf-ctrl-select-icon-position)}.gform-theme--framework.ui-datepicker.ui-datepicker-rtl .ui-datepicker-next::before,.gform-theme--framework.ui-datepicker.ui-datepicker-rtl .ui-datepicker-prev::before{transform:none}.gform-theme--framework.ui-datepicker.ui-datepicker-rtl .ui-datepicker-prev::before{content:var(--gf-icon-ctrl-datepicker-right)}.gform-theme--framework.ui-datepicker.ui-datepicker-rtl .ui-datepicker-next::before{content:var(--gf-icon-ctrl-datepicker-left)}.gform-theme--framework .gfield:where(:not(.gfield--type-html):not(.gfield--type-section)){line-height:1}.gform-theme--framework .gfield--type-address .copy_values_option_container{display:inline-grid;gap:var(--gf-field-choice-meta-space) 0;grid-template-columns:minmax(var(--gf-ctrl-choice-size),max-content) auto;margin-block-end:var(--gf-field-gap-y)}.gform-theme--framework .gfield--type-choice .gfield_checkbox,.gform-theme--framework .gfield--type-choice .gfield_radio{display:flex;flex-direction:column;gap:var(--gf-field-choice-gap)}.gform-theme--framework .gfield--type-choice.gfield--choice-align-horizontal .gfield_checkbox,.gform-theme--framework .gfield--type-choice.gfield--choice-align-horizontal .gfield_radio{flex-direction:row;flex-wrap:wrap;gap:var(--gf-field-choice-align-x-gap-y) var(--gf-field-choice-align-x-gap-x)}.gform-theme--framework .gfield--type-choice .gchoice,.gform-theme--framework .gfield--type-choice .ginput_container_consent{display:inline-grid;gap:var(--gf-field-choice-meta-space) 0;grid-template-columns:minmax(var(--gf-ctrl-choice-size),max-content) auto}.gform-theme--framework .gfield--type-choice .gchoice br,.gform-theme--framework .gfield--type-choice .ginput_container_consent br{display:none}.gform-theme--framework .gfield--type-choice .gchoice_other_control{--gf-local-width:auto;grid-column:span 2;max-inline-size:var(--gf-field-choice-other-ctrl-max-width)}.gform-theme--framework .gfield--type-choice .gfield-choice-toggle-all{align-self:self-start;inline-size:100%}.gform-theme--framework .gfield--type-choice .gfield_choice_all_toggle{justify-self:start}.gform-theme--framework .gfield--type-image_choice .gfield_checkbox,.gform-theme--framework .gfield--type-image_choice .gfield_radio{flex-direction:row;flex-wrap:wrap;gap:var(--gf-field-img-choice-gap)}.gform-theme--framework .gfield--type-image_choice .gchoice{align-self:start;display:block;inline-size:var(--gf-field-img-choice-size);max-inline-size:var(--gf-field-img-choice-size);min-inline-size:var(--gf-field-img-choice-size);position:relative}.gform-theme--framework .gfield--type-image_choice .gchoice:where([\:has\(input\:checked\)])::after{font-family:var(--gf-icon-font-family)!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:never;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;align-items:center;block-size:var(--gf-field-img-choice-check-ind-size);border-radius:var(--gf-field-img-choice-check-ind-radius);content:var(--gf-field-img-choice-check-ind-icon);display:flex;filter:var(--gf-field-img-choice-check-ind-shadow);font-size:var(--gf-field-img-choice-check-ind-icon-size);inline-size:var(--gf-field-img-choice-check-ind-size);justify-content:center;position:absolute;z-index:5}.gform-theme--framework .gfield--type-image_choice .gchoice:where([\:has\(input\:checked\)])::after{font-family:var(--gf-icon-font-family)!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:never;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;align-items:center;block-size:var(--gf-field-img-choice-check-ind-size);border-radius:var(--gf-field-img-choice-check-ind-radius);content:var(--gf-field-img-choice-check-ind-icon);display:flex;filter:var(--gf-field-img-choice-check-ind-shadow);font-size:var(--gf-field-img-choice-check-ind-icon-size);inline-size:var(--gf-field-img-choice-check-ind-size);justify-content:center;position:absolute;z-index:5}.gform-theme--framework .gfield--type-image_choice .gchoice:where(:has(input:checked))::after{font-family:var(--gf-icon-font-family)!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:never;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;align-items:center;block-size:var(--gf-field-img-choice-check-ind-size);border-radius:var(--gf-field-img-choice-check-ind-radius);content:var(--gf-field-img-choice-check-ind-icon);display:flex;filter:var(--gf-field-img-choice-check-ind-shadow);font-size:var(--gf-field-img-choice-check-ind-icon-size);inline-size:var(--gf-field-img-choice-check-ind-size);justify-content:center;position:absolute;z-index:5}.gform-theme--framework .gfield--type-image_choice .gfield-image-choice-wrapper-outer{display:block;min-block-size:100%}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice,.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-no-card .gfield-choice-image-wrapper{--gf-local-border-color:transparent;--gf-local-border-width:var(--gf-ctrl-border-width);--gf-local-border-style:var(--gf-ctrl-border-style);--gf-local-border-block-start:var(--gf-local-border-width) var(--gf-local-border-style) var(--gf-local-border-color);--gf-local-border-block-end:var(--gf-local-border-width) var(--gf-local-border-style) var(--gf-local-border-color);--gf-local-border-inline-start:var(--gf-local-border-width) var(--gf-local-border-style) var(--gf-local-border-color);--gf-local-border-inline-end:var(--gf-local-border-width) var(--gf-local-border-style) var(--gf-local-border-color);--gf-local-outline-color:var(--gf-ctrl-outline-color);--gf-local-outline-width:var(--gf-ctrl-outline-width);--gf-local-shadow:none;box-shadow:var(--gf-local-shadow);outline-color:var(--gf-local-outline-color);outline-offset:var(--gf-ctrl-outline-offset);outline-style:var(--gf-ctrl-outline-style);outline-width:var(--gf-local-outline-width);transition:var(--gf-transition-ctrl)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice,.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-no-card .gfield-choice-image-wrapper::before{border-block-end:var(--gf-local-border-block-end);border-block-start:var(--gf-local-border-block-start);border-inline-end:var(--gf-local-border-inline-end);border-inline-start:var(--gf-local-border-inline-start);border-radius:var(--gf-ctrl-radius)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice{--gf-local-bg-color:var(--gf-ctrl-bg-color);--gf-local-border-color:var(--gf-color-in-ctrl-light-darker);--gf-local-shadow:var(--gf-field-img-choice-shadow);background-color:var(--gf-local-bg-color)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice .gform-field-label{--gf-local-color:var(--gf-ctrl-color);color:var(--gf-local-color)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:hover{--gf-local-bg-color:var(--gf-ctrl-bg-color-hover)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:hover .gform-field-label{--gf-local-color:var(--gf-ctrl-color-hover)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:focus-within{--gf-local-bg-color:var(--gf-ctrl-bg-color-focus)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:focus-within .gform-field-label{--gf-local-color:var(--gf-ctrl-color-focus)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice[\:has\(.gfield-choice-input\:disabled\)]{--gf-local-bg-color:var(--gf-ctrl-bg-color-disabled);--gf-local-border-color:var(--gf-ctrl-border-color-disabled)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:has(.gfield-choice-input:disabled){--gf-local-bg-color:var(--gf-ctrl-bg-color-disabled);--gf-local-border-color:var(--gf-ctrl-border-color-disabled)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice[\:has\(.gfield-choice-input\:disabled\)] .gform-field-label{--gf-local-color:var(--gf-ctrl-color-disabled)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:has(.gfield-choice-input:disabled) .gform-field-label{--gf-local-color:var(--gf-ctrl-color-disabled)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:where([\:has\(input\:checked\)])::after{background-color:var(--gf-field-img-choice-card-check-ind-bg-color);color:var(--gf-field-img-choice-card-check-ind-icon-color)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:where(:has(input:checked))::after{background-color:var(--gf-field-img-choice-card-check-ind-bg-color);color:var(--gf-field-img-choice-card-check-ind-icon-color)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card.gfield_error .gchoice{--gf-local-bg-color:var(--gf-ctrl-bg-color-error)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card.gfield_error .gchoice .gform-field-label{--gf-local-color:var(--gf-ctrl-color-error)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gfield-image-choice-wrapper-outer{padding-block:var(--gf-field-img-choice-card-space);padding-inline:var(--gf-field-img-choice-card-space)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gfield-choice-image-no-image{background-color:var(--gf-field-img-choice-card-placeholder-bg-color);color:var(--gf-field-img-choice-card-placeholder-color)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-no-card .gchoice:where([\:has\(input\:checked\)])::after{background-color:var(--gf-field-img-choice-no-card-check-ind-bg-color);color:var(--gf-field-img-choice-no-card-check-ind-icon-color)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-no-card .gchoice:where(:has(input:checked))::after{background-color:var(--gf-field-img-choice-no-card-check-ind-bg-color);color:var(--gf-field-img-choice-no-card-check-ind-icon-color)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-no-card .gfield-choice-image-wrapper::before{content:"";display:block;inset-block:0;inset-inline:0;position:absolute;transition:var(--gf-transition-ctrl);z-index:1}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-no-card .gfield-choice-image-no-image{background-color:var(--gf-field-img-choice-no-card-placeholder-bg-color);color:var(--gf-field-img-choice-no-card-placeholder-color)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:hover,.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-no-card .gchoice:hover .gfield-choice-image-wrapper{--gf-local-shadow:var(--gf-field-img-choice-shadow-hover)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:focus-within,.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-no-card .gchoice:focus-within .gfield-choice-image-wrapper{--gf-local-border-color:var(--gf-ctrl-border-color-focus);--gf-local-outline-color:var(--gf-ctrl-outline-color-focus);--gf-local-outline-width:var(--gf-ctrl-outline-width-focus)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice[\:has\(.gfield-choice-input\:disabled\)],.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-no-card .gchoice[\:has\(.gfield-choice-input\:disabled\)] .gfield-choice-image-wrapper{--gf-local-shadow:none}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:has(.gfield-choice-input:disabled),.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-appearance-no-card .gchoice:has(.gfield-choice-input:disabled) .gfield-choice-image-wrapper{--gf-local-shadow:none}.gform-theme--framework .gfield--type-image_choice .gchoice[\:has\(.gfield-choice-input\:disabled\)] .gfield-choice-image-wrapper{opacity:var(--gf-field-img-choice-ctrl-opacity-disabled)}.gform-theme--framework .gfield--type-image_choice .gchoice:has(.gfield-choice-input:disabled) .gfield-choice-image-wrapper{opacity:var(--gf-field-img-choice-ctrl-opacity-disabled)}.gform-theme--framework .gfield--type-image_choice.gfield_error.gfield--image-choice-appearance-card .gchoice,.gform-theme--framework .gfield--type-image_choice.gfield_error.gfield--image-choice-appearance-no-card .gfield-choice-image-wrapper{--gf-local-border-color:var(--gf-ctrl-border-color-error)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-style-square .gfield-choice-image-wrapper{border-radius:var(--gf-field-img-choice-radius-square)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-style-square .gfield-choice-image-wrapper::before{border-radius:var(--gf-field-img-choice-radius-square)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-style-square.gfield--image-choice-appearance-card .gchoice:where([\:has\(input\:checked\)])::after{inset-block-start:calc(var(--gf-field-img-choice-card-space) + 10px);inset-inline-end:calc(var(--gf-field-img-choice-card-space) + 10px)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-style-square.gfield--image-choice-appearance-card .gchoice:where(:has(input:checked))::after{inset-block-start:calc(var(--gf-field-img-choice-card-space) + 10px);inset-inline-end:calc(var(--gf-field-img-choice-card-space) + 10px)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-style-square.gfield--image-choice-appearance-no-card .gchoice:where([\:has\(input\:checked\)])::after{inset-block-start:10px;inset-inline-end:10px}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-style-square.gfield--image-choice-appearance-no-card .gchoice:where(:has(input:checked))::after{inset-block-start:10px;inset-inline-end:10px}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-style-circle .gfield-choice-image-wrapper{border-radius:var(--gf-field-img-choice-radius-round)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-style-circle .gfield-choice-image-wrapper::before{border-radius:var(--gf-field-img-choice-radius-round)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-style-circle.gfield--image-choice-appearance-card .gchoice:where([\:has\(input\:checked\)])::after{inset-block-start:var(--gf-field-img-choice-card-space);inset-inline-end:var(--gf-field-img-choice-card-space)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-style-circle.gfield--image-choice-appearance-card .gchoice:where(:has(input:checked))::after{inset-block-start:var(--gf-field-img-choice-card-space);inset-inline-end:var(--gf-field-img-choice-card-space)}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-style-circle.gfield--image-choice-appearance-no-card .gchoice:where([\:has\(input\:checked\)])::after{inset-block-start:0;inset-inline-end:0}.gform-theme--framework .gfield--type-image_choice.gfield--image-choice-style-circle.gfield--image-choice-appearance-no-card .gchoice:where(:has(input:checked))::after{inset-block-start:0;inset-inline-end:0}.gform-theme--framework .gfield--type-image_choice .gfield-choice-image-wrapper{aspect-ratio:var(--gf-field-img-choice-aspect-ratio);margin-block-end:var(--gf-field-img-choice-margin-y-end);opacity:var(--gf-field-img-choice-ctrl-opacity);overflow:hidden;position:relative}.gform-theme--framework .gfield--type-image_choice .gfield-choice-image-no-image{block-size:100%;display:block;inline-size:100%;position:relative}.gform-theme--framework .gfield--type-image_choice .gfield-choice-image-no-image::before{font-family:var(--gf-icon-font-family)!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:never;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;content:var(--gf-icon-ctrl-img-choice-placeholder);font-size:var(--gf-field-img-choice-placeholder-icon-font-size);inset-block-start:50%;left:50%;position:absolute;transform:translate(-50%,-50%)}.gform-theme--framework .gfield--type-image_choice .gfield-choice-image-no-image span{border:0;clip:rect(0,0,0,0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}.gform-theme--framework .gfield--type-image_choice .gfield-choice-image{block-size:100%!important;display:block;inline-size:100%;max-block-size:100%;max-inline-size:100%;-o-object-fit:cover;object-fit:cover}.gform-theme--framework .gfield--type-image_choice .gfield-image-choice-wrapper-inner{display:inline-grid;gap:var(--gf-field-choice-meta-space) 0;grid-template-columns:minmax(var(--gf-ctrl-choice-size),max-content) auto}.gform-theme--framework .gfield--type-image_choice .ginput_container_image_choice--label-hide .gchoice .gfield-choice-image-wrapper{margin-block-end:0}.gform-theme--framework .gfield--type-image_choice .ginput_container_image_choice--label-hide .gchoice .gfield-choice-input,.gform-theme--framework .gfield--type-image_choice .ginput_container_image_choice--label-hide .gchoice .gfield-image-choice-wrapper-inner,.gform-theme--framework .gfield--type-image_choice .ginput_container_image_choice--label-hide .gchoice .gform-field-label{border:0;clip:rect(0,0,0,0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}.gform-theme--framework .gfield--type-image_choice .ginput_container_image_choice--input-hide .gchoice .gform-field-label{--gf-local-margin-x:0}.gform-theme--framework .gfield--type-image_choice .ginput_container_image_choice--input-hide .gchoice .gfield-choice-input{border:0;clip:rect(0,0,0,0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}.gform-theme--framework .gfield--type-image_choice .gchoice_other_control{margin-block-start:var(--gf-field-img-choice-other-ctrl-margin-y-start);max-inline-size:100%}.gform-theme--framework .gfield--input-type-datepicker .ginput_container_date{align-content:flex-start;align-items:center;display:flex}.gform-theme--framework .gfield--input-type-datepicker .ginput_container_date input{--gf-local-width:auto}.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-custom-icon .ginput_container_date img,.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-custom-icon .ginput_container_date::after,.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-default-icon .ginput_container_date img,.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-default-icon .ginput_container_date::after{margin-inline-start:calc(var(--gf-ctrl-padding-x) * -1);pointer-events:none;transform:translateX(-100%);transition:var(--gf-field-date-icon-transition)}html[dir=rtl] .gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-custom-icon .ginput_container_date img,html[dir=rtl] .gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-custom-icon .ginput_container_date::after,html[dir=rtl] .gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-default-icon .ginput_container_date img,html[dir=rtl] .gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-default-icon .ginput_container_date::after{order:1;transform:translateX(100%)}.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-custom-icon .ginput_container_date input,.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-default-icon .ginput_container_date input{--gf-local-padding-x:var(--gf-ctrl-padding-x) var(--gf-field-date-ctrl-padding-x-end)}.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-default-icon .ginput_container_date img{display:none!important}.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-default-icon .ginput_container_date::after{font-family:var(--gf-icon-font-family)!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:never;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;color:var(--gf-field-date-icon-color);content:var(--gf-icon-ctrl-datepicker);display:inline-block;font-size:var(--gf-icon-font-size);inset-block-start:0;inset-inline-start:0}.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-default-icon .ginput_container_date:focus-within::after,.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-default-icon .ginput_container_date:where([\:has\(input\:hover\)])::after{color:var(--gf-field-date-icon-color-hover)}.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-default-icon .ginput_container_date:focus-within::after,.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-default-icon .ginput_container_date:where(:has(input:hover))::after{color:var(--gf-field-date-icon-color-hover)}.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-custom-icon .ginput_container_date img{max-block-size:var(--gf-field-date-custom-icon-max-height);max-inline-size:var(--gf-field-date-custom-icon-max-width);opacity:var(--gf-field-date-custom-icon-opacity)}.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-custom-icon .ginput_container_date:focus-within img,.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-custom-icon .ginput_container_date:where([\:has\(input\:hover\)]) img{opacity:var(--gf-field-date-custom-icon-opacity-hover)}.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-custom-icon .ginput_container_date:focus-within img,.gform-theme--framework .gfield--input-type-datepicker.gfield--datepicker-custom-icon .ginput_container_date:where(:has(input:hover)) img{opacity:var(--gf-field-date-custom-icon-opacity-hover)}.gform-theme--framework.gform-theme.gform_wrapper .gfield:where(.gfield--type-list,.gfield--input-type-list) button.add_list_item,.gform-theme--framework.gform-theme.gform_wrapper .gfield:where(.gfield--type-list,.gfield--input-type-list) button.delete_list_item{--gf-local-height:var(--gf-field-list-btn-size);--gf-local-radius:var(--gf-field-list-btn-radius);--gf-local-column-gap:0;--gf-local-font-size:var(--gf-field-list-btn-font-size);--gf-local-letter-spacing:0;--gf-local-line-height:1;--gf-local-min-height:auto;--gf-local-padding-y:var(--gf-field-list-btn-padding-y);--gf-local-padding-x:var(--gf-field-list-btn-padding-x);--gf-local-width:var(--gf-field-list-btn-size)}.gform-theme--framework.gform-theme.gform_wrapper .gfield:where(.gfield--type-list,.gfield--input-type-list) button.add_list_item::before{--gf-local-content:var(--gf-icon-ctrl-list-item-add)}.gform-theme--framework.gform-theme.gform_wrapper .gfield:where(.gfield--type-list,.gfield--input-type-list) button.delete_list_item::before{--gf-local-content:var(--gf-icon-ctrl-list-item-remove)}.gform-theme--framework .gf_page_steps,.gform-theme--framework .gf_progressbar_wrapper{margin-block-end:var(--gf-field-pg-prog-margin-y-end)}.gform-theme--framework .gf_progressbar_title,.gform-theme--framework .gf_step_label,.gform-theme--framework .gf_step_number{--gf-local-color:var(--gf-field-pg-prog-color);--gf-local-font-family:var(--gf-field-pg-prog-font-family);--gf-local-font-size:var(--gf-field-pg-prog-font-size);--gf-local-font-style:var(--gf-field-pg-prog-font-style);--gf-local-font-weight:var(--gf-field-pg-prog-font-weight);--gf-local-letter-spacing:var(--gf-field-pg-prog-letter-spacing);--gf-local-line-height:var(--gf-field-pg-prog-line-height);--gf-local-text-transform:none;color:var(--gf-local-color);font-family:var(--gf-local-font-family);font-size:var(--gf-local-font-size);font-style:var(--gf-local-font-style);font-weight:var(--gf-local-font-weight);letter-spacing:var(--gf-local-letter-spacing);line-height:var(--gf-local-line-height);text-transform:var(--gf-local-text-transform)}.gform-theme--framework .gf_progressbar_title{margin-block-end:var(--gf-field-pg-prog-title-margin-y-end)}.gform-theme--framework .gf_progressbar{background-color:var(--gf-field-pg-prog-bar-bg-color);border-radius:var(--gf-field-pg-prog-bar-radius)}.gform-theme--framework .gf_progressbar span{border:0;clip:rect(0,0,0,0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}.gform-theme--framework .gf_progressbar .gf_progressbar_percentage{block-size:var(--gf-field-pg-prog-bar-height);border-radius:var(--gf-field-pg-prog-bar-radius)}.gform-theme--framework .gf_progressbar .percentbar_blue{background-color:var(--gf-field-pg-prog-bar-bg-color-blue)}.gform-theme--framework .gf_progressbar .percentbar_gray{background-color:var(--gf-field-pg-prog-bar-bg-color-gray)}.gform-theme--framework .gf_progressbar .percentbar_green{background-color:var(--gf-field-pg-prog-bar-bg-color-green)}.gform-theme--framework .gf_progressbar .percentbar_orange{background-color:var(--gf-field-pg-prog-bar-bg-color-orange)}.gform-theme--framework .gf_progressbar .percentbar_red{background-color:var(--gf-field-pg-prog-bar-bg-color-red)}.gform-theme--framework .gf_progressbar .percentbar_spring{background:var(--gf-field-pg-prog-bar-bg-gradient-spring)}.gform-theme--framework .gf_progressbar .percentbar_blues{background:var(--gf-field-pg-prog-bar-bg-gradient-blues)}.gform-theme--framework .gf_progressbar .percentbar_rainbow{background:var(--gf-field-pg-prog-bar-bg-gradient-rainbow)}.gform-theme--framework .gf_step{align-items:center;display:flex;gap:var(--gf-field-pg-steps-step-gap);position:relative}.gform-theme--framework .gf_step_label{--gf-local-text-transform:var(--gf-field-pg-prog-text-transform)}.gform-theme--framework .gf_step_hidden{display:none}.gform-theme--framework .gf_step_number{--gf-local-bg-color:var(--gf-field-pg-steps-number-bg-color);--gf-local-border-color:var(--gf-field-pg-steps-number-border-color);--gf-local-border:var(--gf-field-pg-steps-number-border-width) var(--gf-field-pg-steps-number-border-style) var(--gf-local-border-color);--gf-local-radius:var(--gf-field-pg-steps-number-radius);--gf-local-color:var(--gf-field-pg-steps-number-color);background-color:var(--gf-local-bg-color);block-size:var(--gf-field-pg-steps-number-size);border:var(--gf-local-border);border-radius:var(--gf-local-radius);color:var(--gf-local-color);display:inline-grid;inline-size:var(--gf-field-pg-steps-number-size);min-inline-size:var(--gf-field-pg-steps-number-size);align-content:center;justify-content:center;place-content:center}.gform-theme--framework .gf_step_active .gf_step_number{--gf-local-bg-color:var(--gf-field-pg-steps-number-bg-color-active);--gf-local-border-color:var(--gf-field-pg-steps-number-border-color-active);--gf-local-color:var(--gf-field-pg-steps-number-color-active)}.gform-theme--framework .gf_step_completed .gf_step_number{--gf-local-bg-color:var(--gf-field-pg-steps-number-bg-color-complete);--gf-local-border-color:var(--gf-field-pg-steps-number-border-color-complete);--gf-local-color:var(--gf-field-pg-steps-number-color-complete)}.gform-theme--framework .gf_step_completed .gf_step_number::after{font-family:var(--gf-icon-font-family)!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:never;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;background-color:var(--gf-field-pg-steps-number-bg-color-complete);block-size:var(--gf-field-pg-steps-number-size);border:var(--gf-field-pg-steps-number-border-width) var(--gf-field-pg-steps-number-border-style) var(--gf-field-pg-steps-number-border-color-complete);border-radius:var(--gf-field-pg-steps-number-radius);color:var(--gf-field-pg-steps-number-color-complete);content:var(--gf-icon-ctrl-pg-numbers-complete);display:inline-grid;font-size:var(--gf-field-pg-steps-icon-font-size);inline-size:var(--gf-field-pg-steps-number-size);inset-block-start:0;inset-inline-start:0;align-content:center;justify-content:center;place-content:center;position:absolute}.gform-theme--framework .gfield--type-password input[type=password]{--gf-local-padding-x:var(--gf-ctrl-padding-x) var(--gf-field-pwd-ctrl-padding-x-end)}.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button{--gf-local-color:var(--gf-ctrl-icon-color)}.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button .dashicons,.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button .dashicons::before,.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button::after,.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button::before{--gf-local-color:var(--gf-ctrl-icon-color)}.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:hover{--gf-local-color:var(--gf-ctrl-icon-color-hover)}.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:hover .dashicons,.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:hover .dashicons::before,.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:hover::after,.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:hover::before{--gf-local-color:var(--gf-ctrl-icon-color-hover)}.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:focus{--gf-local-color:var(--gf-ctrl-icon-color-focus)}.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:focus .dashicons,.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:focus .dashicons::before,.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:focus::after,.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:focus::before{--gf-local-color:var(--gf-ctrl-icon-color-focus)}.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:disabled{--gf-local-color:var(--gf-ctrl-icon-color-disabled)}.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:disabled .dashicons,.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:disabled .dashicons::before,.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:disabled::after,.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button:disabled::before{--gf-local-color:var(--gf-ctrl-icon-color-disabled)}.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button .dashicons.dashicons-visibility::before{--gf-local-content:var(--gf-icon-ctrl-pwd-hidden)}.gform-theme--framework.gform-theme.gform_wrapper .gfield--type-password button.gform_show_password.gform-theme-button .dashicons.dashicons-hidden::before{--gf-local-content:var(--gf-icon-ctrl-pwd-visible)}.gform-theme--framework .gfield_password_strength{--gf-local-bg-color:var(--gf-field-pwd-str-bg-color);--gf-local-border-color:var(--gf-field-pwd-str-border-color);--gf-local-border-style:var(--gf-field-pwd-str-border-style);--gf-local-border-width:var(--gf-field-pwd-str-border-width);--gf-local-border:var(--gf-local-border-width) var(--gf-local-border-style) var(--gf-local-border-color);--gf-local-color:var(--gf-field-pwd-str-color);background-color:var(--gf-local-bg-color);border:var(--gf-local-border);border-radius:var(--gf-field-pwd-str-radius);color:var(--gf-local-color);font-family:var(--gf-field-pwd-str-font-family);font-size:var(--gf-field-pwd-str-font-size);font-style:var(--gf-field-pwd-str-font-style);font-weight:var(--gf-field-pwd-str-font-weight);letter-spacing:var(--gf-field-pwd-str-letter-spacing);line-height:var(--gf-field-pwd-str-line-height);margin-block-start:var(--gf-field-pwd-str-margin-y-start);padding-block:var(--gf-field-pwd-str-padding-y);padding-inline:var(--gf-field-pwd-str-padding-x);position:relative;text-align:var(--gf-field-pwd-str-text-align);transition:var(--gf-field-pwd-str-transition)}.gform-theme--framework .gfield_password_strength::after,.gform-theme--framework .gfield_password_strength::before{--gf-local-bg-color:var(--gf-field-pwd-str-ind-bg-color);--gf-local-height:var(--gf-field-pwd-str-ind-height);--gf-local-radius:var(--gf-field-pwd-str-ind-radius);--gf-local-content:var(--gf-field-pwd-str-ind-content);--gf-local-inset-y-start:var(--gf-field-pwd-str-ind-inset-y-start);--gf-local-inset-x-start:var(--gf-field-pwd-str-ind-inset-x-start);--gf-local-position:var(--gf-field-pwd-str-ind-position);--gf-local-transform:var(--gf-field-pwd-str-ind-transform);--gf-local-transition:none;--gf-local-width:var(--gf-field-pwd-str-ind-width);background-color:var(--gf-local-bg-color);block-size:var(--gf-local-height);border-radius:var(--gf-local-radius);content:var(--gf-local-content);display:var(--gf-field-pwd-str-ind-display);inline-size:var(--gf-local-width);inset-block-start:var(--gf-local-inset-y-start);inset-inline-start:var(--gf-local-inset-x-start);position:var(--gf-local-position);transform:var(--gf-local-transform);transition:var(--gf-local-transition)}.gform-theme--framework .gfield_password_strength::after{--gf-local-transition:var(--gf-field-pwd-str-ind-transition);--gf-local-width:var(--gf-field-pwd-str-ind-width-blank)}.gform-theme--framework .gfield_password_strength.mismatch{--gf-local-bg-color:var(--gf-field-pwd-str-bg-color-mismatch);--gf-local-border-color:var(--gf-field-pwd-str-border-color-mismatch);--gf-local-color:var(--gf-field-pwd-str-color-mismatch)}.gform-theme--framework .gfield_password_strength.mismatch::after{--gf-local-bg-color:var(--gf-field-pwd-str-ind-bg-color-mismatch);--gf-local-width:var(--gf-field-pwd-str-ind-width-mismatch)}.gform-theme--framework .gfield_password_strength.short{--gf-local-bg-color:var(--gf-field-pwd-str-bg-color-short);--gf-local-border-color:var(--gf-field-pwd-str-border-color-short);--gf-local-color:var(--gf-field-pwd-str-color-short)}.gform-theme--framework .gfield_password_strength.short::after{--gf-local-bg-color:var(--gf-field-pwd-str-ind-bg-color-short);--gf-local-width:var(--gf-field-pwd-str-ind-width-short)}.gform-theme--framework .gfield_password_strength.bad{--gf-local-bg-color:var(--gf-field-pwd-str-bg-color-bad);--gf-local-border-color:var(--gf-field-pwd-str-border-color-bad);--gf-local-color:var(--gf-field-pwd-str-color-bad)}.gform-theme--framework .gfield_password_strength.bad::after{--gf-local-bg-color:var(--gf-field-pwd-str-ind-bg-color-bad);--gf-local-width:var(--gf-field-pwd-str-ind-width-bad)}.gform-theme--framework .gfield_password_strength.good{--gf-local-bg-color:var(--gf-field-pwd-str-bg-color-good);--gf-local-border-color:var(--gf-field-pwd-str-border-color-good);--gf-local-color:var(--gf-field-pwd-str-color-good)}.gform-theme--framework .gfield_password_strength.good::after{--gf-local-bg-color:var(--gf-field-pwd-str-ind-bg-color-good);--gf-local-width:var(--gf-field-pwd-str-ind-width-good)}.gform-theme--framework .gfield_password_strength.strong{--gf-local-bg-color:var(--gf-field-pwd-str-bg-color-strong);--gf-local-border-color:var(--gf-field-pwd-str-border-color-strong);--gf-local-color:var(--gf-field-pwd-str-color-strong)}.gform-theme--framework .gfield_password_strength.strong::after{--gf-local-bg-color:var(--gf-field-pwd-str-ind-bg-color-strong);--gf-local-width:var(--gf-field-pwd-str-ind-width-strong)}.gform-theme--framework .gfield--type-product .ginput_product_price{--gf-local-color:var(--gf-field-prod-price-color)}.gform-theme--framework .gfield--type-product input[type].ginput_product_price{--gf-local-display:inline-block}.gform-theme--framework .gfield--type-product .ginput_quantity{--gf-local-width:var(--gf-field-prod-quant-width);margin-block-end:var(--gf-field-prod-quant-margin-y-end)}.gform-theme--framework .gfield--type-product .ginput_container_product_calculation,.gform-theme--framework .gfield--type-product .ginput_container_singleproduct{display:flex;flex-direction:column}.gform-theme--framework .gfield--type-product .ginput_container_product_calculation .ginput_product_price_wrapper,.gform-theme--framework .gfield--type-product .ginput_container_singleproduct .ginput_product_price_wrapper{order:2}.gform-theme--framework .gfield--type-repeater .gfield_repeater_cell:not(:first-child){margin-block-start:var(--gf-field-repeater-gap-y)}.gform-theme--framework .gfield--type-repeater .gfield_repeater_item+.gfield_repeater_item{margin-block-start:var(--gf-field-repeater-gap-y);position:relative}.gform-theme--framework .gfield--type-repeater .gfield_repeater_item+.gfield_repeater_item::before{background-color:var(--gf-field-repeater-separator-color);block-size:1px;content:"";display:block;inline-size:100%;inset-block-start:calc(0px - (var(--gf-field-repeater-gap-y)/ 2));position:absolute}.gform-theme--framework .gfield--type-repeater .gfield_repeater_buttons{margin-block-start:var(--gf-form-footer-margin-y-start)}.gform-theme--framework .gfield--type-repeater .gfield_repeater_buttons .gform-theme-button.gform-theme-button--secondary.add_repeater_item{margin-inline-end:var(--gf-field-repeater-btn-inline-gap)}.gform-theme--framework .gfield--type-repeater .gfield_repeater_wrapper{border-inline-start:var(--gf-field-repeater-nested-border-size) var(--gf-field-repeater-nested-border-style) var(--gf-field-repeater-nested-border-color);border-inline-start-style:var(--gf-field-repeater-nested-border-style);padding-inline-start:var(--gf-field-repeater-nested-padding-x-start)}.gform-theme--framework .gfield--type-repeater .gfield_valid .gform-theme-field-control:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework .gfield--type-repeater .gfield_valid input[type]:where(:not(.gform-text-input-reset):not([type=hidden])):where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework .gfield--type-repeater .gfield_valid select:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework .gfield--type-repeater .gfield_valid select[multiple]:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),.gform-theme--framework .gfield--type-repeater .gfield_valid textarea:where(:not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)){--gf-local-bg-color:var(--gf-ctrl-bg-color);--gf-local-border-color:var(--gf-ctrl-border-color);--gf-local-color:var(--gf-ctrl-color)}.gform-theme--framework:where(:not(.gform_editor)) .gfield--type-section{border-block-end:var(--gf-field-section-border-width) var(--gf-field-section-border-style) var(--gf-field-section-border-color);padding-block-end:var(--gf-field-section-padding-y-end)}.gform-theme--framework .gform_validation_errors{background-color:var(--gf-form-validation-bg-color);border:var(--gf-form-validation-border-width) var(--gf-form-validation-border-style) var(--gf-form-validation-border-color);border-radius:var(--gf-form-validation-radius);box-shadow:var(--gf-form-validation-shadow);display:flex;flex-direction:column;gap:var(--gf-form-validation-gap);margin-block:var(--gf-form-validation-margin-y);padding-block:var(--gf-form-validation-padding-y);padding-inline:var(--gf-form-validation-padding-x)}.gform-theme--framework .gform_validation_errors:focus{border-color:var(--gf-form-validation-border-color-focus);outline:var(--gf-form-validation-outline-focus);outline-offset:var(--gf-ctrl-outline-offset)}.gform-theme--framework .gform_validation_errors .gform_submission_error{color:var(--gf-form-validation-heading-color);display:flex;flex-direction:column;font-family:var(--gf-form-validation-heading-font-family);font-size:var(--gf-form-validation-heading-font-size);font-weight:var(--gf-form-validation-heading-font-weight);gap:var(--gf-form-validation-heading-gap);line-height:var(--gf-form-validation-heading-line-height)}.gform-theme--framework .gform_validation_errors .gform-icon{font-family:var(--gf-icon-font-family)!important;font-style:normal;font-variant:normal;font-weight:400;line-height:1;speak:never;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;background-color:var(--gf-form-validation-heading-icon-bg-color);block-size:var(--gf-form-validation-heading-icon-size);border-color:var(--gf-form-validation-heading-icon-border-color);border-radius:var(--gf-form-validation-heading-icon-radius);border-style:var(--gf-form-validation-heading-icon-border-style);border-width:var(--gf-form-validation-heading-icon-border-width);color:var(--gf-form-validation-heading-icon-color);display:inline-grid;font-size:var(--gf-form-validation-heading-icon-font-size);inline-size:var(--gf-form-validation-heading-icon-size);align-content:center;justify-content:center;place-content:center}.gform-theme--framework .gform_validation_errors .gform-icon::before{content:var(--gf-icon-tooltip-error)}.gform-theme--framework .gform_validation_errors ol{color:var(--gf-form-validation-summary-color);font-family:var(--gf-form-validation-summary-font-family);font-size:var(--gf-form-validation-summary-font-size);font-weight:var(--gf-form-validation-summary-font-weight);line-height:var(--gf-form-validation-summary-line-height);list-style-type:disc;margin-block:0;margin-inline:0;padding-inline:var(--gf-form-validation-padding-x)}.gform-theme--framework .gform_validation_errors ol li+li{margin-block-start:var(--gf-form-validation-summary-margin-y-start)}.gform-theme--framework .gform_validation_errors ol a{border-color:transparent;border-radius:var(--gf-form-validation-radius);border-style:var(--gf-ctrl-border-style);border-width:var(--gf-ctrl-border-width);color:var(--gf-form-validation-summary-color);outline-color:var(--gf-ctrl-outline-color);outline-offset:var(--gf-ctrl-outline-offset);outline-style:var(--gf-ctrl-outline-style);outline-width:var(--gf-ctrl-outline-width);-webkit-text-decoration:var(--gf-form-validation-summary-item-link-text-decoration);text-decoration:var(--gf-form-validation-summary-item-link-text-decoration);transition:var(--gf-ctrl-transition)}.gform-theme--framework .gform_validation_errors ol a:focus{border-color:var(--gf-form-validation-border-color-focus);outline-color:var(--gf-form-validation-outline-color-focus);outline-width:var(--gf-ctrl-outline-width-focus)}@media (min-width:640px){.gform-theme--framework .gform_validation_errors .gform_submission_error{align-items:center;flex-direction:row}.gform-theme--framework .gform_validation_errors ol{padding-inline:var(--gf-form-validation-summary-padding-x)}}.gform-theme--framework .gform_required_legend .gfield_required{color:var(--gf-color-danger)}.gform-theme--framework .gform-loader{border-block-end-color:var(--gf-form-spinner-fg-color);border-block-start-color:var(--gf-form-spinner-bg-color);border-inline-end-color:var(--gf-form-spinner-bg-color);border-inline-start-color:var(--gf-form-spinner-fg-color)}.gform-theme--framework .gform_save_link svg{display:none}.gform-theme--framework .gform_save_link::before{--gf-ctrl-btn-icon:var(--gf-icon-ctrl-save-continue)}.gform-theme--framework .field_description_below .gfield_description:where(:not(.gfield_creditcard_warning_message):not(.field_validation_above .gfield_validation_message):not(.ginput_counter_tinymce):not(.gfield_choice_limit_message)){margin-block-start:var(--gf-desc-space)}.gform-theme--framework .field_description_below .gfield_description:where(.gfield_creditcard_warning_message,.field_validation_above .gfield_validation_message){margin-block-end:var(--gf-desc-space)}.gform-theme--framework .field_description_below.gfield--type-choice .gfield_description:where(:not(.gfield--has-description.field_validation_below .gfield_validation_message):not(.field_validation_above .gfield_validation_message):not(.gfield_choice_limit_message)){margin-block-start:var(--gf-desc-choice-field-space)}.gform-theme--framework .field_description_below.gfield--type-choice .gfield_description:where(.gfield_choice_limit_message){margin-block-end:var(--gf-desc-choice-field-space)}.gform-theme--framework .field_description_above .gfield_description:where([class=gfield_description],.gfield_creditcard_warning_message,.field_validation_above .gfield_validation_message){margin-block-end:var(--gf-desc-space)}.gform-theme--framework .field_description_above .gfield_description:where(:not([class=gfield_description]):not(.gfield_creditcard_warning_message):not(.field_validation_above .gfield_validation_message):not(.ginput_counter_tinymce):not(.gfield_consent_description):not(.gfield_choice_limit_message)){margin-block-start:var(--gf-desc-space)}.gform-theme--framework .field_description_above.gfield--type-choice:where([\:not-has\(.gfield_choice_limit_message\)]) .gfield_description:where(:not(.field_validation_above.gfield_error [class=gfield_description]):not(.gfield_validation_message)){margin-block-end:var(--gf-desc-choice-field-space)}.gform-theme--framework .field_description_above.gfield--type-choice:where(:not(:has(.gfield_choice_limit_message))) .gfield_description:where(:not(.field_validation_above.gfield_error [class=gfield_description]):not(.gfield_validation_message)){margin-block-end:var(--gf-desc-choice-field-space)}.gform-theme--framework .field_description_above.gfield--type-choice .gfield_description:where(.gfield_choice_limit_message){margin-block-end:var(--gf-desc-choice-field-space)}.gform-theme--framework .field_validation_above.gfield--type-choice:where([\:not-has\(.gfield_choice_limit_message\)]) .gfield_description:where(.gfield_validation_message){margin-block-end:var(--gf-desc-choice-field-space)}.gform-theme--framework .field_validation_above.gfield--type-choice:where(:not(:has(.gfield_choice_limit_message))) .gfield_description:where(.gfield_validation_message){margin-block-end:var(--gf-desc-choice-field-space)}.gform-theme--framework .field_validation_below.gfield--type-choice .gfield_description:where(.field_description_above .gfield_validation_message){margin-block-start:var(--gf-desc-choice-field-space)}.gform-theme--framework .left_label .gfield_description:where(:not(.ginput_counter_tinymce):not(.gfield_creditcard_warning_message):not(.field_validation_above .gfield_validation_message):not(.gfield_choice_limit_message)),.gform-theme--framework .right_label .gfield_description:where(:not(.ginput_counter_tinymce):not(.gfield_creditcard_warning_message):not(.field_validation_above .gfield_validation_message):not(.gfield_choice_limit_message)){margin-block:var(--gf-desc-space) 0}@media (min-width:640px){.gform-theme--framework .left_label .gfield_description:where(:not(.ginput_counter_tinymce):not(.gfield_creditcard_warning_message):not(.field_validation_above .gfield_validation_message):not(.gfield_choice_limit_message)),.gform-theme--framework .right_label .gfield_description:where(:not(.ginput_counter_tinymce):not(.gfield_creditcard_warning_message):not(.field_validation_above .gfield_validation_message):not(.gfield_choice_limit_message)){margin-block:0;margin-inline:auto 0;padding-block-start:var(--gf-desc-space)}}.gform-theme--framework .left_label .gfield--type-choice .gfield_description:where(:not(.field_description_below.gfield--has-description .gfield_validation_message):not(.field_validation_above .gfield_validation_message):not(.gfield_choice_limit_message)),.gform-theme--framework .right_label .gfield--type-choice .gfield_description:where(:not(.field_description_below.gfield--has-description .gfield_validation_message):not(.field_validation_above .gfield_validation_message):not(.gfield_choice_limit_message)){margin-block:var(--gf-desc-choice-field-space) 0}@media (min-width:640px){.gform-theme--framework .left_label .gfield--type-choice .gfield_description:where(:not(.field_description_below.gfield--has-description .gfield_validation_message):not(.field_validation_above .gfield_validation_message):not(.gfield_choice_limit_message)),.gform-theme--framework .right_label .gfield--type-choice .gfield_description:where(:not(.field_description_below.gfield--has-description .gfield_validation_message):not(.field_validation_above .gfield_validation_message):not(.gfield_choice_limit_message)){margin-block:0;padding-block-start:var(--gf-desc-choice-field-space)}}.gform-theme--framework .left_label .gfield--type-choice .gfield_description:where(.gfield_choice_limit_message),.gform-theme--framework .right_label .gfield--type-choice .gfield_description:where(.gfield_choice_limit_message){margin-block:0 var(--gf-desc-choice-field-space)}@media (min-width:640px){.gform-theme--framework .left_label .gfield--type-choice .gfield_description:where(.gfield_choice_limit_message),.gform-theme--framework .right_label .gfield--type-choice .gfield_description:where(.gfield_choice_limit_message){margin-block:0;padding-block:0 var(--gf-desc-choice-field-space)}}.gform-theme--framework .left_label .field_description_above .gfield_description:where([class=gfield_description]),.gform-theme--framework .left_label .field_validation_above .gfield_description:where(.gfield_validation_message),.gform-theme--framework .right_label .field_description_above .gfield_description:where([class=gfield_description]),.gform-theme--framework .right_label .field_validation_above .gfield_description:where(.gfield_validation_message){margin-block:0 var(--gf-desc-space)}@media (min-width:640px){.gform-theme--framework .left_label .field_description_above .gfield_description:where([class=gfield_description]),.gform-theme--framework .left_label .field_validation_above .gfield_description:where(.gfield_validation_message),.gform-theme--framework .right_label .field_description_above .gfield_description:where([class=gfield_description]),.gform-theme--framework .right_label .field_validation_above .gfield_description:where(.gfield_validation_message){margin-block:0;padding-block:0 var(--gf-desc-space)}}.gform-theme--framework .left_label .field_description_above.gfield--type-choice:where(:not(.field_validation_above)[\:not-has\(.gfield_choice_limit_message\)]) .gfield_description:where(:not(.gfield_validation_message)),.gform-theme--framework .left_label .field_validation_above.gfield--type-choice:where([\:not-has\(.gfield_choice_limit_message\)]) .gfield_description:where(.gfield_validation_message),.gform-theme--framework .right_label .field_description_above.gfield--type-choice:where(:not(.field_validation_above)[\:not-has\(.gfield_choice_limit_message\)]) .gfield_description:where(:not(.gfield_validation_message)),.gform-theme--framework .right_label .field_validation_above.gfield--type-choice:where([\:not-has\(.gfield_choice_limit_message\)]) .gfield_description:where(.gfield_validation_message){margin-block:0 var(--gf-desc-choice-field-space)}.gform-theme--framework .left_label .field_description_above.gfield--type-choice:where(:not(.field_validation_above):not(:has(.gfield_choice_limit_message))) .gfield_description:where(:not(.gfield_validation_message)),.gform-theme--framework .left_label .field_validation_above.gfield--type-choice:where(:not(:has(.gfield_choice_limit_message))) .gfield_description:where(.gfield_validation_message),.gform-theme--framework .right_label .field_description_above.gfield--type-choice:where(:not(.field_validation_above):not(:has(.gfield_choice_limit_message))) .gfield_description:where(:not(.gfield_validation_message)),.gform-theme--framework .right_label .field_validation_above.gfield--type-choice:where(:not(:has(.gfield_choice_limit_message))) .gfield_description:where(.gfield_validation_message){margin-block:0 var(--gf-desc-choice-field-space)}@media (min-width:640px){.gform-theme--framework .left_label .field_description_above.gfield--type-choice:where(:not(.field_validation_above)[\:not-has\(.gfield_choice_limit_message\)]) .gfield_description:where(:not(.gfield_validation_message)),.gform-theme--framework .left_label .field_validation_above.gfield--type-choice:where([\:not-has\(.gfield_choice_limit_message\)]) .gfield_description:where(.gfield_validation_message),.gform-theme--framework .right_label .field_description_above.gfield--type-choice:where(:not(.field_validation_above)[\:not-has\(.gfield_choice_limit_message\)]) .gfield_description:where(:not(.gfield_validation_message)),.gform-theme--framework .right_label .field_validation_above.gfield--type-choice:where([\:not-has\(.gfield_choice_limit_message\)]) .gfield_description:where(.gfield_validation_message){margin-block:0;padding-block:0 var(--gf-desc-choice-field-space)}.gform-theme--framework .left_label .field_description_above.gfield--type-choice:where(:not(.field_validation_above):not(:has(.gfield_choice_limit_message))) .gfield_description:where(:not(.gfield_validation_message)),.gform-theme--framework .left_label .field_validation_above.gfield--type-choice:where(:not(:has(.gfield_choice_limit_message))) .gfield_description:where(.gfield_validation_message),.gform-theme--framework .right_label .field_description_above.gfield--type-choice:where(:not(.field_validation_above):not(:has(.gfield_choice_limit_message))) .gfield_description:where(:not(.gfield_validation_message)),.gform-theme--framework .right_label .field_validation_above.gfield--type-choice:where(:not(:has(.gfield_choice_limit_message))) .gfield_description:where(.gfield_validation_message){margin-block:0;padding-block:0 var(--gf-desc-choice-field-space)}}.gform-theme--framework .validation_above .gform-conversational__field-footer+.gfield_validation_message.gfield_description{margin-block-end:0;margin-block-start:var(--gf-desc-space)}.gform-theme--framework .gfield_list_group_item::before,.gform-theme--framework .gform-field-label:where(:not([class*=gform-field-label--type-])){--gf-local-margin-y:0 var(--gf-label-space-primary)}.gform-theme--framework .gfield--type-choice.field_description_above.gfield--no-description:where(:not(.field_validation_above.gfield_error)[\:not-has\(.gfield_choice_limit_message\)]) .gform-field-label:where(:not([class*=gform-field-label--type-])),.gform-theme--framework .gfield--type-choice.field_description_below:where(:not(.field_validation_above.gfield_error)[\:not-has\(.gfield_choice_limit_message\)]) .gform-field-label:where(:not([class*=gform-field-label--type-])){--gf-local-margin-y:0 var(--gf-label-choice-field-space-primary)}.gform-theme--framework .gfield--type-choice.field_description_above.gfield--no-description:where(:not(.field_validation_above.gfield_error):not(:has(.gfield_choice_limit_message))) .gform-field-label:where(:not([class*=gform-field-label--type-])),.gform-theme--framework .gfield--type-choice.field_description_below:where(:not(.field_validation_above.gfield_error):not(:has(.gfield_choice_limit_message))) .gform-field-label:where(:not([class*=gform-field-label--type-])){--gf-local-margin-y:0 var(--gf-label-choice-field-space-primary)}@media (min-width:640px){.gform-theme--framework .left_label .gform-field-label:where(:not([class*=gform-field-label--type-]):not(.gfield_header_item):not(.ginput_quantity_label)),.gform-theme--framework .right_label .gform-field-label:where(:not([class*=gform-field-label--type-]):not(.gfield_header_item):not(.ginput_quantity_label)){--gf-local-margin-y:0;--gf-local-margin-x:0;padding-inline-end:var(--gf-label-space-primary)}}.gform-theme--framework .gform-field-label--type-inline{--gf-local-margin-x:var(--gf-label-space-x-secondary) 0;--gf-local-margin-y:var(--gf-label-space-y-secondary) 0}.gform-theme--framework .field_sublabel_below .gform-field-label--type-sub{--gf-local-margin-y:var(--gf-label-space-tertiary) 0}.gform-theme--framework .field_sublabel_above .gform-field-label--type-sub{--gf-local-margin-y:0 var(--gf-label-space-tertiary)}