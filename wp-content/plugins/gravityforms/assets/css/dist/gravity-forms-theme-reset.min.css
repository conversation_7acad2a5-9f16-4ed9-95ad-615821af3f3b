.gform-theme--framework :where(
:not(html):not(iframe):not(canvas):not(img):not(svg):not(video)
:not(svg *):not(symbol *)
:not(.gform-theme__no-reset--el):not(.gform-theme__no-reset--children *):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-reset):not(.gform-theme__disable-reset *):not(.gform_heading *):not(.gfield--type-html *):not(.gfield--type-section *):not(.form_saved_message > *):not(.form_saved_message_sent > *):not(.gform_confirmation_message *):not(.wp-editor-container):not(.mce-tinymce):not(.mce-tinymce *):not(.wp-editor-area):not(.gfield_description > *):not(.gform-field-label--type-inline > :not(span)):not(.ui-resizable-handle):not(.hidden)
){all:unset;display:revert}.gform-theme--framework *,.gform-theme--framework ::after,.gform-theme--framework ::before{box-sizing:border-box}.gform-theme--framework a,.gform-theme--framework button{cursor:revert}.gform-theme--framework menu,.gform-theme--framework ol:where(:not(.gform_heading *):not(.gfield--type-html *):not(.gfield--type-section *):not(.form_saved_message > *):not(.form_saved_message_sent > *):not(.gform_confirmation_message *)),.gform-theme--framework ul:where(:not(.gform_heading *):not(.gfield--type-html *):not(.gfield--type-section *):not(.form_saved_message > *):not(.form_saved_message_sent > *):not(.gform_confirmation_message *)){list-style:none}.gform-theme--framework img{max-inline-size:100%;max-block-size:100%}.gform-theme--framework table{border-collapse:collapse}.gform-theme--framework input,.gform-theme--framework textarea{-webkit-user-select:auto}.gform-theme--framework textarea{white-space:revert}.gform-theme--framework meter{-webkit-appearance:revert;-moz-appearance:revert;appearance:revert}.gform-theme--framework :where(pre){all:revert}.gform-theme--framework ::-moz-placeholder{color:unset}.gform-theme--framework ::placeholder{color:unset}.gform-theme--framework :where([hidden]){display:none}.gform-theme--framework :where([contenteditable]:not([contenteditable=false])){-moz-user-modify:read-write;-webkit-user-modify:read-write;word-wrap:break-word;-webkit-line-break:after-white-space;-webkit-user-select:auto}.gform-theme--framework :where([draggable=true]){-webkit-user-drag:element}.gform-theme--framework :where(dialog:modal){all:revert}.gform-theme--framework input[type=checkbox]::before,.gform-theme--framework input[type=radio]::before{height:auto;margin-block:0;margin-inline:0;position:static;width:auto}.gform-theme--framework input[type=checkbox]::after,.gform-theme--framework input[type=radio]::after{content:none}