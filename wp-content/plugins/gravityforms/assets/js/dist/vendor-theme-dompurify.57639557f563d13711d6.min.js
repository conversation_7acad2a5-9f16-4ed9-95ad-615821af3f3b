(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[567],{6308:function(e){e.exports=function(){"use strict";const{entries:e,setPrototypeOf:t,isFrozen:n,getPrototypeOf:o,getOwnPropertyDescriptor:r}=Object;let{freeze:i,seal:a,create:l}=Object,{apply:c,construct:s}="undefined"!=typeof Reflect&&Reflect;i||(i=function(e){return e}),a||(a=function(e){return e}),c||(c=function(e,t,n){return e.apply(t,n)}),s||(s=function(e,t){return new e(...t)});const u=N(Array.prototype.forEach),m=N(Array.prototype.pop),p=N(Array.prototype.push),f=N(String.prototype.toLowerCase),d=N(String.prototype.toString),h=N(String.prototype.match),g=N(String.prototype.replace),T=N(String.prototype.indexOf),y=N(String.prototype.trim),E=N(Object.prototype.hasOwnProperty),A=N(RegExp.prototype.test),_=b(TypeError);function N(e){return function(t){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return c(e,t,o)}}function b(e){return function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];return s(e,n)}}function S(e,o){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:f;t&&t(e,null);let i=o.length;for(;i--;){let t=o[i];if("string"==typeof t){const e=r(t);e!==t&&(n(o)||(o[i]=e),t=e)}e[t]=!0}return e}function R(e){for(let t=0;t<e.length;t++)E(e,t)||(e[t]=null);return e}function w(t){const n=l(null);for(const[o,r]of e(t))E(t,o)&&(Array.isArray(r)?n[o]=R(r):r&&"object"==typeof r&&r.constructor===Object?n[o]=w(r):n[o]=r);return n}function C(e,t){for(;null!==e;){const n=r(e,t);if(n){if(n.get)return N(n.get);if("function"==typeof n.value)return N(n.value)}e=o(e)}function n(){return null}return n}const L=i(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),D=i(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),k=i(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),O=i(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),v=i(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),x=i(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),I=i(["#text"]),M=i(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),U=i(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),P=i(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),H=i(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),F=a(/\{\{[\w\W]*|[\w\W]*\}\}/gm),z=a(/<%[\w\W]*|[\w\W]*%>/gm),B=a(/\${[\w\W]*}/gm),W=a(/^data-[\-\w.\u00B7-\uFFFF]/),G=a(/^aria-[\-\w]+$/),Y=a(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),j=a(/^(?:\w+script|data):/i),q=a(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),X=a(/^html$/i),$=a(/^[a-z][.\w]*(-[.\w]+)+$/i);var K=Object.freeze({__proto__:null,MUSTACHE_EXPR:F,ERB_EXPR:z,TMPLIT_EXPR:B,DATA_ATTR:W,ARIA_ATTR:G,IS_ALLOWED_URI:Y,IS_SCRIPT_OR_DATA:j,ATTR_WHITESPACE:q,DOCTYPE_NAME:X,CUSTOM_ELEMENT:$});const V=function(){return"undefined"==typeof window?null:window},Z=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML(e){return e},createScriptURL(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+r+" could not be created."),null}};function J(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:V();const n=e=>J(e);if(n.version="3.0.11",n.removed=[],!t||!t.document||9!==t.document.nodeType)return n.isSupported=!1,n;let{document:o}=t;const r=o,a=r.currentScript,{DocumentFragment:c,HTMLTemplateElement:s,Node:N,Element:b,NodeFilter:R,NamedNodeMap:F=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:z,DOMParser:B,trustedTypes:W}=t,G=b.prototype,j=C(G,"cloneNode"),q=C(G,"nextSibling"),$=C(G,"childNodes"),Q=C(G,"parentNode");if("function"==typeof s){const e=o.createElement("template");e.content&&e.content.ownerDocument&&(o=e.content.ownerDocument)}let ee,te="";const{implementation:ne,createNodeIterator:oe,createDocumentFragment:re,getElementsByTagName:ie}=o,{importNode:ae}=r;let le={};n.isSupported="function"==typeof e&&"function"==typeof Q&&ne&&void 0!==ne.createHTMLDocument;const{MUSTACHE_EXPR:ce,ERB_EXPR:se,TMPLIT_EXPR:ue,DATA_ATTR:me,ARIA_ATTR:pe,IS_SCRIPT_OR_DATA:fe,ATTR_WHITESPACE:de,CUSTOM_ELEMENT:he}=K;let{IS_ALLOWED_URI:ge}=K,Te=null;const ye=S({},[...L,...D,...k,...v,...I]);let Ee=null;const Ae=S({},[...M,...U,...P,...H]);let _e=Object.seal(l(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Ne=null,be=null,Se=!0,Re=!0,we=!1,Ce=!0,Le=!1,De=!1,ke=!1,Oe=!1,ve=!1,xe=!1,Ie=!1,Me=!0,Ue=!1;const Pe="user-content-";let He=!0,Fe=!1,ze={},Be=null;const We=S({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ge=null;const Ye=S({},["audio","video","img","source","image","track"]);let je=null;const qe=S({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Xe="http://www.w3.org/1998/Math/MathML",$e="http://www.w3.org/2000/svg",Ke="http://www.w3.org/1999/xhtml";let Ve=Ke,Ze=!1,Je=null;const Qe=S({},[Xe,$e,Ke],d);let et=null;const tt=["application/xhtml+xml","text/html"],nt="text/html";let ot=null,rt=null;const it=o.createElement("form"),at=function(e){return e instanceof RegExp||e instanceof Function},lt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!rt||rt!==e){if(e&&"object"==typeof e||(e={}),e=w(e),et=-1===tt.indexOf(e.PARSER_MEDIA_TYPE)?nt:e.PARSER_MEDIA_TYPE,ot="application/xhtml+xml"===et?d:f,Te=E(e,"ALLOWED_TAGS")?S({},e.ALLOWED_TAGS,ot):ye,Ee=E(e,"ALLOWED_ATTR")?S({},e.ALLOWED_ATTR,ot):Ae,Je=E(e,"ALLOWED_NAMESPACES")?S({},e.ALLOWED_NAMESPACES,d):Qe,je=E(e,"ADD_URI_SAFE_ATTR")?S(w(qe),e.ADD_URI_SAFE_ATTR,ot):qe,Ge=E(e,"ADD_DATA_URI_TAGS")?S(w(Ye),e.ADD_DATA_URI_TAGS,ot):Ye,Be=E(e,"FORBID_CONTENTS")?S({},e.FORBID_CONTENTS,ot):We,Ne=E(e,"FORBID_TAGS")?S({},e.FORBID_TAGS,ot):{},be=E(e,"FORBID_ATTR")?S({},e.FORBID_ATTR,ot):{},ze=!!E(e,"USE_PROFILES")&&e.USE_PROFILES,Se=!1!==e.ALLOW_ARIA_ATTR,Re=!1!==e.ALLOW_DATA_ATTR,we=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Ce=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Le=e.SAFE_FOR_TEMPLATES||!1,De=e.WHOLE_DOCUMENT||!1,ve=e.RETURN_DOM||!1,xe=e.RETURN_DOM_FRAGMENT||!1,Ie=e.RETURN_TRUSTED_TYPE||!1,Oe=e.FORCE_BODY||!1,Me=!1!==e.SANITIZE_DOM,Ue=e.SANITIZE_NAMED_PROPS||!1,He=!1!==e.KEEP_CONTENT,Fe=e.IN_PLACE||!1,ge=e.ALLOWED_URI_REGEXP||Y,Ve=e.NAMESPACE||Ke,_e=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&at(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(_e.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&at(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(_e.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(_e.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Le&&(Re=!1),xe&&(ve=!0),ze&&(Te=S({},I),Ee=[],!0===ze.html&&(S(Te,L),S(Ee,M)),!0===ze.svg&&(S(Te,D),S(Ee,U),S(Ee,H)),!0===ze.svgFilters&&(S(Te,k),S(Ee,U),S(Ee,H)),!0===ze.mathMl&&(S(Te,v),S(Ee,P),S(Ee,H))),e.ADD_TAGS&&(Te===ye&&(Te=w(Te)),S(Te,e.ADD_TAGS,ot)),e.ADD_ATTR&&(Ee===Ae&&(Ee=w(Ee)),S(Ee,e.ADD_ATTR,ot)),e.ADD_URI_SAFE_ATTR&&S(je,e.ADD_URI_SAFE_ATTR,ot),e.FORBID_CONTENTS&&(Be===We&&(Be=w(Be)),S(Be,e.FORBID_CONTENTS,ot)),He&&(Te["#text"]=!0),De&&S(Te,["html","head","body"]),Te.table&&(S(Te,["tbody"]),delete Ne.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw _('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw _('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ee=e.TRUSTED_TYPES_POLICY,te=ee.createHTML("")}else void 0===ee&&(ee=Z(W,a)),null!==ee&&"string"==typeof te&&(te=ee.createHTML(""));i&&i(e),rt=e}},ct=S({},["mi","mo","mn","ms","mtext"]),st=S({},["foreignobject","desc","title","annotation-xml"]),ut=S({},["title","style","font","a","script"]),mt=S({},[...D,...k,...O]),pt=S({},[...v,...x]),ft=function(e){let t=Q(e);t&&t.tagName||(t={namespaceURI:Ve,tagName:"template"});const n=f(e.tagName),o=f(t.tagName);return!!Je[e.namespaceURI]&&(e.namespaceURI===$e?t.namespaceURI===Ke?"svg"===n:t.namespaceURI===Xe?"svg"===n&&("annotation-xml"===o||ct[o]):Boolean(mt[n]):e.namespaceURI===Xe?t.namespaceURI===Ke?"math"===n:t.namespaceURI===$e?"math"===n&&st[o]:Boolean(pt[n]):e.namespaceURI===Ke?!(t.namespaceURI===$e&&!st[o])&&!(t.namespaceURI===Xe&&!ct[o])&&!pt[n]&&(ut[n]||!mt[n]):!("application/xhtml+xml"!==et||!Je[e.namespaceURI]))},dt=function(e){p(n.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){e.remove()}},ht=function(e,t){try{p(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){p(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!Ee[e])if(ve||xe)try{dt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},gt=function(e){let t=null,n=null;if(Oe)e="<remove></remove>"+e;else{const t=h(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===et&&Ve===Ke&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const r=ee?ee.createHTML(e):e;if(Ve===Ke)try{t=(new B).parseFromString(r,et)}catch(e){}if(!t||!t.documentElement){t=ne.createDocument(Ve,"template",null);try{t.documentElement.innerHTML=Ze?te:r}catch(e){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(o.createTextNode(n),i.childNodes[0]||null),Ve===Ke?ie.call(t,De?"html":"body")[0]:De?t.documentElement:i},Tt=function(e){return oe.call(e.ownerDocument||e,e,R.SHOW_ELEMENT|R.SHOW_COMMENT|R.SHOW_TEXT|R.SHOW_PROCESSING_INSTRUCTION|R.SHOW_CDATA_SECTION,null)},yt=function(e){return e instanceof z&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof F)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Et=function(e){return"function"==typeof N&&e instanceof N},At=function(e,t,o){le[e]&&u(le[e],e=>{e.call(n,t,o,rt)})},_t=function(e){let t=null;if(At("beforeSanitizeElements",e,null),yt(e))return dt(e),!0;const o=ot(e.nodeName);if(At("uponSanitizeElement",e,{tagName:o,allowedTags:Te}),e.hasChildNodes()&&!Et(e.firstElementChild)&&A(/<[/\w]/g,e.innerHTML)&&A(/<[/\w]/g,e.textContent))return dt(e),!0;if(7===e.nodeType)return dt(e),!0;if(!Te[o]||Ne[o]){if(!Ne[o]&&bt(o)){if(_e.tagNameCheck instanceof RegExp&&A(_e.tagNameCheck,o))return!1;if(_e.tagNameCheck instanceof Function&&_e.tagNameCheck(o))return!1}if(He&&!Be[o]){const t=Q(e)||e.parentNode,n=$(e)||e.childNodes;if(n&&t)for(let o=n.length-1;o>=0;--o)t.insertBefore(j(n[o],!0),q(e))}return dt(e),!0}return e instanceof b&&!ft(e)?(dt(e),!0):"noscript"!==o&&"noembed"!==o&&"noframes"!==o||!A(/<\/no(script|embed|frames)/i,e.innerHTML)?(Le&&3===e.nodeType&&(t=e.textContent,u([ce,se,ue],e=>{t=g(t,e," ")}),e.textContent!==t&&(p(n.removed,{element:e.cloneNode()}),e.textContent=t)),At("afterSanitizeElements",e,null),!1):(dt(e),!0)},Nt=function(e,t,n){if(Me&&("id"===t||"name"===t)&&(n in o||n in it))return!1;if(Re&&!be[t]&&A(me,t));else if(Se&&A(pe,t));else if(!Ee[t]||be[t]){if(!(bt(e)&&(_e.tagNameCheck instanceof RegExp&&A(_e.tagNameCheck,e)||_e.tagNameCheck instanceof Function&&_e.tagNameCheck(e))&&(_e.attributeNameCheck instanceof RegExp&&A(_e.attributeNameCheck,t)||_e.attributeNameCheck instanceof Function&&_e.attributeNameCheck(t))||"is"===t&&_e.allowCustomizedBuiltInElements&&(_e.tagNameCheck instanceof RegExp&&A(_e.tagNameCheck,n)||_e.tagNameCheck instanceof Function&&_e.tagNameCheck(n))))return!1}else if(je[t]);else if(A(ge,g(n,de,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==T(n,"data:")||!Ge[e])if(we&&!A(fe,g(n,de,"")));else if(n)return!1;return!0},bt=function(e){return"annotation-xml"!==e&&h(e,he)},St=function(e){At("beforeSanitizeAttributes",e,null);const{attributes:t}=e;if(!t)return;const o={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Ee};let r=t.length;for(;r--;){const i=t[r],{name:a,namespaceURI:l,value:c}=i,s=ot(a);let p="value"===a?c:y(c);if(o.attrName=s,o.attrValue=p,o.keepAttr=!0,o.forceKeepAttr=void 0,At("uponSanitizeAttribute",e,o),p=o.attrValue,o.forceKeepAttr)continue;if(ht(a,e),!o.keepAttr)continue;if(!Ce&&A(/\/>/i,p)){ht(a,e);continue}Le&&u([ce,se,ue],e=>{p=g(p,e," ")});const f=ot(e.nodeName);if(Nt(f,s,p)){if(!Ue||"id"!==s&&"name"!==s||(ht(a,e),p=Pe+p),ee&&"object"==typeof W&&"function"==typeof W.getAttributeType)if(l);else switch(W.getAttributeType(f,s)){case"TrustedHTML":p=ee.createHTML(p);break;case"TrustedScriptURL":p=ee.createScriptURL(p)}try{l?e.setAttributeNS(l,a,p):e.setAttribute(a,p),m(n.removed)}catch(e){}}}At("afterSanitizeAttributes",e,null)},Rt=function e(t){let n=null;const o=Tt(t);for(At("beforeSanitizeShadowDOM",t,null);n=o.nextNode();)At("uponSanitizeShadowNode",n,null),_t(n)||(n.content instanceof c&&e(n.content),St(n));At("afterSanitizeShadowDOM",t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=null,i=null,a=null,l=null;if(Ze=!e,Ze&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Et(e)){if("function"!=typeof e.toString)throw _("toString is not a function");if("string"!=typeof(e=e.toString()))throw _("dirty is not a string, aborting")}if(!n.isSupported)return e;if(ke||lt(t),n.removed=[],"string"==typeof e&&(Fe=!1),Fe){if(e.nodeName){const t=ot(e.nodeName);if(!Te[t]||Ne[t])throw _("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof N)o=gt("\x3c!----\x3e"),i=o.ownerDocument.importNode(e,!0),1===i.nodeType&&"BODY"===i.nodeName||"HTML"===i.nodeName?o=i:o.appendChild(i);else{if(!ve&&!Le&&!De&&-1===e.indexOf("<"))return ee&&Ie?ee.createHTML(e):e;if(o=gt(e),!o)return ve?null:Ie?te:""}o&&Oe&&dt(o.firstChild);const s=Tt(Fe?e:o);for(;a=s.nextNode();)_t(a)||(a.content instanceof c&&Rt(a.content),St(a));if(Fe)return e;if(ve){if(xe)for(l=re.call(o.ownerDocument);o.firstChild;)l.appendChild(o.firstChild);else l=o;return(Ee.shadowroot||Ee.shadowrootmode)&&(l=ae.call(r,l,!0)),l}let m=De?o.outerHTML:o.innerHTML;return De&&Te["!doctype"]&&o.ownerDocument&&o.ownerDocument.doctype&&o.ownerDocument.doctype.name&&A(X,o.ownerDocument.doctype.name)&&(m="<!DOCTYPE "+o.ownerDocument.doctype.name+">\n"+m),Le&&u([ce,se,ue],e=>{m=g(m,e," ")}),ee&&Ie?ee.createHTML(m):m},n.setConfig=function(){lt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),ke=!0},n.clearConfig=function(){rt=null,ke=!1},n.isValidAttribute=function(e,t,n){rt||lt({});const o=ot(e),r=ot(t);return Nt(o,r,n)},n.addHook=function(e,t){"function"==typeof t&&(le[e]=le[e]||[],p(le[e],t))},n.removeHook=function(e){if(le[e])return m(le[e])},n.removeHooks=function(e){le[e]&&(le[e]=[])},n.removeAllHooks=function(){le={}},n}return J()}()}}]);