"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[301],{5248:function(e,t,i){i.r(t),i.d(t,{default:function(){return De}});var n,o=i(5798),a=i(9662),r=i.n(a),s=i(1533),l=i.n(s),c=i(2685),d=i.n(c),u={labels:(0,o.getNodes)("choices-ui-label",!0),mainSettings:(0,o.getNodes)('[data-js="choices-ui-setting"][data-type="main"]',!0,document,!0),sections:(0,o.getNodes)("choices-ui-section",!0),settings:(0,o.getNodes)("choices-ui-setting",!0),flyoutTrigger:(0,o.getNodes)("choices-ui-trigger")[0],flyoutTriggerLabel:(0,o.getNodes)("choices-ui-trigger-label")[0]},f=(null===l()||void 0===l()||null===(n=l().form_editor)||void 0===n?void 0:n.choices_ui)||{},m=function(){var e=(0,o.getNodes)('[data-js="choices-ui-setting"][data-type="main"]',!0,r().instances.choicesUi.flyout.elements.content,!0).filter(function(e){return"none"!==window.getComputedStyle(e).getPropertyValue("display")})[0],t=(0,o.getNodes)('[data-js="choices-ui-section"][data-type="options"]',!1,e,!0)[0],i=(0,o.getNodes)("choices-ui-option-list",!1,t)[0];t.style.display="block",t.style.display=i.clientHeight>25?"block":"none"},v=function(){var e,t,i,n,a,s=u.settings.filter(function(e){return"none"!==window.getComputedStyle(e).getPropertyValue("display")});u.flyoutTrigger.style.display=s.length?"":"none",u.flyoutTriggerLabel.style.display=s.length?"":"none",function(){if(!(u.mainSettings.length<2)){var e=u.mainSettings.filter(function(e){return"none"!==window.getComputedStyle(e).getPropertyValue("display")})[0];if(e){var t=(0,o.getNodes)("choices-ui-option-list",!1,e)[0];t.innerHTML="",u.options.forEach(function(e){t.appendChild(e),"list-item"===window.getComputedStyle(e).getPropertyValue("display")&&(e.style.display="inline-block")})}}}(),i=(null===(e=window)||void 0===e||null===(e=e.field)||void 0===e?void 0:e.type)||"",n=(null===(t=window)||void 0===t||null===(t=t.field)||void 0===t?void 0:t.inputType)||"",a=r().instances.choicesUi.flyout.elements.flyout,(0,o.removeClassThatContains)(a,"gform-flyout--choices-ui--"),i&&a.classList.add("gform-flyout--choices-ui--".concat(i)),n&&a.classList.add("gform-flyout--choices-ui--input-type-".concat(n)),m()},g=function(e){var t=e.title,i=void 0===t?"":t,n=e.content,a=void 0===n?null:n,s=e.position,l=void 0===s?"beforeend":s,c=e.type,d=void 0===c?"":c;if(a){r().instances.choicesUi.flyout.elements.content.insertAdjacentHTML(l,'\n\t\t<div class="choices-ui__section" data-js="choices-ui-section" data-type="'.concat(d,'">\n\t\t\t<h6 class="choices-ui__section-label">').concat((0,o.escapeHtml)(i),"</h6>\n\t\t</div>\n\t"));var f=(0,o.getNodes)("choices-ui-section",!0);f[f.length-1].appendChild(a),u.sections.push(f[f.length-1])}else(0,o.consoleError)("Gravity Forms Admin: You must supply a valid node to appendSectionHtml.")},p=function(e){u.container=e,(0,o.trigger)({event:"gform/choices_ui/pre_init",native:!1,data:{elements:u}}),r().instances=r().instances||{},r().instances.choicesUi={},function(){var e=f.i18n,t=e.title,i=e.expandableTitle,n=e.description;r().instances.choicesUi.flyout=new(d())({closeOnOutsideClickExceptions:[".media-modal",".gform-dialog__mask"],description:(0,o.escapeHtml)(n),expandable:!0,expandableTitle:(0,o.escapeHtml)(i),expandableWidth:100,id:"choices-ui-flyout",maxWidth:540,mobileBreakpoint:1200,onOpen:function(){setTimeout(function(){m()},50),(0,o.trigger)({event:"gforms/choices_ui/opened",native:!1,data:r().instances.choicesUi})},position:"absolute",simplebar:!0,target:'[data-js="form-editor"]',title:(0,o.escapeHtml)(t),triggers:'[data-js="choices-ui-trigger"]',wrapperClasses:"gform-flyout gform-flyout--choices-ui",zIndex:100})}(),function(){r().instances.choicesUi.flyout.elements.content.insertAdjacentHTML("afterbegin",'<ul class="choices-ui__content" data-js="choices-ui-content"></ul>');var e=(0,o.getNodes)("choices-ui-content",!1,r().instances.choicesUi.flyout.elements.content)[0];u.settings.forEach(function(t){return e.appendChild(t)})}(),u.optionsList=(0,o.getNodes)("choices-ui-option-list",!1,r().instances.choicesUi.flyout.elements.content)[0],u.settings.forEach(function(e){"option"===e.dataset.type&&u.optionsList.appendChild(e)}),u.options=(0,o.getNodes)('[data-js="choices-ui-option-list"] > li',!0,r().instances.choicesUi.flyout.elements.content,!0),r().instances.choicesUi.flyout.elements.flyout.addEventListener("click",function(e){e.stopPropagation()}),document.addEventListener("gform/form_editor/setting_selected",v),u.flyoutEl=(0,o.getNodes)("#choices-ui-flyout .gform-flyout__body",!1,document,!0),r().instances.choicesUi.elements=u,r().instances.choicesUi.methods={appendSectionHtml:g},(0,o.trigger)({event:"gform/choices_ui/post_render",native:!1,data:r().instances.choicesUi}),(0,o.consoleInfo)("Gravity Forms Admin: Initialized choices ui flyout.")},h=i(455),y=i(9280),w=i.n(y),_=i(7616),b=i(3801),S=i.n(b),k=_.ReactDOM.createRoot,A=function(e){var t=e.detail.id,i=window.wp.media({library:{type:"image"},multiple:!1});i.on("select",function(){var e=i.state().get("selection").first().toJSON(),n=e.url,o=void 0===n?"":n,a=e.id,r=void 0===a?"":a,s=e.sizes,l=(void 0===s?{}:s).large,c=(void 0===l?{}:l).url;C(t,r,void 0===c?"":c,o)}),i.open()},E=function(){var e=(0,h.A)(w().mark(function e(t){var i,n,o,a,r,s,c,d,u,f,m,v;return w().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,(s=new FormData).append("name","fileUpload"),s.append("action","upload-attachment"),s.append("_wpnonce",(null===(i=_wpPluploadSettings)||void 0===i||null===(i=i.defaults)||void 0===i||null===(i=i.multipart_params)||void 0===i?void 0:i._wpnonce)||""),s.append("async-upload",t.detail.file),e.next=8,fetch((null===l()||void 0===l()||null===(n=l().endpoints)||void 0===n?void 0:n.ajaxurl)||"",{method:"POST",credentials:"same-origin",body:s});case 8:return c=e.sent,e.next=11,c.json();case 11:d=e.sent,u=t.detail.id,f=null==d||null===(o=d.data)||void 0===o?void 0:o.id,m=null==d||null===(a=d.data)||void 0===a?void 0:a.url,v=null==d||null===(r=d.data)||void 0===r||null===(r=r.sizes)||void 0===r||null===(r=r.large)||void 0===r?void 0:r.url,C(u,f,v,m),e.next=22;break;case 19:e.prev=19,e.t0=e.catch(0),console.error(e.t0);case 22:case"end":return e.stop()}},e,null,[[0,19]])}));return function(t){return e.apply(this,arguments)}}(),C=function(e,t,i,n){(0,o.trigger)({event:"gform/file_upload/external_manager/file_selected",native:!1,data:{fileUploadId:e,id:t,largeUrl:i,url:n}})},B=function(e){field=GetSelectedField();var t=e.type.split("/").pop(),i="file_remove"===t?"id":"fileUploadId",n=e.detail[i].split("_").pop(),o="",a="";"file_selected"===t&&(o=e.detail.largeUrl?e.detail.largeUrl:e.detail.url,a=e.detail.id),field.choices[n]&&(field.choices[n].file_url=o,field.choices[n].attachment_id=a),RefreshSelectedFieldPreview()},I=function(){r().addAction("gform_load_field_choices",function(){(0,o.getNodes)(".field-choice-row",!0,document,!0).forEach(function(e){var t;if("checkbox"===e.dataset.input_type||"radio"===e.dataset.input_type){var i=(0,o.getNode)("gform-image-choice-upload",e);if(i&&!e.querySelector(".mounted")){var n=(null==i||null===(t=i.dataset)||void 0===t?void 0:t.jsProps)||"{}",a=JSON.parse(n);k(i).render(_.React.createElement(S(),a)),i.classList.add("mounted")}}})}),document.addEventListener("gform/file_upload/external_manager/open",A),document.addEventListener("gform/file_upload/external_manager/save",E),document.addEventListener("gform/file_upload/external_manager/file_selected",B),document.addEventListener("gform/file_upload/external_manager/file_remove",B)},x=function(){I(),(0,o.consoleInfo)("Gravity Forms Admin: Initialized choices ui for image choices.")},F=function(e){p(e),x(),(0,o.consoleInfo)("Gravity Forms Admin: Initialized all choices ui scripts.")},T=function(e){var t=e.detail.field;document.querySelector(".gform-compact-view")&&(t.classList.add("trigger-reflow"),t.offsetHeight,t.classList.remove("trigger-reflow"))},D=function(){document.addEventListener("gform/layout_editor/field_refresh_preview",T),(0,o.consoleInfo)("Gravity Forms Admin: Initialized form editor field event listeners.")},N=function(){D(),(0,o.consoleInfo)("Gravity Forms Admin: Initialized all form editor field scripts.")},L=i(1860),j=i(2888),O=i(1873),U=i(7113),P=i(0),z=i(1118),H=i(6739),R=i(7821),M=i(428),q=i.n(M);function V(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=function(e,t){if(e){if("string"==typeof e)return G(e,t);var i={}.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?G(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,r=!0,s=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return r=e.done,e},e:function(e){s=!0,a=e},f:function(){try{r||null==i.return||i.return()}finally{if(s)throw a}}}}function G(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=Array(t);i<t;i++)n[i]=e[i];return n}function J(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(J=function(){return!!e})()}var Q=function(e){function t(){return(0,O.A)(this,t),e=this,i=t,n=arguments,i=(0,z.A)(i),(0,P.A)(e,J()?Reflect.construct(i,n||[],(0,z.A)(e).constructor):i.apply(e,n));var e,i,n}return(0,R.A)(t,e),(0,U.A)(t,[{key:"init",value:function(){var e=this;this.eventsManager.addListener("SaveRequested",this.save,this),this.eventsManager.addListener("SaveCompleted",this.resetFormChars,this),document.addEventListener("gform/dialog/confirm",this.maybeSave.bind(this));var t,i=V(this.config.data.domEvents);try{var n,o=function(){var i=t.value;if(i){var n=e.eventsManager.get(i.name),o=document,a={};if("document"!==i.elementSelector&&(o=document.getElementsByClassName(i.elementSelector.replace(".",""))[0]),void 0===o)return{v:void 0};o.addEventListener(i.action,function(t){if(!e.saveInProgress){if("keydown"===i.action&&"keys"in i&&i.keys.length>0){if(-1===i.keys.indexOf(t.keyCode))return!1;if(a[t.keyCode.toString()]=t.keyCode,!i.keys.every(function(e){return e in a!=!1}))return e.pressedKeysTimeOut=setTimeout(function(){a={}},1e3),!1}e.saveInProgress||(r().instances.adminFormSaverUIHandler.activeElement=document.activeElement,document.activeElement.blur(),t.preventDefault(),t.stopImmediatePropagation(),a={},n.fire(),e.saveInProgress=!0)}})}};for(i.s();!(t=i.n()).done;)if(n=o())return n.v}catch(e){i.e(e)}finally{i.f()}}},{key:"maybeSave",value:function(e){if("gform/dialog/confirm"===e.type){var t;if("dialog-embed-form-unsaved-changes"!==(null==e||null===(t=e.detail)||void 0===t||null===(t=t.instance)||void 0===t||null===(t=t.options)||void 0===t?void 0:t.id))return;this.save()}}},{key:"save",value:function(){var e,i,n,a,r,s=(0,o.getNodes)("force-focus")[0];if(s&&s.focus(),this.formJSONString=this.getUpdatedFormJSON(),!window.ValidateForm())return this.eventsManager.trigger("SaveAborted"),!1;(e=t,i="save",n=this,a=3,r=(0,H.A)((0,z.A)(1&a?e.prototype:e),i,n),2&a&&"function"==typeof r?function(e){return r.apply(n,e)}:r)([])}},{key:"deepSanitizeFormValues",value:function(e){if("object"!==(0,j.A)(e))return e;for(var t in e){var i=e[t];"object"!==(0,j.A)(i)||null===i?"string"==typeof i?(e[t]=i.replace(/\\'/g,"'"),e[t]=i.replace(/\\/g,"\\\\")):e[t]=i:e[t]=this.deepSanitizeFormValues(i)}return e}},{key:"resetFormChars",value:function(){window.form=this.reverseFormSanitization(this.form)}},{key:"reverseFormSanitization",value:function(e){if("object"!==(0,j.A)(e))return e;for(var t in e){var i=e[t];"object"!==(0,j.A)(i)||null===i?e[t]="string"==typeof i?i.replace(/\\\\/g,"\\"):i:e[t]=this.reverseFormSanitization(i)}return e}},{key:"getUpdatedFormJSON",value:function(){try{this.form=window.UpdateFormObject(),window.gforms_original_json=q().toJSON(this.form)}catch(e){(0,o.consoleError)(e)}var e=(0,L.A)({},this.form);"feeds"in e&&delete e.feeds;var t=this.deepSanitizeFormValues(e);return q().toJSON(t)}}])}(i(1281).A),W=i(6752),K=i(3244),Y=i.n(K),$=i(6900),X=i.n($),Z=i(3963),ee=i.n(Z),te=function(){return(0,U.A)(function e(t,i){(0,O.A)(this,e),this.events=t,this.config=i,this.timeOuts=[],this.scripts=[],this.styles=[],this.buttons=[],this.maybeHandleNormalPostRequest(),this.setInitialUrlState(),this.initButtons(),this.bindEvents(),this.init()},[{key:"init",value:function(){var e=this;this.timeOuts.forEach(function(e){window.clearTimeout(e)}),document.getElementsByTagName("script").forEach(function(t){var i=t.getAttribute("src");null!==i&&(i=i.substring(0,i.indexOf("?ver")),e.scripts.push(i))}),document.getElementsByTagName("link").forEach(function(t){var i=t.getAttribute("href");null!==i&&(i=i.substring(0,i.indexOf("?ver")),e.styles.push(i))}),this.selectedField={}}},{key:"initButtons",value:function(){var e=this;this.config.data.selectors.saveAnimationButtons.forEach(function(t){(0,o.getNodes)(t,!0,document,!0).forEach(function(t){var i=t.id?t.id:(0,o.uniqueId)("button-");t.id=i;var n=new(X())({activeType:"loader",id:i,interactive:!0,loaderOptions:{background:"transparent",foreground:"#fff",mask:!1,showOnRender:!1,size:1.5},rendered:!0});e.buttons.push(n)})})}},{key:"maybeHandleNormalPostRequest",value:function(){void 0!==window.updateFormResult&&("invalid_json"===window.updateFormResult.status?this.showSaveErrorNotification():this.showSaveSuccessNotification())}},{key:"bindEvents",value:function(){this.events.addListeners([{name:"SaveBegan",handler:[this.init,this.beginButtonAnimation]},{name:"SaveInProgress",handler:this.handleSaveProgress},{name:["SaveFailed","SaveRequestFailed","SaveResponseMalformed"],handler:[this.handleSaveFailure]},{name:"SaveSucceeded",handler:[this.handleSaveSuccess]},{name:"SaveCompleted",handler:this.reset},{name:"SaveBegan",handler:this.beginButtonAnimation},{name:["SaveCompleted","SaveAborted"],handler:[this.endButtonAnimation,this.reset]}],this)}},{key:"beginButtonAnimation",value:function(){this.buttons.filter(function(e){return e.options.interactive}).forEach(function(e){return e.activateButton()})}},{key:"endButtonAnimation",value:function(){this.buttons.filter(function(e){return e.options.interactive}).forEach(function(e){return e.deactivateButton()})}},{key:"reset",value:function(){var e=this;this.registerTimeOut(function(){e.buttons.forEach(function(e){e.deactivateButton()}),"id"in e.selectedField&&q()("#field_"+e.selectedField.id).click(),e.resetSettingsControlsDisabledState(),e.activeElement&&q()(e.activeElement).focus(),r().instances.adminFormSaver.saveInProgress=!1},this.config.data.animationDelay)}},{key:"resetElementClasses",value:function(e,t){var i=t.classList.value;if(void 0!==i&&""!==i){i=i.split(" "),e.removeClass();for(var n=0;n<i.length;n++)""!==i[n]&&void 0!==i[n]&&e.addClass(i[n])}}},{key:"handleSaveProgress",value:function(){var e=this;this.selectedField=window.GetSelectedField(),this.selectedField||(this.selectedField={}),this.registerTimeOut(function(){Y().a11y.speak(e.config.i18n.saveInProgress,"polite"),e.disableSettingsControls()},this.config.data.animationDelay/8)}},{key:"handleSaveFailure",value:function(e){var t=this;this.registerTimeOut(function(){var i,n=null==e||null===(i=e.error)||void 0===i?void 0:i.message;n&&"Failed to fetch"===n?t.showSaveErrorNotification(t.config.i18n.networkError):t.showSaveErrorDialog(),(0,o.trigger)({event:"gform/form_editor_saver/post_save_error",native:!1})},this.config.data.animationDelay)}},{key:"fireManualSave",value:function(){(0,o.getNodes)("#gform_export",!1,document,!0)[0].value=!0,window.SaveForm()}},{key:"showSaveErrorDialog",value:function(){new(ee())({animationDelay:250,closeOnConfirmClick:!1,closeOnMaskClick:!1,confirmButtonIcon:"floppy-disk",onConfirm:this.fireManualSave.bind(this),id:"dialog-ajax-save-error",cancelButtonText:this.config.i18n.ajaxErrorDialogCancelButtonText,closeButtonTitle:this.config.i18n.ajaxErrorDialogCloseButtonTitle,confirmButtonText:this.config.i18n.ajaxErrorDialogConfirmButtonText,content:this.config.i18n.ajaxErrorDialogContent,title:this.config.i18n.ajaxErrorDialogTitle,titleIcon:"circle-delete",mode:"dialog",titleIconColor:"#DD301D",wrapperClasses:"gform-dialog ",zIndex:1e5}).showDialog()}},{key:"handleSaveSuccess",value:function(e){var t=this;this.registerTimeOut(function(){t.showSaveSuccessNotification(),t.updatePageURL(),(0,o.trigger)({event:"gform/form_editor_saver/post_save_success",native:!1,data:{saveSuccessResponse:e}})},this.config.data.animationDelay)}},{key:"setInitialUrlState",value:function(){window.history.replaceState(window.form,document.title,(0,o.updateQueryVar)("gf_ajax_save",null))}},{key:"updatePageURL",value:function(){window.history.replaceState({},document.title,(0,o.updateQueryVar)("gf_ajax_save",(0,o.uniqueId)(""),(0,o.updateQueryVar)("isnew",null)))}},{key:"showSaveSuccessNotification",value:function(){var e=this.config.data.urls.formPreview,t=this.config.i18n,i=t.formUpdated,n=t.viewForm;(0,o.trigger)({data:{autoHideDelay:4500,container:"#form_editor_fields_container",ctaLink:(0,o.escapeHtml)((0,o.vsprintf)(e,[window.form.id])),ctaTarget:"_blank",ctaShowExternalLinkIcon:!0,ctaExternalLinkMessage:(0,o.escapeHtml)("(opens in a new tab)"),ctaText:(0,o.escapeHtml)(n),icon:"circle-check-alt",message:(0,o.escapeHtml)(i),type:"success"},event:"gform/snackbar/render",native:!1})}},{key:"showSaveErrorNotification",value:function(e){var t=this.config.i18n.genericError,i="string"==typeof e&&""!==e&&null!==e?e:t;(0,o.trigger)({data:{autoHideDelay:4500,container:"#form_editor_fields_container",icon:"circle-delete",message:(0,o.escapeHtml)(i),type:"error"},event:"gform/snackbar/render",native:!1})}},{key:"disableSettingsControls",value:function(){document.querySelectorAll(".field_setting input, .field_setting select, .field_setting textarea").forEach(function(e){e.setAttribute("data-js-initial-disabled-state",e.disabled),e.disabled=!0})}},{key:"resetSettingsControlsDisabledState",value:function(){document.querySelectorAll(".field_setting input, .field_setting select, .field_setting textarea").forEach(function(e){"false"===e.getAttribute("data-js-initial-disabled-state")&&e.removeAttribute("disabled"),e.removeAttribute("data-js-initial-disabled-state")})}},{key:"registerTimeOut",value:function(e,t){var i=setTimeout(e,t);return this.timeOuts.push(i),i}}])}(),ie=(null===l()||void 0===l()?void 0:l().form_editor_save_form)||{};r().instances=(null===r()||void 0===r()?void 0:r().instances)||{};var ne,oe=function(){r().instances.formSaverEventsManager=new W.A,r().instances.adminFormSaverUIHandler=new te(r().instances.formSaverEventsManager,ie),r().instances.adminFormSaver=new Q(ie,{config:ie,events:r().instances.formSaverEventsManager,endpointKey:"form_editor_save_form",form:window.form}),r().instances.adminFormSaver.init()},ae=i(8140),re=i(2595),se=i.n(re),le=function(e,t){var i,n;return function(){var o=this,a=arguments;n?(clearTimeout(i),i=setTimeout(function(){Date.now()-n>=t&&(e.apply(o,a),n=Date.now())},t-(Date.now()-n))):(e.apply(o,a),n=Date.now())}},ce=_.ReactDOM.createRoot,de={DeleteField:"delete",StartDuplicateField:"duplicate"},ue=((null===l()||void 0===l()||null===(ne=l().components)||void 0===ne?void 0:ne.dropdown_menu)||{}).i18n,fe=ue.duplicateButtonLabel,me=ue.deleteButtonLabel,ve=ue.dropdownButtonLabel,ge=function(e){return function(t){var i=t.target.closest(".gfield");if(i){var n=i.getAttribute("id").split("_"),o=(0,ae.A)(n,2),a=(o[0],o[1]),r=de[e]||e.toLowerCase(),s="gfield_".concat(r,"_").concat(a),l=document.getElementById(s);if(l&&"function"==typeof window[e]&&window[e](l),"StartDuplicateField"===e){var c=i.querySelector(".gform-button");c&&c.click()}}}},pe={iconBefore:"duplicate",iconPrefix:"gform-icon",label:fe,customClasses:["gform-compact-view-overflow-menu__item","gform-compact-view-overflow-menu__item-duplicate"],customAttributes:{onClick:ge("StartDuplicateField")}},he={iconBefore:"trash",iconPrefix:"gform-icon",label:me,customClasses:["gform-compact-view-overflow-menu__item","gform-compact-view-overflow-menu__item-delete"],customAttributes:{onClick:ge("DeleteField")}},ye=["total","paypal","captcha","turnstile","post_title","post_content","post_excerpt","total","shipping","mollie","creditcard","submit"],we=function(e){var t=function(e){var t="gfield--type-";return document.getElementById("field_".concat(e.split("_").pop())).className.split(" ").find(function(e){return e.startsWith(t)}).substring(13)}(e);return!ye.includes(t)},_e=function(){return document.getElementById("form_editor_fields_container").classList.contains("gform-compact-view")},be=function(e,t){if(t&&!e.getAttribute("data-dropdown-added"))!function(e){var t={align:"right",listItems:[we(e.id)&&{key:"duplicate",props:pe},{key:"delete",props:he}].filter(Boolean),type:"action",customClasses:["gform-compact-view-overflow-menu__container"],triggerAttributes:{icon:"ellipsis",size:"size-height-s",type:"simplified",ariaText:ve,ariaId:e.id},width:120,autoPosition:!0,customAttributes:{"aria-label":ve}};ce(e).render(_.React.createElement(se(),t))}(e),e.setAttribute("data-dropdown-added","true");else if(!t&&e.getAttribute("data-dropdown-added")){ce(e).unmount(),e.removeAttribute("data-dropdown-added")}},Se=function(e){(0,o.getNodes)('[data-js="gform-compact-view-overflow-menu"]',!0,document,!0).forEach(function(t){return be(t,e)})},ke=function(){Se(_e())},Ae=function(){var e;e=le(Se,200),_e()&&e(!0),["gform/layout_editor/field_modified","gform/form_editor/field-duplicated-native","gform/form_editor/compact-view-active","gform/form_editor/compact-view-inactive","gform/layout_editor/field_refresh_preview","gform/layout_editor/field_start_change_type"].forEach(function(e){return document.addEventListener(e,ke)}),(0,o.consoleInfo)("Gravity Forms Admin: Initialized dropdown component on event: gform/form_editor/compact-view-active")},Ee=l().components.dialog.i18n,Ce=function(e){var t=document.getElementById(e);if(t){var i=t.closest('[data-js="gform-dialog-mask"]');i&&i.remove(),t.remove()}};r().instances.dialogAlert=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=new(ee())({id:"alert-dialog-message",triggers:'[data-js="dialog-alert-message"]',closeButtonClasses:"gform-button--circular",closeButtonSize:"xs",closeButtonType:"simplified",wrapperClasses:"gform-dialog gform-dialog--alert",title:e,content:t,titleIcon:i?"circle-check-fine":"circle-delete",titleIconColor:i?"#22A752":"#DD301D",onClose:function(){return Ce(n.options.id)}});n.showDialog()},r().instances.dialogConfirmAsync=function(e,t){return new Promise(function(i){var n=new(ee())({id:"alert-dialog-confirmation",triggers:'[data-js="dialog-confirm-message"]',closeButtonClasses:"gform-button--circular",closeButtonSize:"xs",closeButtonType:"simplified",wrapperClasses:"gform-dialog gform-dialog--alert",content:t,title:e,closeButtonAriaLabel:Ee.close,animationDelay:250,closeOnMaskClick:!1,onConfirm:function(){return i(!0)},onOpen:function(){var e=document.querySelector("#".concat(n.options.id," .gform-dialog__cancel"));e&&e.addEventListener("click",function(){i(!1)})},onClose:function(){return setTimeout(function(){return Ce(n.options.id)},100)},cancelButtonText:Ee.cancel,closeButtonTitle:Ee.close,confirmButtonText:Ee.ok,titleIcon:"circle-delete",titleIconColor:"#DD301D",mode:"dialog"});n.showDialog()})};var Be=function(){r().instances=(null===r()||void 0===r()?void 0:r().instances)||{},r().instances.dialogAlert=r().instances.dialogAlert||"",r().instances.dialogConfirmAsync=r().instances.dialogConfirmAsync||"",r().components=(null===r()||void 0===r()?void 0:r().components)||{},r().components.Dialog=ee()},Ie=function(){Be()};r().instances.moveBulkChoicesBehind=function(){var e=document.getElementById("TB_window"),t=document.getElementById("TB_overlay");"visible"===e.style.visibility&&(e.style.zIndex=9,t.style.zIndex=9)};var xe=function(){r().instances=(null===r()||void 0===r()?void 0:r().instances)||{},r().instances.moveBulkChoicesBehind=r().instances.moveBulkChoicesBehind||""},Fe=function(){xe()},Te=function(){Fe(),Ie()},De=function(e){F(e),N(),oe(),Ae(),Te(),(0,o.consoleInfo)("Gravity Forms Admin: Initialized all form editor scripts.")}}}]);