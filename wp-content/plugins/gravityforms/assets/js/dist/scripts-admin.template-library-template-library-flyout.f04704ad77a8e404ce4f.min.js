"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[752],{7688:function(e,t,r){r.r(t);var n=r(1860),o=r(527),a=r(7616),i=r(5798),c=r(9877),l=r.n(c),u=r(484),s=r.n(u),f=r(7510),m=r.n(f),p=r(4496),d=r.n(p),y=r(1862),b=r.n(y),g=r(2312),v=r.n(g),h=r(2045),E=r.n(h),O=r(9049);function w(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function C(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?w(Object(r),!0).forEach(function(t){(0,o.A)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var A=a.React.useCallback,R=a.React.useEffect,P=a.React.useRef;t.default=function(e){var t=e.flyoutAttributes,r=void 0===t?{}:t,o=e.footerAttributes,c=void 0===o?{}:o,u=e.showAlert,f=void 0!==u&&u,p=e.strings,y=void 0===p?{}:p,g=e.titleInputState,h=void 0===g?{}:g,w=(0,O.default)(function(e){return e.flyoutTitleValue}),_=(0,O.default)(function(e){return e.setFlyoutTitleValue}),j=(0,O.default)(function(e){return e.flyoutDescriptionValue}),k=(0,O.default)(function(e){return e.setFlyoutDescriptionValue}),D=(0,O.default)(function(e){return e.flyoutOpen}),x=P(null);R(function(){D&&x.current&&setTimeout(function(){var e=Array.from(x.current.children).find(function(e){return"INPUT"===e.tagName});e&&e.focus()},100)},[D]),R(function(){var e=function(e){if("Enter"===e.key&&D&&x.current){var t=Array.from(x.current.children).find(function(e){return"INPUT"===e.tagName});t===document.activeElement&&N.current&&(e.preventDefault(),_(t.value),setTimeout(function(){N.current()},100))}};return document.addEventListener("keydown",e),function(){document.removeEventListener("keydown",e)}});var T={className:(0,a.classnames)({"gform-flyout__foot":!0})},z=C(C({},c.primaryButtonAttributes),{},{customClasses:(0,a.classnames)({"gform-flyout__foot-primary-button":!0}),type:"primary-new"}),N=P(z.onClick);N.current=z.onClick;var I=C(C({},c.secondaryButtonAttributes),{},{customClasses:(0,a.classnames)({"gform-flyout__foot-secondary-button":!0}),type:"white"});r.afterContent=a.React.createElement("footer",T,a.React.createElement("div",{className:"gform-flyout__foot-inner"},a.React.createElement(s(),I),a.React.createElement(s(),z)));var F=A((0,i.debounce)(_,{wait:300}),[w]),S=A((0,i.debounce)(k,{wait:300}),[j]),V=C({animationDelay:150,closeButtonCustomAttributes:{circular:!0,icon:"x",iconPrefix:"gform-common-icon",size:"size-xs",type:"simplified"},customClasses:["gform-template-library__flyout"],direction:"right",headerHeadingCustomAttributes:{size:"display-sm",weight:"semibold"},zIndex:100001},r),q={controlled:!0,id:"template-library-form-title-input",required:!0,requiredLabel:{content:"(".concat(y.required,")")},placeholder:y.titlePlaceholder,size:"size-l",labelPosition:"above",labelAttributes:{label:y.title,htmlFor:"template-library-form-title-input"},onChange:F,error:h.errorState,helpTextAttributes:{content:h.errorMessage},value:w};return a.React.createElement(E(),V,f&&a.React.createElement(v(),{content:y.upgradeAlert,contentCustomAttributes:{asHtml:!0},customClasses:["gform-template-library__flyout-alert"],isInline:!0,spacing:6,type:"error"}),a.React.createElement(l(),{spacing:6},a.React.createElement(m(),(0,n.A)({},q,{ref:x}))),a.React.createElement(l(),{spacing:6},a.React.createElement(d(),{label:y.description,htmlFor:"template-library-form-description-text"}),a.React.createElement(b(),{controlled:!0,id:"template-library-form-description-text",placeholder:y.formDescriptionPlaceHolder,onChange:S,value:j,customClasses:["gform-template-library__flyout-textarea"]})))}}}]);