"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[959],{9712:function(t,e,o){o.r(e),o.d(e,{default:function(){return a}});var s=o(5798),r=function(t){var e=t.delegateTarget,o=(0,s.getNode)("system-status-copy-label",e),r=(0,s.getNode)("system-status-copy-copied",e),n=(0,s.getNode)("system-report-text");(0,s.clipboard)(n.innerHTML)?(setTimeout(function(){i(o,r,e,!0)},100),setTimeout(function(){i(r,o,e,!1)},7e3)):(0,s.consoleInfo)("There was an error copying to clipboard.")},i=function(t,e,o,s){t.setAttribute("aria-hidden","true"),e.setAttribute("aria-hidden","false"),s?o.classList.add("gform-system-report__copy-button-copied"):o.classList.remove("gform-system-report__copy-button-copied")},n=function(t,e){e&&!confirm(e)||((0,s.getNode)("system-report-action").value=t,(0,s.getNode)("system-report-form").submit())},c=function(t){!function(t){(0,s.delegate)(t,'[data-js="gf-copy-system-report"]',"click",r)}(t),window.gfDoAction=n},a=function(t){c(t),(0,s.consoleInfo)("Gravity Forms Admin: Initialized system report")}}}]);