"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[333],{7333:function(e,r,t){t.r(r),t.d(r,{default:function(){return D}});var o=t(7940),n=t(6111),s=t(2492),a=t(7616),l=t(8335),i=t(5798),u=t(1969),p=t(2503),c=t(1025),d=t(6404),f=t(8840),y=t(9907),b=t(6793),v=function(e,r){return(0,i.slugify)("".concat(e,"-").concat(r))},m=t(1844),g=t(4965),T=t(1677),h=t(7611),P=t(6511),C=t(8456),A=/Android/i,w=function(e,r){void 0!==r&&(!function(){if("undefined"!=typeof navigator)return A.test(navigator.userAgent)}()?e.setSelectionRange(r,r):setTimeout(function(){return e.setSelectionRange(r,r)},0))},j=function(e,r,t,o,n){var s=new C.Q(t);s.input(e);var a=(0,g.A)(s.getTemplate(),function(e){return function(r,t){return e&&"+"===r&&!t?r:(0,m.A)(r)}}(o)),l=(0,T.A)(e,r,a),i=l.value,u=l.caret;if(n){var p=function(e,r,t){switch(t){case"Backspace":r>0&&(e=e.slice(0,r-1)+e.slice(r),r--);break;case"Delete":e=e.slice(0,r)+e.slice(r+1)}return{value:e,caret:r}}(i,u,n);i=p.value,u=p.caret}s.reset(),s.input(i);var c=(0,h.A)(s.getTemplate(),"x",!0);return{asYouType:s,formatted:(0,P.A)(i,u,c)}};function O(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,o)}return t}function x(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?O(Object(t),!0).forEach(function(r){(0,n.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):O(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var _=a.React.useEffect,R=a.React.useRef,q=a.React.useState,E=a.React.forwardRef,V=".gform-input",S="default",k="error",z=E(function(e,r){var t=e.countries,m=e.customAttributes,g=e.customClasses,T=e.disabled,h=e.dropdownAttributes,P=e.dropdownClasses,C=e.helpTextAttributes,A=e.helpTextClasses,O=e.i18n,E=e.inputAttributes,z=e.inputClasses,D=e.international,Y=e.label,L=e.labelAttributes,N=e.labelClasses,B=e.language,M=e.onChange,F=e.preferredCountries,I=e.required,K=e.requiredLabelAttributes,H=e.requiredLabelClasses,Q=e.size,$=e.spacing,G=e.usePlaceholder,J=e.useValidation,U=e.width,W=R(null),X=r||W,Z=R(null),ee=R(null),re=R(null),te=(0,l.useIdContext)(),oe=q(function(){var e=(0,f.h$)(t,B,O,{showFlag:(null==h?void 0:h.showFlag)||!0,showCallingCode:(null==h?void 0:h.showCallingCode)||!0});if(null!=h&&h.initialValue)return(0,c.Oj)(null==h?void 0:h.initialValue,e,!1);if(F.length){var r,o,n=(0,f.oL)(e,F,O);return"group"===(null==n||null===(r=n[0])||void 0===r?void 0:r.type)?(null==n||null===(o=n[0])||void 0===o||null===(o=o.items)||void 0===o?void 0:o[0])||{}:null==n?void 0:n[0]}return null!=e&&e[0]?null==e?void 0:e[0]:{}}),ne=(0,s.A)(oe,2),se=ne[0],ae=ne[1],le=q(function(){if(null!=E&&E.value){var e=j(null==E?void 0:E.value,0,null==se?void 0:se.value,D),r=e.asYouType,t=e.formatted;return{asYouType:r,chars:(null==r?void 0:r.getChars())||"",value:t.text,numberValue:(null==r?void 0:r.getNumberValue())||""}}return{asYouType:null,chars:"",value:"",numberValue:""}}),ie=(0,s.A)(le,2),ue=ie[0],pe=ie[1],ce=q(S),de=(0,s.A)(ce,2),fe=de[0],ye=de[1];_(function(){ee.current&&re.current!==ee.current.querySelector(V)&&(re.current=ee.current.querySelector(V))},[ee,re]);var be=function(){if(J){var e=ue.asYouType,r=ue.numberValue,t=null==e?void 0:e.isPossible();ye(r?t?"correct":k:S)}else fe!==S&&ye(S)},ve=function(e,r,t){var o=re.current,n=o.selectionStart,s=j(e,n,null==se?void 0:se.value,D,r),a=s.asYouType,l=s.formatted,i=(null==a?void 0:a.getNumberValue())||"";pe({asYouType:a,chars:(null==a?void 0:a.getChars())||"",value:l.text,numberValue:i});var u={country:null==se?void 0:se.value,number:i,isValid:(null==a?void 0:a.isPossible())||!1};requestAnimationFrame(function(){w(o,l.caret)}),M(u,t)},me=v(te,"help-text"),ge=v(te,"label"),Te=x({className:(0,a.classnames)(x((0,n.A)((0,n.A)({"gform-phone":!0,"wp-exclude-emoji":!0},"gform-phone--size-".concat(Q),!0),"gform-phone--disabled",T),(0,i.spacerClasses)($)),g),id:te,style:{width:U?"".concat(U,"px"):void 0}},m),he=x(x({className:(0,a.classnames)(["gform-phone__label","gform-text","gform-text--color-port","gform-typography--size-text-sm","gform-typography--weight-medium"],N)},L),{},{id:ge,onClick:function(){var e,r=null==Z||null===(e=Z.current)||void 0===e?void 0:e.querySelector(".gform-dropdown__trigger");r&&r.focus()}}),Pe=x(x({size:"text-sm",weight:"medium"},K),{},{customClasses:(0,a.classnames)(["gform-phone__required"],H)}),Ce=x(x({countries:t,hasSearch:!0,popoverMaxHeight:300},h),{},{customClasses:(0,a.classnames)(["gform-phone__dropdown"],P),disabled:T,i18n:O,id:v(te,"dropdown"),language:B,onChange:function(e,r){ae(r);var t=j(ue.chars,0,null==r?void 0:r.value,D),o=t.asYouType,n=t.formatted,s=(null==o?void 0:o.getNumberValue())||"";pe({asYouType:o,chars:(null==o?void 0:o.getChars())||"",value:n.text,numberValue:s});var a={country:null==r?void 0:r.value,number:s,isValid:(null==o?void 0:o.isPossible())||!1};M(a,e),requestAnimationFrame(be)},preferredCountries:F,searchAttributes:x({wrapperClasses:["gform-phone__dropdown-search-wrapper"]},(null==h?void 0:h.searchAttributes)||{}),searchClasses:["gform-phone__dropdown-search"],size:Q,triggerAttributes:{"aria-labelledby":Y?ge:void 0},triggerClasses:["gform-phone__dropdown-trigger"]}),Ae=x(x({},E),{},{borderStyle:fe,customAttributes:{"aria-describedby":C.content&&fe===k?me:void 0,"aria-labelledby":Y?ge:void 0},customClasses:(0,a.classnames)(["gform-phone__input"],z),directControlled:!0,disabled:T,id:v(te,"input"),onBlur:function(e){var r;null!=E&&E.onBlur&&(null==E||null===(r=E.onBlur)||void 0===r||r.call(E,e));e.defaultPrevented||be()},onChange:function(e,r){ve(e,void 0,r)},onKeyDown:function(e){var r;null!=E&&E.onKeyDown&&(null==E||null===(r=E.onKeyDown)||void 0===r||r.call(E,e));if(!e.defaultPrevented){var t=re.current;if(!t.hasAttribute("readonly")){var o=e.key;if(["Backspace","Delete"].includes(o)){e.preventDefault();var n=t.value,s=function(e){if(e.selectionStart!==e.selectionEnd)return{start:Math.min(e.selectionStart,e.selectionEnd),end:Math.max(e.selectionStart,e.selectionEnd)}}(t),a=s?function(e,r){var t=Math.min(r.start,r.end),o=Math.max(r.start,r.end);return e.slice(0,t)+e.slice(o)}(n,s):n;ve(a,s?void 0:o,e)}}}},placeholder:function(){if(G){var e=(0,p.a)(null==se?void 0:se.value,u.A);if(e)return D?e.formatInternational():e.formatNational()}}(),required:I,size:"size-".concat(Q),type:"tel",value:ue.value,wrapperClasses:(0,a.classnames)({"gform-phone__input-wrapper":!0},(null==E?void 0:E.wrapperClasses)||[])}),we=x(x({size:"text-xs",weight:"regular"},C),{},{customClasses:(0,a.classnames)(["gform-phone__help-text"],A),id:me});return a.React.createElement("div",(0,o.A)({},Te,{ref:X}),Y&&a.React.createElement("div",he,Y,I&&a.React.createElement(y.A,Pe)),a.React.createElement("div",{className:"gform-phone__wrapper"},a.React.createElement(d.A,(0,o.A)({},Ce,{ref:Z})),a.React.createElement(b.A,(0,o.A)({},Ae,{ref:ee}))),fe===k&&a.React.createElement(y.A,we))});z.propTypes={countries:a.PropTypes.array,customAttributes:a.PropTypes.object,customClasses:a.PropTypes.oneOfType([a.PropTypes.string,a.PropTypes.array,a.PropTypes.object]),disabled:a.PropTypes.bool,dropdownAttributes:a.PropTypes.object,dropdownClasses:a.PropTypes.oneOfType([a.PropTypes.string,a.PropTypes.array,a.PropTypes.object]),helpTextAttributes:a.PropTypes.object,helpTextClasses:a.PropTypes.oneOfType([a.PropTypes.string,a.PropTypes.array,a.PropTypes.object]),i18n:a.PropTypes.object,id:a.PropTypes.string,inputAttributes:a.PropTypes.object,inputClasses:a.PropTypes.oneOfType([a.PropTypes.string,a.PropTypes.array,a.PropTypes.object]),international:a.PropTypes.bool,label:a.PropTypes.string,labelAttributes:a.PropTypes.object,labelClasses:a.PropTypes.oneOfType([a.PropTypes.string,a.PropTypes.array,a.PropTypes.object]),language:a.PropTypes.string,onChange:a.PropTypes.func,preferredCountries:a.PropTypes.array,required:a.PropTypes.bool,requiredLabelAttributes:a.PropTypes.object,requiredLabelClasses:a.PropTypes.oneOfType([a.PropTypes.string,a.PropTypes.array,a.PropTypes.object]),size:a.PropTypes.oneOf(["r","l","xl"]),spacing:a.PropTypes.oneOfType([a.PropTypes.string,a.PropTypes.number,a.PropTypes.array,a.PropTypes.object]),usePlaceholder:a.PropTypes.bool,useValidation:a.PropTypes.bool,width:a.PropTypes.number};var D=E(function(e,r){var t=x(x({},{countries:[],customAttributes:{},customClasses:[],disabled:!1,dropdownAttributes:{},dropdownClasses:[],helpTextAttributes:{},helpTextClasses:[],i18n:{},id:"",inputAttributes:{},inputClasses:[],international:!0,label:"",labelAttributes:{},labelClasses:[],language:"en",onChange:function(){},preferredCountries:[],required:!1,requiredLabelAttributes:{},requiredLabelClasses:[],size:"r",spacing:"",usePlaceholder:!0,useValidation:!1,width:0}),e),n={id:t.id};return a.React.createElement(l.IdProvider,n,a.React.createElement(z,(0,o.A)({},t,{ref:r})))})}}]);