"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[129],{5674:function(t,e,o){o.r(e),o.d(e,{default:function(){return U}});var i,s,n,r,a,d,c,l=o(5798),p=o(527),u=o(9662),m=o.n(u),f=o(1533),g=o.n(f),h=o(455),v=o(6020),b=o(1873),y=o(7113),_=o(9280),w=o.n(_),T=o(7122),k=o.n(T),C=o(1866),j=o(6900),O=o(3963),A=o.n(O),P=o(7195),x=o.n(P),D=o(2685),H=o.n(D),S=o(8150),L=o(5973),E=o.n(L),I=o(5695);function B(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),o.push.apply(o,i)}return o}function N(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?B(Object(o),!0).forEach(function(e){(0,p.A)(t,e,o[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):B(Object(o)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))})}return t}var M,G=function(){return(0,y.A)(function t(){var e,o,i,s,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,b.A)(this,t),this.options=(0,l.deepMerge)({data:{},endpoints:{},dialog:{closeOnConfirmClick:!1,closeOnMaskClick:!1,confirmButtonIcon:"floppy-disk",id:"dialog-embed-form-unsaved-changes",mode:"dialog",titleIcon:"circle-delete",titleIconColor:"#DD301D",wrapperClasses:"gform-dialog gform-dialog--embed-form-unsaved",zIndex:1e5},dialogLoader:{additionalClasses:"gform-dialog__confirm-loader",background:"#3e7da6",foreground:"#fff",mask:!1,showOnRender:!1,size:1.5},flyout:{closeOnOutsideClick:!1,maxWidth:540,mobileBreakpoint:1200,position:"absolute",simplebar:!0,target:'[data-js="form-editor"]',triggers:'[data-js="embed-flyout-trigger"]',wrapperClasses:"gform-flyout gform-flyout--embed-form",zIndex:95},i18n:{},urls:{}},n),(0,l.trigger)({event:"gform/embed_form/pre_init",native:!1,data:{instance:this}}),(0,l.isEmptyObject)(this.options.data)||(0,l.isEmptyObject)(this.options.i18n)?(0,l.consoleError)("The embed form component requires data and language strings to instantiate."):(this.instances={},this.elements={},this.properties={postTypes:(null===(e=this.options.data)||void 0===e?void 0:e.post_types)||[]},this.state={addToActiveCPT:null!==(o=this.properties.postTypes)&&void 0!==o&&o[0]?this.properties.postTypes[0].slug:"",createNewActiveCPT:null!==(i=this.properties.postTypes)&&void 0!==i&&i[0]?this.properties.postTypes[0].slug:"",isMock:"mock_endpoint"===(null===(s=this.options.endpoints)||void 0===s||null===(s=s.create_post_with_block)||void 0===s?void 0:s.action),redirectRequested:!1,redirectType:""},this.init())},[{key:"redirectToEditor",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],o="".concat((0,l.sprintf)(this.options.urls.edit_post,t)).concat(e?"&gfAddBlock=".concat(this.options.data.form_id):"");if(this.state.isMock)return(0,l.consoleInfo)("Currently in mock state, if live would have redirected to: ".concat(o)),o;window.location.href=o}},{key:"getGroupHTML",value:function(t){return'<div class="gform-embed-form__flyout-group" data-js="embed-flyout-group">'.concat(t,"</div>")}},{key:"getGroupTitle",value:function(t){return(0,l.saferHtml)(i||(i=(0,v.A)(['<h6 class="gform-embed-form__group-title">',"</h6>"])),t)}},{key:"getGroupActionButton",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return'<div class="gform-embed-form__flyout-group-footer">'.concat((0,j.buttonTemplate)({attributes:'data-js="'.concat(e,'"'),label:(0,l.escapeHtml)(t),type:"white"}),"</div>")}},{key:"getPostTypeSwitcher",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return'<article class="gform-embed-form__post-type-switcher">'.concat(this.properties.postTypes.map(function(i,r){var a={name:t,"data-js":"post-type-switcher","data-type":i.slug};return 0===r&&(a.checked="checked"),(0,S.inputTemplate)({customAttributes:a,label:(0,l.sprintf)(o,'<span class="gform-embed-form__visually-hidden">',"</span>",(0,l.escapeHtml)(i.label)),id:(0,l.saferHtml)(s||(s=(0,v.A)(["","",""])),e,i.slug),type:"radio",value:(0,l.saferHtml)(n||(n=(0,v.A)(["",""])),i.slug)})}).join(""),"</article>")}},{key:"getFormIdHtml",value:function(){var t=(0,I.statusIndicatorTemplate)({hasDot:!1,isStatic:!0,label:(0,l.saferHtml)(r||(r=(0,v.A)(["",""])),(0,l.vsprintf)(this.options.i18n.id,[this.options.data.form_id])),pill:!1,status:"inactive"});return'<div class="gform-embed-form__form-id"><p>'.concat(t,"</p></div>")}},{key:"getDropdownOptions",value:function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=(null===(t=this.options.data)||void 0===t||null===(t=t.items)||void 0===t?void 0:t[e.slug])||{};return{attributes:'data-type="'.concat(e.slug,'"'),dropdownListAttributes:"data-simplebar",hasSearch:o.count>5,listData:o.entries||[],searchAriaText:(0,l.escapeHtml)((0,l.vsprintf)(this.options.i18n.add_search_aria_text,[e.slug])),searchInputId:(0,l.saferHtml)(a||(a=(0,v.A)(["gform-form-switcher-search-add-to-form-",""])),e.slug),searchPlaceholder:(0,l.escapeHtml)((0,l.vsprintf)(this.options.i18n.add_search_aria_text,[e.slug])),selector:"gform-dropdown-add-to-form-".concat(e.slug),triggerAriaId:"gform-form-switcher-label-add-to-form-".concat(e.slug),triggerAriaText:(0,l.escapeHtml)(this.options.i18n.add_trigger_aria_text),triggerId:"gform-form-switcher-control-add-to-form-".concat(e.slug),triggerPlaceholder:(0,l.escapeHtml)((0,l.vsprintf)(this.options.i18n.add_dropdown_placeholder,[e.label])),wrapperClasses:"gform-dropdown gform-embed-form__dropdown"}}},{key:"getAddToDropdowns",value:function(){var t=this;return this.properties.postTypes.map(function(e,o){return'\n\t\t\t\t<article\n\t\t\t\t\tclass="gform-embed-form__dropdown-wrapper'.concat(0!==o?" gform-embed-form--hidden":"",'"\n\t\t\t\t\tdata-js="embed-flyout-post-type-action-container"\n\t\t\t\t\tdata-type="').concat((0,l.escapeHtml)(e.slug),'"\n\t\t\t\t>\n\t\t\t\t\t').concat((0,P.dropdownTemplate)(t.getDropdownOptions(e)),"\n\t\t\t\t</article>\n\t\t")}).join("")}},{key:"getCreateNewInputs",value:function(){var t=this;return this.properties.postTypes.map(function(e,o){var i={customAttributes:{"data-js":"create-new-post-input","data-type":e.slug},placeholder:(0,l.escapeHtml)((0,l.vsprintf)(t.options.i18n.create_placeholder,[e.label])),type:"text"};return'\n\t\t\t\t<article\n\t\t\t\t\tclass="gform-embed-form__create-input-wrapper'.concat(0!==o?" gform-embed-form--hidden":"",'"\n\t\t\t\t\tdata-js="embed-flyout-post-type-action-container"\n\t\t\t\t\tdata-type="').concat((0,l.escapeHtml)(e.slug),'"\n\t\t\t\t>\n\t\t\t\t\t').concat((0,S.inputTemplate)(i),"\n\t\t\t\t</article>\n\t\t")}).join("")}},{key:"getAddToExistingContentHtml",value:function(){var t=this.getGroupTitle(this.options.i18n.add_title);return t+=this.getPostTypeSwitcher("add_post_type","embed-form-add-to-post-",this.options.i18n.add_post_type_choice_label),t+=this.getAddToDropdowns(),t+=this.getGroupActionButton(this.options.i18n.add_button_label,"embed-form-add-to-post-trigger"),this.getGroupHTML(t)}},{key:"getCreateNewContentHtml",value:function(){var t=this.getGroupTitle(this.options.i18n.create_title);return t+=this.getPostTypeSwitcher("create_new_in_post_type","embed-form-create-new-",this.options.i18n.create_post_type_choice_label),t+=this.getCreateNewInputs(),t+=this.getGroupActionButton(this.options.i18n.create_button_label,"embed-form-create-post-trigger"),this.getGroupHTML(t)}},{key:"getShortcodeTrigger",value:function(){var t=(0,l.sprintf)((0,l.escapeHtml)(this.options.i18n.shortcode_helper),'<a href="'.concat(this.options.urls.shortcode_docs,'" rel="noopener" target="_blank">'),'<span class="screen-reader-text">'.concat(this.options.i18n.shortcode_external_link_screen_reader_text,'</span>&nbsp;<span class="gform-icon gform-icon--external-link"></span></a>')),e=(0,l.saferHtml)(d||(d=(0,v.A)(['\n\t\t\t<span class="gform-embed-form__shortcode-copy-label" data-js="shortcode-copy-label" aria-hidden="false">','</span>\n\t\t\t<span class="gform-embed-form__shortcode-copy-copied" data-js="shortcode-copy-copied" aria-hidden="true">\n\t\t\t\t<i class="gform-embed-form__shortcode-copy-icon gform-icon gform-icon--circle-check-alt"></i>\n\t\t\t\t',"\n\t\t\t</span>\n\t\t"])),this.options.i18n.shortcode_button_label,this.options.i18n.shortcode_button_copied);return'<div class="gform-embed-form__shortcode-footer" aria-live="assertive">'.concat((0,j.buttonTemplate)({attributes:'data-js="embed-form-shortcode-trigger"',customClasses:["gform-embed-form__shortcode-trigger"],html:e,icon:"copy",label:"",type:"white"}),'<p class="gform-embed-form__shortcode-footer-helper">').concat(t,"</p></div>")}},{key:"getShortcodeHtml",value:function(){var t=this.getGroupTitle(this.options.i18n.shortcode_title);return t+=(0,l.saferHtml)(c||(c=(0,v.A)(['<article class="gform-embed-form__shortcode-description"><p>',"</p></article>"])),this.options.i18n.shortcode_description),t+=this.getShortcodeTrigger(),this.getGroupHTML(t)}},{key:"generateFlyoutContent",value:function(){var t=this.getFormIdHtml();return t+=this.getAddToExistingContentHtml(),t+=this.getCreateNewContentHtml(),t+=this.getShortcodeHtml()}},{key:"resetConfirmDialogState",value:function(t){var e=this.instances.dialog.elements,o=e.cancelButton,i=e.closeButton,s=e.confirmButton;o.disabled=!1,i.disabled=!1,s.disabled=!1,s.style.width="",this.instances.dialogLoader.hideLoader(),s.classList.remove("gform-dialog__confirm-button--saving"),t&&"gform/form_editor_saver/post_save_error"===t.type&&(this.state.redirectRequested=!1,this.state.redirectType="")}},{key:"handleDialogConfirm",value:function(){var t=this.instances.dialog.elements,e=t.cancelButton,o=t.closeButton,i=t.confirmButton;e.disabled=!0,o.disabled=!0,i.disabled=!0,i.style.width="".concat(i.offsetWidth,"px"),this.instances.dialogLoader.showLoader(),i.classList.contains("gform-dialog__confirm-saving--initialized")||(i.classList.add("gform-dialog__confirm-saving--initialized"),i.insertAdjacentHTML("beforeend",'\n\t\t\t\t<span class="gform-dialog__confirm-saving-text gform-button__text gform-button__text--active">'.concat(this.options.i18n.dialog_confirm_saving,"</span>\n\t\t\t"))),i.classList.add("gform-dialog__confirm-button--saving")}},{key:"wrapDialogConfirmText",value:function(){var t=this.instances.dialog.elements.confirmButton.innerHTML;this.instances.dialog.elements.confirmButton.innerHTML='<span class="gform-dialog__confirm-button--idle-text">'.concat(t,"</span>")}},{key:"render",value:function(){this.instances.flyout=new(H())(N({content:this.generateFlyoutContent(),title:this.options.i18n.title},this.options.flyout)),this.instances.dialog=new(A())(N({cancelButtonText:this.options.i18n.dialog_cancel_text,closeButtonTitle:this.options.i18n.dialog_close_title,closeButtonSize:"xs",confirmButtonText:this.options.i18n.dialog_confirm_text,content:this.options.i18n.dialog_content,onConfirm:this.handleDialogConfirm.bind(this),title:this.options.i18n.dialog_title},this.options.dialog)),this.wrapDialogConfirmText(),this.instances.dialogLoader=new(E())(N({target:"#".concat(this.instances.dialog.elements.confirmButton.id)},this.options.dialogLoader))}},{key:"storeElements",value:function(){var t=this.instances.flyout.elements.flyout;this.elements={addToExistingDropdowns:(0,l.getNodes)(".gform-embed-form__dropdown",!0,t,!0),addToExistingTrigger:(0,l.getNodes)("embed-form-add-to-post-trigger",!1,t)[0],createNewInputs:(0,l.getNodes)("create-new-post-input",!0,t),createNewTrigger:(0,l.getNodes)("embed-form-create-post-trigger",!1,t)[0],shortcodeTrigger:(0,l.getNodes)("embed-form-shortcode-trigger",!1,t)[0]}}},{key:"handlePostTypeSwitcherChange",value:function(t){var e=t.delegateTarget,o=(0,l.getClosest)(e,'[data-js="embed-flyout-group"]');"create_new_in_post_type"===e.name?this.state.createNewActiveCPT=e.value:this.state.addToActiveCPT=e.value,(0,l.getNodes)("embed-flyout-post-type-action-container",!0,o).forEach(function(t){t.dataset.type===e.dataset.type?t.classList.remove("gform-embed-form--hidden"):t.classList.add("gform-embed-form--hidden")})}},{key:"handlePostSaveRedirect",value:function(){this.state.redirectRequested&&(this.resetConfirmDialogState(),"addToPost"===this.state.redirectType?this.handleAddToPost():"createPost"===this.state.redirectType&&this.handleCreatePost(),this.state.redirectRequested=!1,this.state.redirectType="")}},{key:"handleAddToPost",value:function(){var t=this,e=this.elements.addToExistingDropdowns.filter(function(e){return e.dataset.type===t.state.addToActiveCPT})[0],o=(0,l.getNodes)("gform-dropdown-control",!1,e)[0];if(o.dataset.value){if((0,l.isFormDirty)())return this.state.redirectRequested=!0,this.state.redirectType="addToPost",void this.instances.dialog.showDialog();this.instances.dialog.closeDialog(),this.redirectToEditor(o.dataset.value)}else o.focus()}},{key:"handleCreatePost",value:(t=(0,h.A)(w().mark(function t(){var e,o,i,s,n,r=this;return w().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e=this.elements.createNewInputs.filter(function(t){return t.dataset.type===r.state.createNewActiveCPT})[0],o=(0,l.escapeHtml)(e.value.trim())){t.next=5;break}return e.focus(),t.abrupt("return");case 5:if(!(0,l.isFormDirty)()){t.next=11;break}return this.state.redirectRequested=!0,this.state.redirectType="createPost",this.instances.dialog.showDialog(),t.abrupt("return");case 11:if(this.instances.dialog.closeDialog(),i={baseUrl:k(),method:"POST",body:{form_id:this.options.data.form_id,post_title:o,post_type:e.dataset.type}},!this.state.isMock){t.next=18;break}(0,l.consoleInfo)("Mock endpoint, data that would have been sent is:"),(0,l.consoleInfo)(i),t.next=22;break;case 18:return t.next=20,(0,C.Ay)("create_post_with_block",this.options.endpoints,i);case 20:null!=(n=t.sent)&&null!==(s=n.data)&&void 0!==s&&s.success&&this.redirectToEditor(n.data.data.ID,!1);case 22:case"end":return t.stop()}},t,this)})),function(){return t.apply(this,arguments)})},{key:"handleCopyShortcodeClick",value:function(t){var e=t.delegateTarget,o=(0,l.getNodes)("shortcode-copy-label",!1,e)[0],i=(0,l.getNodes)("shortcode-copy-copied",!1,e)[0],s='[gravityform id="'.concat(this.options.data.form_id,'" title="true"]');(0,l.clipboard)(s),setTimeout(function(){o.setAttribute("aria-hidden","true"),i.setAttribute("aria-hidden","false"),e.classList.add("gform-embed-form__shortcode-trigger--copied")},100),setTimeout(function(){o.setAttribute("aria-hidden","false"),i.setAttribute("aria-hidden","true"),e.classList.remove("gform-embed-form__shortcode-trigger--copied")},2e3)}},{key:"bindDropdowns",value:function(){var t=this;this.instances.dropdowns={},this.properties.postTypes.forEach(function(e){t.instances.dropdowns["gform-dropdown-add-to-form-".concat(e.slug)]=new(x())({baseUrl:k(),endpoints:t.options.endpoints,endpointArgs:{post_type:e.slug},endpointKey:"get_posts",listData:t.options.data.items[e.slug].entries,searchType:"async",selector:"gform-dropdown-add-to-form-".concat(e.slug)})})}},{key:"flyoutShouldStayOpen",value:function(t){var e=this.instances.flyout,o=e.elements.flyout,i=e.state;return o.contains(t)||!i.open||(0,l.getClosest)(t,'[data-js="gform-dialog-mask"]')||"gform-dialog-mask"===t.dataset.js}},{key:"bindEvents",value:function(){var t=this.instances.flyout,e=t.elements.flyout,o=t.closeFlyout;(0,l.delegate)(e,'[data-js="post-type-switcher"]',"change",this.handlePostTypeSwitcherChange.bind(this)),(0,l.delegate)(e,'[data-js="embed-form-add-to-post-trigger"]',"click",this.handleAddToPost.bind(this)),(0,l.delegate)(e,'[data-js="embed-form-create-post-trigger"]',"click",this.handleCreatePost.bind(this)),(0,l.delegate)(e,'[data-js="embed-form-shortcode-trigger"]',"click",this.handleCopyShortcodeClick.bind(this)),document.addEventListener("gform/form_editor_saver/post_save_success",this.handlePostSaveRedirect.bind(this)),document.addEventListener("gform/form_editor_saver/post_save_error",this.resetConfirmDialogState.bind(this)),document.addEventListener("click",function(t){this.flyoutShouldStayOpen(t.target)||o()}.bind(this))}},{key:"init",value:function(){this.render(),this.storeElements(),this.bindDropdowns(),this.bindEvents(),(0,l.trigger)({event:"gform/embed_form/post_render",native:!1,data:{instance:this}})}}]);var t}();function F(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),o.push.apply(o,i)}return o}var R=(null===g()||void 0===g()||null===(M=g().components)||void 0===M?void 0:M.embed_form)||{};m().instances=(null===m()||void 0===m()?void 0:m().instances)||{},m().components=(null===m()||void 0===m()?void 0:m().components)||{};var q=function(){m().instances.embedForm=new G(function(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?F(Object(o),!0).forEach(function(e){(0,p.A)(t,e,o[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):F(Object(o)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))})}return t}({},R))},z=function(){q()};var U=function(){z(),(0,l.consoleInfo)("Gravity Forms Admin: Initialized embed form component.")}}}]);