<?php
return array(
	'basic_contact'        => array(
		'id'                    => 'basic_contact',
		'description'           => esc_html__( 'A simple and basic contact form with only two fields', 'gravityforms' ),
		'title'                 => esc_html__( 'Simple Contact Form', 'gravityforms' ),
		'template_background'   => 'sazerac-yellow',
		'template_thumbnail'    => 'BasicContact.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/contact-form/',
		'template_access_level' => [ 'godaddy', 'gravityflow', 'gravityview', 'single', 'ltsingle', 'basic', 'multi', 'ltmulti', 'pro', 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'labelPlacement'             => 'top_label',
			'useCurrentUserAsAuthor'     => '1',
			'postAuthor'                 => '1',
			'postCategory'               => '1',
			'postStatus'                 => 'draft',
			'title'                      => esc_html__( 'Simple Contact Form', 'gravityforms' ),
			'description'                => esc_html__( 'Please get in contact using the form below...', 'gravityforms' ),
			'maxEntriesAllowed'          => '0',
			'button'                     => array(
				'type'             => 'text',
				'text'             => esc_html__( 'Submit', 'gravityforms' ),
				'imageUrl'         => '',
				'conditionalLogic' => null,
			),
			'fields'                     => array(
				array(
					'type'                  => 'name',
					'id'                    => 1,
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'label'                 => esc_html__( 'Name', 'gravityforms' ),
					'inputs'                => array(
						array(
							'id'                    => '1.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'       => esc_html__( 'Mr.', 'gravityforms' ),
									'value'      => 'Mr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Mrs.', 'gravityforms' ),
									'value'      => 'Mrs.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Miss', 'gravityforms' ),
									'value'      => 'Miss',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Ms.', 'gravityforms' ),
									'value'      => 'Ms.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Dr.', 'gravityforms' ),
									'value'      => 'Dr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Prof.', 'gravityforms' ),
									'value'      => 'Prof.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Rev.', 'gravityforms' ),
									'value'      => 'Rev.',
									'isSelected' => false,
									'price'      => '',
								),
							),
							'isHidden'  => true,
							'inputType' => 'radio',
						),
						array(
							'id'                    => '1.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'placeholder'           => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '1.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '1.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'placeholder'           => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '1.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'formId'                => 84,
					'inputType'             => '',
					'displayOnly'           => '',
					'nameFormat'            => 'advanced',
					'choices'               => '',
					'conditionalLogic'      => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'allowsPrepopulate'     => false,
					'useRichTextEditor'     => false,
					'defaultValue'          => '',
					'description'           => '',
					'visibility'            => 'visible',
					'fields'                => '',
					'layoutGroupId'         => '755102fe',
					'adminLabel'            => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'email',
					'id'                    => 2,
					'isRequired'            => true,
					'size'                  => 'large',
					'errorMessage'          => 'Please supply a valid email address',
					'label'                 => esc_html__( 'Email', 'gravityforms' ),
					'formId'                => 84,
					'inputType'             => '',
					'displayOnly'           => '',
					'inputs'                => array(
						array(
							'id'                    => '2',
							'label'                 => esc_html__( 'Enter Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
						array(
							'id'                    => '2.2',
							'label'                 => esc_html__( 'Confirm Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
					),
					'choices'               => '',
					'conditionalLogic'      => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'allowsPrepopulate'     => false,
					'useRichTextEditor'     => false,
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'layoutGroupId'         => '17f293c9',
					'autocompleteAttribute' => '',
					'emailConfirmEnabled'   => true,
					'adminLabel'            => '',
					'description'           => '',
					'maxLength'             => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'enableAutocomplete'    => true,
				),
				array(
					'type'                  => 'textarea',
					'id'                    => 3,
					'isRequired'            => true,
					'size'                  => 'medium',
					'label'                 => esc_html__( 'Comments', 'gravityforms' ),
					'formId'                => 84,
					'inputType'             => '',
					'displayOnly'           => '',
					'inputs'                => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'allowsPrepopulate'     => false,
					'useRichTextEditor'     => false,
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'description'           => esc_html__( 'Please let us know what\'s on your mind. Have a question for us? Ask away.', 'gravityforms' ),
					'defaultValue'          => '',
					'checkboxLabel'         => '',
					'maxLength'             => 600,
					'layoutGroupId'         => '2de1220e',
					'adminLabel'            => '',
					'errorMessage'          => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
			),
			'descriptionPlacement'       => 'above',
			'id'                         => 84,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => null,
			'pagination'                 => null,
			'firstPageCssClass'          => null,
			'nextFieldId'                => 13,
			'subLabelPlacement'          => 'above',
			'cssClass'                   => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'feeds'                      => array(
				'gravityformsadvancedpostcreation' => array(),
			),
			'version'                    => '2.7',
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'customRequiredIndicator'    => '',
			'markupVersion'              => 2,
			'notifications'              => array(
				'51794abf1f0d1' => array(
					'id'      => '51794abf1f0d1',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'We have received your inquiry', 'gravityforms' ),
					'message' => wp_kses_post( __( '<p>Hi there {Name (First):1.3},</p><p>Thank you for getting in touch. We have received your inquiry and will get back to you within one business day.</p>','gravityforms' ) ),
					'toType'  => 'field',
					'toField' => '2',
					'to' => '2',
					'event'   => 'form_submission',
					'name'    => 'User Notification',
					'type'    => 'user',
				),
				'51794abf1f0d2' => array(
					'id'      => '51794abf1f0d2',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'New submission from {form_title}', 'gravityforms' ),
					'message' => '{all_fields}',
					'toType'  => 'email',
					'to' => '{admin_email}',
					'event'   => 'form_submission',
					'name'    => 'Admin Notification',
				),
			),
			'confirmations'              => array(
				'5179518e5e160' => array(
					'type'              => 'message',
					'id'                => '5179518e5e160',
					'isDefault'         => true,
					'message'           => esc_html__( 'Thank you for contacting us! We will get in touch with you shortly.', 'gravityforms' ),
					'name'              => 'Default Confirmation',
					'disableAutoformat' => false,
					'pageId'            => 0,
					'url'               => '',
					'queryString'       => '',
					'conditionalLogic'  => array(),
				),
			),
			'autoResponder'              => array(
				'toField' => '2',
				'from'    => '{admin_email}',
				'subject' => 'Submission from {Name (First):1.3} - {Email:2}',
				'message' => esc_html__( 'Hi there {Name (First):1.3}. We received the following information from you and will respond to your inquiry as quickly as possible.', 'gravityforms' ) . '{all_fields}',
			),
			'delete_entry'               => '',
			'delete_entry_condition'     => '',
			'delete_entry_period'        => '',
			'delete_entry_units'         => 'hour',
		),
		'version'               => '2.7',
	),
	'advanced_contact'     => array(
		'id'                    => 'advanced_contact',
		'description'           => esc_html__( 'An advanced contact form.', 'gravityforms' ),
		'title'                 => esc_html__( 'Advanced Contact Form', 'gravityforms' ),
		'template_background'   => 'porcelain-gray',
		'template_thumbnail'    => 'AdvanceContact.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/advanced-contact-form/',
		'template_access_level' => [ 'godaddy', 'gravityflow', 'gravityview', 'single', 'ltsingle', 'basic', 'multi', 'ltmulti', 'pro', 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'labelPlacement'             => 'top_label',
			'useCurrentUserAsAuthor'     => '1',
			'postAuthor'                 => '1',
			'postCategory'               => '1',
			'postStatus'                 => 'draft',
			'title'                      => esc_html__( 'Advanced Contact Form', 'gravityforms' ),
			'description'                => esc_html__( 'We would love to hear from you! Please fill out this form and we will get in touch with you shortly.', 'gravityforms' ),
			'maxEntriesAllowed'          => '0',
			'button'                     => array(
				'type'             => 'text',
				'text'             => esc_html__( 'Submit', 'gravityforms' ),
				'imageUrl'         => '',
				'conditionalLogic' => null,
			),
			'fields'                     => array(
				array(
					'type'                  => 'section',
					'id'                    => 7,
					'size'                  => 'medium',
					'displayOnly'           => '1',
					'description'           => '',
					'label'                 => esc_html__( 'About You', 'gravityforms' ),
					'formId'                => 3,
					'inputType'             => '',
					'choices'               => '',
					'inputs'                => '',
					'conditionalLogic'      => '',
					'visibility'            => 'visible',
					'productField'          => '',
					'isRequired'            => false,
					'labelPlacement'        => '',
					'defaultValue'          => '',
					'fields'                => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'inputMaskIsCustom'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'autocompleteAttribute' => '',
					'allowsPrepopulate'     => false,
					'useRichTextEditor'     => false,
					'checkboxLabel'         => '',
					'layoutGroupId'         => 'c66e1637',
					'adminLabel'            => '',
					'maxLength'             => '',
					'errorMessage'          => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'enableAutocomplete'    => false,
				),
				array(
					'type'                  => 'name',
					'id'                    => 1,
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'label'                 => esc_html__( 'Your Name', 'gravityforms' ),
					'inputs'                => array(
						array(
							'id'                    => '1.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'       => esc_html__( 'Mr.', 'gravityforms' ),
									'value'      => 'Mr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Mrs.', 'gravityforms' ),
									'value'      => 'Mrs.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Miss', 'gravityforms' ),
									'value'      => 'Miss',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Ms.', 'gravityforms' ),
									'value'      => 'Ms.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Dr.', 'gravityforms' ),
									'value'      => 'Dr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Prof.', 'gravityforms' ),
									'value'      => 'Prof.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Rev.', 'gravityforms' ),
									'value'      => 'Rev.',
									'isSelected' => false,
									'price'      => '',
								),
							),
							'isHidden'              => true,
							'inputType'             => 'radio',
						),
						array(
							'id'                    => '1.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'placeholder'           => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '1.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '1.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'placeholder'           => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '1.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'formId'                => 3,
					'inputType'             => '',
					'displayOnly'           => '',
					'nameFormat'            => 'advanced',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'allowsPrepopulate'     => false,
					'useRichTextEditor'     => false,
					'defaultValue'          => '',
					'description'           => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'visibility'            => 'visible',
					'productField'          => '',
					'fields'                => '',
					'layoutGroupId'         => 'c1236779',
					'inputMaskIsCustom'     => false,
					'checkboxLabel'         => '',
					'adminLabel'            => '',
					'maxLength'             => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                    => 'address',
					'id'                      => 4,
					'size'                    => 'medium',
					'addressType'             => 'us',
					'label'                   => esc_html__( 'Your Address', 'gravityforms' ),
					'defaultCountry'          => 'United States',
					'defaultState'            => 'Virginia',
					'inputs'                  => array(
						array(
							'id'                    => '4.1',
							'label'                 => esc_html__( 'Street Address', 'gravityforms' ),
							'placeholder'           => '',
							'autocompleteAttribute' => 'address-line1',
						),
						array(
							'id'                    => '4.2',
							'label'                 => esc_html__( 'Address Line 2', 'gravityforms' ),
							'placeholder'           => '',
							'autocompleteAttribute' => 'address-line2',
						),
						array(
							'id'                    => '4.3',
							'label'                 => esc_html__( 'City', 'gravityforms' ),
							'placeholder'           => '',
							'autocompleteAttribute' => 'address-level2',
						),
						array(
							'id'                    => '4.4',
							'label'                 => esc_html__( 'State / Province', 'gravityforms' ),
							'isHidden'              => true,
							'placeholder'           => '',
							'autocompleteAttribute' => 'address-level1',
						),
						array(
							'id'                    => '4.5',
							'label'                 => esc_html__( 'Zip / Postal Code', 'gravityforms' ),
							'placeholder'           => '',
							'autocompleteAttribute' => 'postal-code',
						),
						array(
							'id'                    => '4.6',
							'label'                 => esc_html__( 'Country', 'gravityforms' ),
							'placeholder'           => '',
							'autocompleteAttribute' => 'country-name',
						),
					),
					'formId'                  => 3,
					'inputType'               => '',
					'displayOnly'             => '',
					'isRequired'              => false,
					'labelPlacement'          => '',
					'descriptionPlacement'    => '',
					'subLabelPlacement'       => '',
					'placeholder'             => '',
					'multipleFiles'           => false,
					'maxFiles'                => '',
					'calculationFormula'      => '',
					'calculationRounding'     => '',
					'enableCalculation'       => '',
					'disableQuantity'         => false,
					'defaultProvince'         => '',
					'displayAllCategories'    => false,
					'inputMask'               => false,
					'inputMaskValue'          => '',
					'allowsPrepopulate'       => false,
					'useRichTextEditor'       => false,
					'choices'                 => '',
					'conditionalLogic'        => '',
					'visibility'              => 'visible',
					'productField'            => '',
					'description'             => '',
					'hideState'               => '',
					'hideAddress2'            => '',
					'enableCopyValuesOption'  => '',
					'copyValuesOptionDefault' => '',
					'copyValuesOptionLabel'   => '',
					'fields'                  => '',
					'layoutGroupId'           => 'aaa2ed03',
					'adminLabel'              => '',
					'inputMaskIsCustom'       => '',
					'maxLength'               => '',
					'errorMessage'            => '',
					'cssClass'                => '',
					'inputName'               => '',
					'noDuplicates'            => false,
					'defaultValue'            => '',
					'enableAutocomplete'      => true,
					'autocompleteAttribute'   => '',
				),
				array(
					'type'                  => 'section',
					'id'                    => 10,
					'size'                  => 'medium',
					'displayOnly'           => '1',
					'description'           => esc_html__( 'We would love to chat with you. How can we get in touch?', 'gravityforms' ),
					'label'                 => esc_html__( 'How Can We Reach You?', 'gravityforms' ),
					'formId'                => 3,
					'inputType'             => '',
					'choices'               => '',
					'inputs'                => '',
					'conditionalLogic'      => '',
					'visibility'            => 'visible',
					'productField'          => '',
					'isRequired'            => false,
					'labelPlacement'        => '',
					'defaultValue'          => '',
					'fields'                => '',
					'adminLabel'            => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'errorMessage'          => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'layoutGroupId'         => '2d3b3f3c',
				),
				array(
					'type'                  => 'select',
					'id'                    => 11,
					'size'                  => 'medium',
					'enableChoiceValue'     => '1',
					'label'                 => esc_html__( 'Preferred Method of Contact', 'gravityforms' ),
					'choices'               => array(
						array(
							'text'  => esc_html__( 'Email', 'gravityforms' ),
							'value' => 'Email',
						),
						array(
							'text'  => esc_html__( 'Phone', 'gravityforms' ),
							'value' => 'Phone',
						),
					),
					'formId'                => 3,
					'inputType'             => '',
					'displayOnly'           => '',
					'isRequired'            => false,
					'inputs'                => '',
					'conditionalLogic'      => '',
					'visibility'            => 'visible',
					'productField'          => '',
					'labelPlacement'        => '',
					'description'           => '',
					'descriptionPlacement'  => '',
					'defaultValue'          => '',
					'enablePrice'           => '',
					'fields'                => '',
					'layoutGroupId'         => '34920583',
					'adminLabel'            => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'errorMessage'          => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'email',
					'id'                    => 2,
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => 'Oops. Please supply a valid email address',
					'label'                 => esc_html__( 'Your Email Address', 'gravityforms' ),
					'emailConfirmEnabled'   => '1',
					'formId'                => 3,
					'inputType'             => '',
					'displayOnly'           => '',
					'inputs'                => array(
						array(
							'id'                    => '2',
							'label'                 => esc_html__( 'Enter Email', 'gravityforms' ),
							'name'                  => '',
							'placeholder'           => '',
							'customLabel'           => 'Email Address',
							'autocompleteAttribute' => 'email',
						),
						array(
							'id'                    => '2.2',
							'label'                 => esc_html__( 'Confirm Email', 'gravityforms' ),
							'name'                  => '',
							'placeholder'           => '',
							'customLabel'           => 'Confirm Email Address',
							'autocompleteAttribute' => 'email',
						),
					),
					'choices'               => '',
					'conditionalLogic'      => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'allowsPrepopulate'     => false,
					'useRichTextEditor'     => false,
					'visibility'            => 'visible',
					'productField'          => '',
					'description'           => '',
					'fields'                => '',
					'layoutGroupId'         => '92a6673b',
					'adminLabel'            => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
					'checkboxLabel'         => '',
				),
				array(
					'type'                  => 'phone',
					'id'                    => 5,
					'isRequired'            => true,
					'size'                  => 'medium',
					'phoneFormat'           => 'standard',
					'errorMessage'          => 'Please enter your phone number.',
					'label'                 => esc_html__( 'Your Phone', 'gravityforms' ),
					'conditionalLogic'      => array(
						'actionType' => 'show',
						'logicType'  => 'all',
						'rules'      => array(
							array(
								'fieldId'  => '11',
								'operator' => 'is',
								'value'    => 'Phone',
							),
						),
					),
					'formId'                => 3,
					'inputType'             => '',
					'displayOnly'           => '',
					'inputs'                => '',
					'choices'               => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'allowsPrepopulate'     => false,
					'useRichTextEditor'     => false,
					'visibility'            => 'visible',
					'productField'          => '',
					'description'           => '',
					'defaultValue'          => '',
					'fields'                => '',
					'layoutGroupId'         => 'a5b7fe20',
					'adminLabel'            => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => 'tel',
				),
				array(
					'type'                  => 'select',
					'id'                    => 12,
					'isRequired'            => true,
					'size'                  => 'medium',
					'enableChoiceValue'     => '1',
					'label'                 => esc_html__( 'Best Time to Call You', 'gravityforms' ),
					'choices'               => array(
						array(
							'isSelected' => true,
							'text'       => esc_html__( 'Select A Time', 'gravityforms' ),
							'value'      => '',
						),
						array(
							'text'  => esc_html__( '12:00 am', 'gravityforms' ),
							'value' => '12:00 am',
						),
						array(
							'text'  => esc_html__( '12:30 am', 'gravityforms' ),
							'value' => '12:30 am',
						),
						array(
							'text'  => esc_html__( '1:00 am', 'gravityforms' ),
							'value' => '1:00 am',
						),
						array(
							'text'  => esc_html__( '1:30 am', 'gravityforms' ),
							'value' => '1:30 am',
						),
						array(
							'text'  => esc_html__( '2:00 am', 'gravityforms' ),
							'value' => '2:00 am',
						),
						array(
							'text'  => esc_html__( '2:30 am', 'gravityforms' ),
							'value' => '2:30 am',
						),
						array(
							'text'  => esc_html__( '3:00 am', 'gravityforms' ),
							'value' => '3:00 am',
						),
						array(
							'text'  => esc_html__( '3:30 am', 'gravityforms' ),
							'value' => '3:30 am',
						),
						array(
							'text'  => esc_html__( '4:00 am', 'gravityforms' ),
							'value' => '4:00 am',
						),
						array(
							'text'  => esc_html__( '4:30 am', 'gravityforms' ),
							'value' => '4:30 am',
						),
						array(
							'text'  => esc_html__( '5:00 am', 'gravityforms' ),
							'value' => '5:00 am',
						),
						array(
							'text'  => esc_html__( '5:30 am', 'gravityforms' ),
							'value' => '5:30 am',
						),
						array(
							'text'  => esc_html__( '6:00 am', 'gravityforms' ),
							'value' => '6:00 am',
						),
						array(
							'text'  => esc_html__( '6:30 am', 'gravityforms' ),
							'value' => '6:30 am',
						),
						array(
							'text'  => esc_html__( '7:00 am', 'gravityforms' ),
							'value' => '7:00 am',
						),
						array(
							'text'  => esc_html__( '7:30 am', 'gravityforms' ),
							'value' => '7:30 am',
						),
						array(
							'text'  => esc_html__( '8:00 am', 'gravityforms' ),
							'value' => '8:00 am',
						),
						array(
							'text'  => esc_html__( '8:30 am', 'gravityforms' ),
							'value' => '8:30 am',
						),
						array(
							'text'  => esc_html__( '9:00 am', 'gravityforms' ),
							'value' => '9:00 am',
						),
						array(
							'text'  => esc_html__( '9:30 am', 'gravityforms' ),
							'value' => '9:30 am',
						),
						array(
							'text'  => esc_html__( '10:00 am', 'gravityforms' ),
							'value' => '10:00 am',
						),
						array(
							'text'  => esc_html__( '10:30 am', 'gravityforms' ),
							'value' => '10:30 am',
						),
						array(
							'text'  => esc_html__( '11:00 am', 'gravityforms' ),
							'value' => '11:00 am',
						),
						array(
							'text'  => esc_html__( '11:30 am', 'gravityforms' ),
							'value' => '11:30 am',
						),
						array(
							'text'  => esc_html__( '12:00 pm', 'gravityforms' ),
							'value' => '12:00 pm',
						),
						array(
							'text'  => esc_html__( '12:30 pm', 'gravityforms' ),
							'value' => '12:30 pm',
						),
						array(
							'text'  => esc_html__( '1:00 pm', 'gravityforms' ),
							'value' => '1:00 pm',
						),
						array(
							'text'  => esc_html__( '1:30 pm', 'gravityforms' ),
							'value' => '1:30 pm',
						),
						array(
							'text'  => esc_html__( '2:00 pm', 'gravityforms' ),
							'value' => '2:00 pm',
						),
						array(
							'text'  => esc_html__( '2:30 pm', 'gravityforms' ),
							'value' => '2:30 pm',
						),
						array(
							'text'  => esc_html__( '3:00 pm', 'gravityforms' ),
							'value' => '3:00 pm',
						),
						array(
							'text'  => esc_html__( '3:30 pm', 'gravityforms' ),
							'value' => '3:30 pm',
						),
						array(
							'text'  => esc_html__( '4:00 pm', 'gravityforms' ),
							'value' => '4:00 pm',
						),
						array(
							'text'  => esc_html__( '4:30 pm', 'gravityforms' ),
							'value' => '4:30 pm',
						),
						array(
							'text'  => esc_html__( '5:00 pm', 'gravityforms' ),
							'value' => '5:00 pm',
						),
						array(
							'text'  => esc_html__( '5:30 pm', 'gravityforms' ),
							'value' => '5:30 pm',
						),
						array(
							'text'  => esc_html__( '6:00 pm', 'gravityforms' ),
							'value' => '6:00 pm',
						),
						array(
							'text'  => esc_html__( '6:30 pm', 'gravityforms' ),
							'value' => '6:30 pm',
						),
						array(
							'text'  => esc_html__( '7:00 pm', 'gravityforms' ),
							'value' => '7:00 pm',
						),
						array(
							'text'  => esc_html__( '7:30 pm', 'gravityforms' ),
							'value' => '7:30 pm',
						),
						array(
							'text'  => esc_html__( '8:00 pm', 'gravityforms' ),
							'value' => '8:00 pm',
						),
						array(
							'text'  => esc_html__( '8:30 pm', 'gravityforms' ),
							'value' => '8:30 pm',
						),
						array(
							'text'  => esc_html__( '9:00 pm', 'gravityforms' ),
							'value' => '9:00 pm',
						),
						array(
							'text'  => esc_html__( '9:30 pm', 'gravityforms' ),
							'value' => '9:30 pm',
						),
						array(
							'text'  => esc_html__( '10:00 pm', 'gravityforms' ),
							'value' => '10:00 pm',
						),
						array(
							'text'  => esc_html__( '10:30 pm', 'gravityforms' ),
							'value' => '10:30 pm',
						),
						array(
							'text'  => esc_html__( '11:00 pm', 'gravityforms' ),
							'value' => '11:00 pm',
						),
						array(
							'text'  => esc_html__( '11:30 pm', 'gravityforms' ),
							'value' => '11:30 pm',
						),
					),
					'conditionalLogic'      => array(
						'actionType' => 'show',
						'logicType'  => 'all',
						'rules'      => array(
							array(
								'fieldId'  => '11',
								'operator' => 'is',
								'value'    => 'Phone',
							),
						),
					),
					'formId'                => 3,
					'inputType'             => '',
					'displayOnly'           => '',
					'inputs'                => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'allowsPrepopulate'     => false,
					'useRichTextEditor'     => false,
					'visibility'            => 'visible',
					'productField'          => '',
					'description'           => '',
					'defaultValue'          => '',
					'enablePrice'           => '',
					'fields'                => '',
					'layoutGroupId'         => '5d733fd7',
					'adminLabel'            => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'errorMessage'          => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'section',
					'id'                    => 8,
					'size'                  => 'medium',
					'displayOnly'           => '1',
					'description'           => esc_html__( 'Please let us know what\'s on your mind. Have a question for us? Ask away.', 'gravityforms' ),
					'label'                 => esc_html__( 'What\'s on your mind?', 'gravityforms' ),
					'formId'                => 3,
					'inputType'             => '',
					'choices'               => '',
					'inputs'                => '',
					'conditionalLogic'      => '',
					'visibility'            => 'visible',
					'productField'          => '',
					'isRequired'            => false,
					'labelPlacement'        => '',
					'defaultValue'          => '',
					'fields'                => '',
					'layoutGroupId'         => '73cb7ce0',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'inputMaskIsCustom'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'autocompleteAttribute' => '',
					'allowsPrepopulate'     => false,
					'useRichTextEditor'     => false,
					'adminLabel'            => '',
					'maxLength'             => '',
					'errorMessage'          => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'enableAutocomplete'    => false,
				),
				array(
					'type'                  => 'textarea',
					'id'                    => 3,
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => 'Please enter your message.',
					'label'                 => esc_html__( 'Your Comments/Questions', 'gravityforms' ),
					'formId'                => 3,
					'inputType'             => '',
					'displayOnly'           => '',
					'inputs'                => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'allowsPrepopulate'     => false,
					'useRichTextEditor'     => false,
					'visibility'            => 'visible',
					'productField'          => '',
					'description'           => '',
					'defaultValue'          => '',
					'form_id'               => '',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'layoutGroupId'         => '20d48602',
					'adminLabel'            => '',
					'maxLength'             => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
			),
			'descriptionPlacement'       => 'below',
			'gpollDisplayResults'        => '1',
			'gpollShowResultsLink'       => '1',
			'gpollShowPercentages'       => '1',
			'gpollShowCounts'            => '1',
			'gpollStyle'                 => 'green',
			'gpollCookie'                => '1 month',
			'id'                         => 3,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => null,
			'pagination'                 => null,
			'firstPageCssClass'          => null,
			'is_active'                  => '1',
			'date_created'               => '2016-07-12 20:22:20',
			'is_trash'                   => '0',
			'nextFieldId'                => 16,
			'feeds'                      => array(
				'gravityformsadvancedpostcreation' => array(),
			),
			'version'                    => '2.7',
			'subLabelPlacement'          => 'above',
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'customRequiredIndicator'    => '',
			'cssClass'                   => '',
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'markupVersion'              => 2,
			'confirmations'              => array(
				'51794abf1ee7a' => array(
					'type'              => 'message',
					'id'                => '51794abf1ee7a',
					'isDefault'         => true,
					'url'               => '',
					'queryString'       => '',
					'name'              => 'Default Confirmation',
					'message'           => esc_html__( 'Thank you for contacting us! We will get in touch with you shortly.', 'gravityforms' ),
					'disableAutoformat' => false,
					'pageId'            => 0,
					'conditionalLogic'  => array(),
				),
			),
			'notifications'              => array(
				'51794abf1f0d1' => array(
					'id'      => '51794abf1f0d1',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'We have received your inquiry', 'gravityforms' ),
					'message' => wp_kses_post( __( '<p>Hi there {Name (First):1.3},</p><p>Thank you for getting in touch. We have received your inquiry and will get back to you within one business day.</p>','gravityforms' ) ),
					'toType'  => 'field',
					'toField' => '2',
					'to' => '2',
					'event'   => 'form_submission',
					'name'    => 'User Notification',
					'type'    => 'user',
				),
				'51794abf1f0d2' => array(
					'id'      => '51794abf1f0d2',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'New submission from {form_title}', 'gravityforms' ),
					'message' => '{all_fields}',
					'toType'  => 'email',
					'to' => '{admin_email}',
					'event'   => 'form_submission',
					'name'    => 'Admin Notification',
				),
			),
		),
		'version'               => '2.7',
	),
	'contest'              => array(
		'id'                    => 'contest',
		'description'           => esc_html__( 'A form that allows your users to enter a contest', 'gravityforms' ),
		'title'                 => esc_html__( 'Contest Entry Form', 'gravityforms' ),
		'template_background'   => 'panache-green',
		'template_thumbnail'    => 'Contest.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/contest-entry-form/',
		'template_access_level' => [ 'godaddy', 'gravityflow', 'gravityview', 'single', 'ltsingle', 'basic', 'multi', 'ltmulti', 'pro', 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'title'                      => esc_html__( 'Form Template Library: Contest Entry Form', 'gravityforms' ),
			'description'                => esc_html__( 'Enter our competition today to be in with a chance of winning...', 'gravityforms' ),
			'labelPlacement'             => 'top_label',
			'descriptionPlacement'       => 'below',
			'button'                     => array(
				'type'     => 'text',
				'text'     => esc_html__( 'Enter!', 'gravityforms' ),
				'imageUrl' => '',
			),
			'fields'                     => array(
				array(
					'type'                  => 'name',
					'id'                    => 1,
					'label'                 => esc_html__( 'Name', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'large',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'nameFormat'            => 'advanced',
					'inputs'                => array(
						array(
							'id'                    => '1.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'  => esc_html__( 'Dr.', 'gravityforms' ),
									'value' => 'Dr.',
								),
								array(
									'text'  => esc_html__( 'Miss', 'gravityforms' ),
									'value' => 'Miss',
								),
								array(
									'text'  => esc_html__( 'Mr.', 'gravityforms' ),
									'value' => 'Mr.',
								),
								array(
									'text'  => esc_html__( 'Mrs.', 'gravityforms' ),
									'value' => 'Mrs.',
								),
								array(
									'text'  => esc_html__( 'Ms.', 'gravityforms' ),
									'value' => 'Ms.',
								),
								array(
									'text'  => esc_html__( 'Prof.', 'gravityforms' ),
									'value' => 'Prof.',
								),
								array(
									'text'  => esc_html__( 'Rev.', 'gravityforms' ),
									'value' => 'Rev.',
								),
							),
							'isHidden'              => true,
							'inputType'             => 'radio',
						),
						array(
							'id'                    => '1.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '1.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '1.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '1.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'formId'                => 124,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'layoutGridColumnSpan'  => '',
					'enableEnhancedUI'      => 0,
					'layoutGroupId'         => 'c6d2ac55',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
				),
				array(
					'type'                  => 'email',
					'id'                    => 2,
					'label'                 => esc_html__( 'Email', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'large',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => array(
						array(
							'id'                    => '2',
							'label'                 => esc_html__( 'Enter Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
						array(
							'id'                    => '2.2',
							'label'                 => esc_html__( 'Confirm Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
					),
					'autocompleteAttribute' => 'email',
					'formId'                => 124,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'enableAutocomplete'    => true,
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'layoutGridColumnSpan'  => 12,
					'emailConfirmEnabled'   => true,
					'enableEnhancedUI'      => 0,
					'layoutGroupId'         => 'df5bddbc',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
				),
				array(
					'type'                 => 'radio',
					'id'                   => 7,
					'label'                => esc_html__( 'The answer is...', 'gravityforms' ),
					'adminLabel'           => '',
					'isRequired'           => false,
					'size'                 => 'medium',
					'errorMessage'         => '',
					'visibility'           => 'visible',
					'inputs'               => null,
					'choices'              => array(
						array(
							'text'       => esc_html__( 'Answer A', 'gravityforms' ),
							'value'      => 'Answer A',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Answer B', 'gravityforms' ),
							'value'      => 'Answer B',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Answer C', 'gravityforms' ),
							'value'      => 'Answer C',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'formId'               => 124,
					'description'          => '',
					'allowsPrepopulate'    => false,
					'inputMask'            => false,
					'inputMaskValue'       => '',
					'inputMaskIsCustom'    => false,
					'maxLength'            => '',
					'inputType'            => '',
					'labelPlacement'       => '',
					'descriptionPlacement' => '',
					'subLabelPlacement'    => '',
					'placeholder'          => '',
					'cssClass'             => '',
					'inputName'            => '',
					'noDuplicates'         => false,
					'defaultValue'         => '',
					'conditionalLogic'     => '',
					'productField'         => '',
					'enableOtherChoice'    => '',
					'enablePrice'          => '',
					'multipleFiles'        => false,
					'maxFiles'             => '',
					'calculationFormula'   => '',
					'calculationRounding'  => '',
					'enableCalculation'    => '',
					'disableQuantity'      => false,
					'displayAllCategories' => false,
					'useRichTextEditor'    => false,
					'fields'               => '',
					'displayOnly'          => '',
				),
				array(
					'type'                     => 'consent',
					'checked_indicator_url'    => 'https://www.gravityforms.com/wp-content/plugins/gravityforms/images/tick.png',
					'checked_indicator_markup' => '<img src="https://www.gravityforms.com/wp-content/plugins/gravityforms/images/tick.png" />',
					'id'                       => 5,
					'label'                    => esc_html__( 'Competition Terms and Conditions', 'gravityforms' ),
					'adminLabel'               => '',
					'isRequired'               => true,
					'size'                     => 'large',
					'errorMessage'             => '',
					'visibility'               => 'visible',
					'inputs'                   => array(
						array(
							'id'    => '5.1',
							'label' => esc_html__( 'Consent', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'       => '5.2',
							'label'    => esc_html__( 'Text', 'gravityforms' ),
							'name'     => '',
							'isHidden' => true,
						),
						array(
							'id'       => '5.3',
							'label'    => esc_html__( 'Description', 'gravityforms' ),
							'name'     => '',
							'isHidden' => true,
						),
					),
					'checkboxLabel'            => wp_kses_post( __( '<strong>I agree to the competition terms and conditions.</strong>', 'gravityforms' ) ),
					'descriptionplaceholder'   => '',
					'choices'                  => array(
						array(
							'text'       => esc_html__( 'Checked', 'gravityforms' ),
							'value'      => '1',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'formId'                   => 124,
					'description'              => esc_html__( 'Terms and conditions placeholder.', 'gravityforms' ),
					'allowsPrepopulate'        => false,
					'inputMask'                => false,
					'inputMaskValue'           => '',
					'inputMaskIsCustom'        => false,
					'maxLength'                => '',
					'labelPlacement'           => '',
					'descriptionPlacement'     => 'above',
					'subLabelPlacement'        => '',
					'placeholder'              => '',
					'cssClass'                 => '',
					'inputName'                => '',
					'noDuplicates'             => false,
					'defaultValue'             => '',
					'enableAutocomplete'       => false,
					'autocompleteAttribute'    => '',
					'conditionalLogic'         => '',
					'productField'             => '',
					'layoutGridColumnSpan'     => 12,
					'enableEnhancedUI'         => 0,
					'layoutGroupId'            => '27e48dc9',
					'multipleFiles'            => false,
					'maxFiles'                 => '',
					'calculationFormula'       => '',
					'calculationRounding'      => '',
					'enableCalculation'        => '',
					'disableQuantity'          => false,
					'displayAllCategories'     => false,
					'useRichTextEditor'        => false,
					'fields'                   => '',
					'displayOnly'              => '',
					'inputType'                => '',
				),
			),
			'version'                    => '2.7',
			'id'                         => 124,
			'markupVersion'              => 2,
			'validationSummary'          => '1',
			'nextFieldId'                => 8,
			'useCurrentUserAsAuthor'     => true,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => null,
			'pagination'                 => null,
			'firstPageCssClass'          => null,
			'form_slug'                  => 'contest-entry-form',
			'subLabelPlacement'          => 'above',
			'cssClass'                   => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'delete_entry'               => '',
			'delete_entry_condition'     => '',
			'delete_entry_period'        => '',
			'delete_entry_units'         => 'hour',
			'notifications'              => array(
				'51794abf1f0d1' => array(
					'id'      => '51794abf1f0d1',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'You have successfully entered our competition', 'gravityforms' ),
					'message' => wp_kses_post( __( '<p>Hi there {Name (First):1.3},</p><p>Thank you for getting in touch and entering our competition. Keep an eye on your inbox as winners will be contacted via email.</p><p>Good Luck!</p>','gravityforms' ) ),
					'toType'  => 'field',
					'toField' => '2',
					'to' => '2',
					'event'   => 'form_submission',
					'name'    => 'User Notification',
					'type'    => 'user',
				),
				'51794abf1f0d2' => array(
					'id'      => '51794abf1f0d2',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'New submission from {form_title}', 'gravityforms' ),
					'message' => '{all_fields}',
					'toType'  => 'email',
					'to' => '{admin_email}',
					'event'   => 'form_submission',
					'name'    => 'Admin Notification',
				),
			),
			'confirmations'              => array(
				'6069e74961a04' => array(
					'id'                => '6069e74961a04',
					'name'              => 'Default Confirmation',
					'isDefault'         => true,
					'type'              => 'message',
					'message'           => esc_html__( 'Thank you for entering our competition! The winners will be contacted via email.', 'gravityforms' ),
					'url'               => '',
					'pageId'            => 0,
					'queryString'       => '',
					'disableAutoformat' => false,
					'conditionalLogic'  => array(),
				),
			),
		),
		'version'               => '2.7',
	),
	'donation'             => array(
		'id'                    => 'donation',
		'description'           => esc_html__( 'A donation form for multiple purposes', 'gravityforms' ),
		'title'                 => esc_html__( 'Donation Form', 'gravityforms' ),
		'template_background'   => 'hawkes-blue',
		'template_thumbnail'    => 'Donation.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/donation-form/',
		'template_access_level' => [ 'pro', 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'title'                      => esc_html__( 'Donation Form', 'gravityforms' ),
			'description'                => esc_html__( 'Help us provide care and support for vulnerable adults.', 'gravityforms' ),
			'labelPlacement'             => 'top_label',
			'descriptionPlacement'       => 'below',
			'button'                     => array(
				'type'     => 'text',
				'text'     => esc_html__( 'Submit', 'gravityforms' ),
				'imageUrl' => '',
			),
			'fields'                     => array(
				array(
					'type'                  => 'name',
					'id'                    => 1,
					'label'                 => esc_html__( 'Name', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'nameFormat'            => 'advanced',
					'inputs'                => array(
						array(
							'id'                    => '1.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'       => esc_html__( 'Mr.', 'gravityforms' ),
									'value'      => 'Mr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Mrs.', 'gravityforms' ),
									'value'      => 'Mrs.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Miss', 'gravityforms' ),
									'value'      => 'Miss',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Ms.', 'gravityforms' ),
									'value'      => 'Ms.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Dr.', 'gravityforms' ),
									'value'      => 'Dr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Prof.', 'gravityforms' ),
									'value'      => 'Prof.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Rev.', 'gravityforms' ),
									'value'      => 'Rev.',
									'isSelected' => false,
									'price'      => '',
								),
							),
							'isHidden'  => true,
							'inputType' => 'radio',
						),
						array(
							'id'                    => '1.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '1.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '1.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '1.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'formId'                => 8,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
					'layoutGroupId'         => 'b3e88f88',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'email',
					'id'                    => 2,
					'label'                 => esc_html__( 'Email', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => array(
						array(
							'id'    => '2',
							'label' => esc_html__( 'Enter Email', 'gravityforms' ),
							'name'  => '',
							'autocompleteAttribute' => 'email',
						),
						array(
							'id'                    => '2.2',
							'label'                 => esc_html__( 'Confirm Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
					),
					'formId'                => 8,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'emailConfirmEnabled'   => true,
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
					'layoutGroupId'         => '98e31b7c',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => 'email',
				),
				array(
					'type'                  => 'product',
					'id'                    => 3,
					'label'                 => esc_html__( 'Donation Amount', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'inputType'             => 'radio',
					'enablePrice'           => true,
					'formId'                => 8,
					'description'           => esc_html__( 'Choose how much you would like to donate.', 'gravityforms' ),
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'enableChoiceValue'     => true,
					'choices'               => array(
						array(
							'text'       => esc_html__( '10 USD', 'gravityforms' ),
							'value'      => '10',
							'isSelected' => false,
							'price'      => '$10.00',
						),
						array(
							'text'       => esc_html__( '50 USD', 'gravityforms' ),
							'value'      => '50',
							'isSelected' => false,
							'price'      => '$50.00',
						),
						array(
							'text'       => esc_html__( '250 USD', 'gravityforms' ),
							'value'      => '250',
							'isSelected' => false,
							'price'      => '$250.00',
						),
						array(
							'text'       => esc_html__( 'Other amount', 'gravityforms' ),
							'value'      => 'Other amount',
							'isSelected' => false,
							'price'      => '$0.00',
						),
					),
					'conditionalLogic'      => '',
					'productField'          => '',
					'basePrice'             => '$0.00',
					'disableQuantity'       => false,
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'checkboxLabel'         => '',
					'fields'                => '',
					'displayOnly'           => '',
					'layoutGroupId'         => '219e0578',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'product',
					'id'                    => 4,
					'label'                 => esc_html__( 'Other Amount', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'inputType'             => 'price',
					'enablePrice'           => null,
					'formId'                => 8,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => null,
					'conditionalLogic'      => array(
						'actionType' => 'show',
						'logicType'  => 'all',
						'rules'      => array(
							array(
								'fieldId'  => '3',
								'operator' => 'is',
								'value'    => 'Other amount',
							),
						),
					),
					'productField'          => '',
					'basePrice'             => '$0.00',
					'disableQuantity'       => false,
					'fields'                => '',
					'displayOnly'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'checkboxLabel'         => '',
					'layoutGroupId'         => 'c34c65cd',
					'autocompleteAttribute' => '',
					'enableAutocomplete'    => false,
				),
				array(
					'type'                  => 'total',
					'id'                    => 5,
					'label'                 => esc_html__( 'Total', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'formId'                => 8,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'displayOnly'           => '',
					'layoutGroupId'         => '2a92be03',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'autocompleteAttribute' => '',
					'useRichTextEditor'     => false,
					'layoutGridColumnSpan'  => 12,
					'enableAutocomplete'    => false,
				),
			),
			'version'                    => '2.7',
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'id'                         => 8,
			'nextFieldId'                => 9,
			'useCurrentUserAsAuthor'     => true,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => null,
			'pagination'                 => null,
			'firstPageCssClass'          => null,
			'subLabelPlacement'          => 'above',
			'cssClass'                   => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'markupVersion'              => 2,
			'confirmations'              => array(
				'5f033f18375c7' => array(
					'id'          => '5f033f18375c7',
					'name'        => 'Default Confirmation',
					'isDefault'   => true,
					'type'        => 'message',
					'message'     => esc_html__( 'Thank you for your contribution! We appreciate your support.', 'gravityforms' ),
					'url'         => '',
					'pageId'      => '',
					'queryString' => '',
				),
			),
			'notifications'              => array(
				'51794abf1f0d2' => array(
					'id'      => '51794abf1f0d2',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'You have received a new donation.', 'gravityforms' ),
					'message' => '{all_fields}',
					'toType'  => 'email',
					'to' => '{admin_email}',
					'event'   => 'form_submission',
					'name'    => 'Admin Notification',
				),
			),
		),
		'version'               => '2.7',
	),
	'ecommerce'            => array(
		'id'                    => 'ecommerce',
		'description'           => esc_html__( 'A form that allows you to sell products and let your customers pay via different payment gateways', 'gravityforms' ),
		'title'                 => esc_html__( 'eCommerce Form', 'gravityforms' ),
		'template_background'   => 'iceberg-blue',
		'template_thumbnail'    => 'eCommerce.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/ecommerce-form/',
		'template_access_level' => [ 'pro', 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'title'                      => esc_html__( 'eCommerce Form', 'gravityforms' ),
			'description'                => esc_html__( 'A form that allows you to sell products and let your customers pay via different payment gateways', 'gravityforms' ),
			'labelPlacement'             => 'top_label',
			'descriptionPlacement'       => 'above',
			'button'                     => array(
				'type'             => 'text',
				'text'             => esc_html__( 'Submit', 'gravityforms' ),
				'imageUrl'         => '',
				'conditionalLogic' => null,
			),
			'fields'                     => array(
				array(
					'type'                  => 'product',
					'id'                    => 1,
					'label'                 => esc_html__( 'My Super Awesome Product', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => array(
						array(
							'id'    => '1.1',
							'label' => esc_html__( 'Name', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '1.2',
							'label' => esc_html__( 'Price', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '1.3',
							'label' => esc_html__( 'Quantity', 'gravityforms' ),
							'name'  => '',
						),
					),
					'inputType'             => 'singleproduct',
					'enablePrice'           => null,
					'formId'                => 5,
					'description'           => esc_html__( 'This is my super awesome product. It\'s the best, so everyone should buy it!', 'gravityforms' ),
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => null,
					'conditionalLogic'      => '',
					'productField'          => '',
					'basePrice'             => '$10.00',
					'disableQuantity'       => false,
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '31aea450',
				),
				array(
					'type'                  => 'product',
					'id'                    => 11,
					'label'                 => esc_html__( 'Another Amazing Product', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => array(
						array(
							'id'    => '11.1',
							'label' => esc_html__( 'Name', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '11.2',
							'label' => esc_html__( 'Price', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '11.3',
							'label' => esc_html__( 'Quantity', 'gravityforms' ),
							'name'  => '',
						),
					),
					'inputType'             => 'singleproduct',
					'enablePrice'           => null,
					'formId'                => 5,
					'description'           => esc_html__( 'If you loved the first product, you\'re really going to love this one. Don\'t miss out, order yours while they\'re still in stock.', 'gravityforms' ),
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => null,
					'conditionalLogic'      => '',
					'productField'          => '',
					'basePrice'             => '$15.00',
					'disableQuantity'       => false,
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '03b4852e',
				),
				array(
					'type'                  => 'total',
					'id'                    => 13,
					'label'                 => esc_html__( 'Subtotal', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'formId'                => 5,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '9a35c595',
				),
				array(
					'type'                  => 'page',
					'id'                    => 10,
					'label'                 => '',
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'displayOnly'           => true,
					'nextButton'            => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Next', 'gravityforms' ),
						'imageUrl' => '',
					),
					'previousButton'        => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Previous', 'gravityforms' ),
						'imageUrl' => '',
					),
					'formId'                => 5,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '94bf6611',
				),
				array(
					'type'                  => 'name',
					'id'                    => 7,
					'label'                 => esc_html__( 'Name', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'nameFormat'            => 'advanced',
					'inputs'                => array(
						array(
							'id'                    => '7.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'       => esc_html__( 'Mr.', 'gravityforms' ),
									'value'      => 'Mr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Mrs.', 'gravityforms' ),
									'value'      => 'Mrs.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Miss', 'gravityforms' ),
									'value'      => 'Miss',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Ms.', 'gravityforms' ),
									'value'      => 'Ms.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Dr.', 'gravityforms' ),
									'value'      => 'Dr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Prof.', 'gravityforms' ),
									'value'      => 'Prof.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Rev.', 'gravityforms' ),
									'value'      => 'Rev.',
									'isSelected' => false,
									'price'      => '',
								),
							),
							'isHidden'  => true,
							'inputType' => 'radio',
						),
						array(
							'id'                    => '7.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '7.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '7.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '7.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'formId'                => 5,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '2a2cb23e',
				),
				array(
					'type'                  => 'email',
					'id'                    => 9,
					'label'                 => esc_html__( 'Email', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'formId'                => 5,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'emailConfirmEnabled'   => '',
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => 'email',
					'layoutGroupId'         => '6884a6d5',
				),
				array(
					'type'                  => 'page',
					'id'                    => 12,
					'label'                 => '',
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'displayOnly'           => true,
					'nextButton'            => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Next', 'gravityforms' ),
						'imageUrl' => '',
					),
					'previousButton'        => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Previous', 'gravityforms' ),
						'imageUrl' => '',
					),
					'formId'                => 5,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '8d96f708',
				),
				array(
					'type'                    => 'address',
					'id'                      => 14,
					'label'                   => esc_html__( 'Billing Address', 'gravityforms' ),
					'adminLabel'              => '',
					'isRequired'              => false,
					'size'                    => 'medium',
					'errorMessage'            => '',
					'addressType'             => 'international',
					'inputs'                  => array(
						array(
							'id'                    => '14.1',
							'label'                 => esc_html__( 'Street Address', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line1',
						),
						array(
							'id'                    => '14.2',
							'label'                 => esc_html__( 'Address Line 2', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line2',
						),
						array(
							'id'                    => '14.3',
							'label'                 => esc_html__( 'City', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level2',
						),
						array(
							'id'                    => '14.4',
							'label'                 => esc_html__( 'State / Province', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level1',
						),
						array(
							'id'                    => '14.5',
							'label'                 => esc_html__( 'ZIP / Postal Code', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'postal-code',
						),
						array(
							'id'                    => '14.6',
							'label'                 => esc_html__( 'Country', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'country-name',
						),
					),
					'formId'                  => 5,
					'description'             => '',
					'allowsPrepopulate'       => false,
					'inputMask'               => false,
					'inputMaskValue'          => '',
					'inputType'               => '',
					'labelPlacement'          => '',
					'descriptionPlacement'    => '',
					'subLabelPlacement'       => '',
					'placeholder'             => '',
					'cssClass'                => '',
					'inputName'               => '',
					'visibility'              => 'visible',
					'noDuplicates'            => false,
					'defaultValue'            => '',
					'choices'                 => '',
					'conditionalLogic'        => '',
					'defaultCountry'          => '',
					'defaultProvince'         => '',
					'productField'            => '',
					'defaultState'            => '',
					'enableCopyValuesOption'  => '',
					'copyValuesOptionDefault' => '',
					'copyValuesOptionLabel'   => '',
					'multipleFiles'           => false,
					'maxFiles'                => '',
					'calculationFormula'      => '',
					'calculationRounding'     => '',
					'enableCalculation'       => '',
					'disableQuantity'         => false,
					'displayAllCategories'    => false,
					'useRichTextEditor'       => false,
					'displayOnly'             => '',
					'fields'                  => '',
					'inputMaskIsCustom'       => '',
					'maxLength'               => '',
					'enableAutocomplete'      => true,
					'autocompleteAttribute'   => '',
					'layoutGroupId'           => 'f411d92c',
				),
				array(
					'type'                    => 'address',
					'id'                      => 8,
					'label'                   => esc_html__( 'Shipping Address', 'gravityforms' ),
					'adminLabel'              => '',
					'isRequired'              => false,
					'size'                    => 'medium',
					'errorMessage'            => '',
					'addressType'             => 'international',
					'inputs'                  => array(
						array(
							'id'                    => '8.1',
							'label'                 => esc_html__( 'Street Address', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line1',
						),
						array(
							'id'                    => '8.2',
							'label'                 => esc_html__( 'Address Line 2', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line2',
						),
						array(
							'id'                    => '8.3',
							'label'                 => esc_html__( 'City', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level2',
						),
						array(
							'id'                    => '8.4',
							'label'                 => esc_html__( 'State / Province', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level1',
						),
						array(
							'id'                    => '8.5',
							'label'                 => esc_html__( 'ZIP / Postal Code', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'postal-code',
						),
						array(
							'id'                    => '8.6',
							'label'                 => esc_html__( 'Country', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'country-name',
						),
					),
					'formId'                  => 5,
					'description'             => '',
					'allowsPrepopulate'       => false,
					'inputMask'               => false,
					'inputMaskValue'          => '',
					'inputType'               => '',
					'labelPlacement'          => '',
					'descriptionPlacement'    => '',
					'subLabelPlacement'       => '',
					'placeholder'             => '',
					'cssClass'                => '',
					'inputName'               => '',
					'visibility'              => 'visible',
					'noDuplicates'            => false,
					'defaultValue'            => '',
					'choices'                 => '',
					'conditionalLogic'        => '',
					'defaultCountry'          => '',
					'defaultProvince'         => '',
					'productField'            => '',
					'defaultState'            => '',
					'enableCopyValuesOption'  => 1,
					'copyValuesOptionDefault' => 1,
					'copyValuesOptionLabel'   => 'Same as billing address',
					'displayOnly'             => '',
					'multipleFiles'           => false,
					'maxFiles'                => '',
					'calculationFormula'      => '',
					'calculationRounding'     => '',
					'enableCalculation'       => '',
					'disableQuantity'         => false,
					'displayAllCategories'    => false,
					'useRichTextEditor'       => false,
					'copyValuesOptionField'   => '14',
					'fields'                  => '',
					'inputMaskIsCustom'       => '',
					'maxLength'               => '',
					'enableAutocomplete'      => true,
					'autocompleteAttribute'   => '',
					'layoutGroupId'           => '141adef5',
				),
				array(
					'type'                  => 'shipping',
					'id'                    => 5,
					'label'                 => esc_html__( 'Shipping', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'inputType'             => 'radio',
					'enablePrice'           => true,
					'formId'                => 5,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => array(
						array(
							'text'       => esc_html__( 'Standard Shipping', 'gravityforms' ),
							'value'      => 'Standard Shipping',
							'isSelected' => false,
							'price'      => '$5.00',
						),
						array(
							'text'       => esc_html__( 'Express Shipping', 'gravityforms' ),
							'value'      => 'Express Shipping',
							'isSelected' => false,
							'price'      => '$10.00',
						),
						array(
							'text'       => esc_html__( 'Overnight Shipping', 'gravityforms' ),
							'value'      => 'Overnight Shipping',
							'isSelected' => false,
							'price'      => '$20.00',
						),
					),
					'conditionalLogic'      => false,
					'basePrice'             => '$0.00',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'enableChoiceValue'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => 'be3a214b',
				),
				array(
					'type'                  => 'total',
					'id'                    => 3,
					'label'                 => esc_html__( 'Total', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'formId'                => 5,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '04984a61',
				),
				array(
					'type'                  => 'radio',
					'id'                    => 6,
					'label'                 => esc_html__( 'Payment Method', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'choices'               => array(
						array(
							'text'       => esc_html__( 'Credit Card', 'gravityforms' ),
							'value'      => 'Credit Card',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'PayPal', 'gravityforms' ),
							'value'      => 'PayPal',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'formId'                => 5,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'enableOtherChoice'     => '',
					'enablePrice'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => 'c3313dae',
				),
			),
			'version'                    => '2.7',
			'id'                         => 5,
			'useCurrentUserAsAuthor'     => true,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => array(
				'type'     => 'text',
				'text'     => esc_html__( 'Previous', 'gravityforms' ),
				'imageUrl' => '',
			),
			'pagination'                 => array(
				'type'                                => 'percentage',
				'pages'                               => array(
					'',
					'',
					'',
				),
				'style'                               => 'orange',
				'backgroundColor'                     => null,
				'color'                               => null,
				'display_progressbar_on_confirmation' => false,
				'progressbar_completion_text'         => null,
			),
			'firstPageCssClass'          => '',
			'subLabelPlacement'          => 'above',
			'cssClass'                   => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'nextFieldId'                => 17,
			'feeds'                      => array(
				'gravityformsadvancedpostcreation' => array(),
			),
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'customRequiredIndicator'    => '',
			'markupVersion'              => 2,
			'confirmations'              => array(
				'59de5efdd62d1' => array(
					'id'          => '59de5efdd62d1',
					'name'        => 'Default Confirmation',
					'isDefault'   => true,
					'type'        => 'message',
					'message'     => esc_html__( 'Thank you for shopping with us! Your payment was successfully completed.', 'gravityforms' ),
					'url'         => '',
					'pageId'      => '',
					'queryString' => '',
				),
			),
			'notifications'              => array(
				'51794abf1f0d2' => array(
					'id'      => '51794abf1f0d2',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'New submission from {form_title}', 'gravityforms' ),
					'message' => '{all_fields}',
					'toType'  => 'email',
					'to' => '{admin_email}',
					'event'   => 'form_submission',
					'name'    => 'Admin Notification',
				),
			),
		),
		'version'               => '2.7',
	),
	'stripe'               => array(
		'id'                    => 'stripe',
		'title'                 => esc_html__( 'Stripe Checkout Form', 'gravityforms' ),
		'description'           => esc_html__( 'A form that allows you to sell products and let your customers pay via Stripe', 'gravityforms' ),
		'template_background'   => 'iceberg-blue',
		'template_thumbnail'    => 'eCommerceStripe.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/ecommerce-stripe-form/',
		'template_access_level' => [ 'pro', 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'title'                      => esc_html__( 'Stripe Checkout Form', 'gravityforms' ),
			'description'                => esc_html__( 'A form that allows you to sell products and let your customers pay via Stripe', 'gravityforms' ),
			'labelPlacement'             => 'top_label',
			'descriptionPlacement'       => 'above',
			'button'                     => array(
				'type'                 => 'text',
				'text'                 => esc_html__( 'Submit', 'gravityforms' ),
				'imageUrl'             => '',
				'conditionalLogic'     => null,
				'width'                => 'auto',
				'location'             => 'bottom',
				'layoutGridColumnSpan' => 12,
			),
			'fields'                     => array(
				array(
					'type'                  => 'product',
					'id'                    => 1,
					'label'                 => esc_html__( 'My Super Awesome Product', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => array(
						array(
							'id'    => '1.1',
							'label' => esc_html__( 'Name', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '1.2',
							'label' => esc_html__( 'Price', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '1.3',
							'label' => esc_html__( 'Quantity', 'gravityforms' ),
							'name'  => '',
						),
					),
					'inputType'             => 'singleproduct',
					'enablePrice'           => null,
					'description'           => esc_html__( 'This is my super awesome product. It\'s the best, so everyone should buy it!', 'gravityforms' ),
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => null,
					'conditionalLogic'      => '',
					'productField'          => '',
					'basePrice'             => '$10.00',
					'disableQuantity'       => false,
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '31aea450',
					'validateState'         => true,
					'formId'                => 18,
					'checkboxLabel'         => '',
				),
				array(
					'type'                  => 'product',
					'id'                    => 11,
					'label'                 => esc_html__( 'Another Amazing Product', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => array(
						array(
							'id'    => '11.1',
							'label' => esc_html__( 'Name', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '11.2',
							'label' => esc_html__( 'Price', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '11.3',
							'label' => esc_html__( 'Quantity', 'gravityforms' ),
							'name'  => '',
						),
					),
					'inputType'             => 'singleproduct',
					'enablePrice'           => null,
					'description'           => esc_html__( 'If you loved the first product, you\'re really going to love this one. Don\'t miss out, order yours while they\'re still in stock.', 'gravityforms' ),
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => null,
					'conditionalLogic'      => '',
					'productField'          => '',
					'basePrice'             => '$15.00',
					'disableQuantity'       => false,
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '03b4852e',
					'validateState'         => true,
					'formId'                => 18,
				),
				array(
					'type'                  => 'page',
					'id'                    => 10,
					'label'                 => '',
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'displayOnly'           => true,
					'nextButton'            => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Next', 'gravityforms' ),
						'imageUrl' => '',
					),
					'previousButton'        => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Previous', 'gravityforms' ),
						'imageUrl' => '',
					),
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '94bf6611',
					'formId'                => 18,
				),
				array(
					'type'                  => 'name',
					'id'                    => 7,
					'label'                 => esc_html__( 'Name', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'nameFormat'            => 'advanced',
					'inputs'                => array(
						array(
							'id'                    => '7.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'       => esc_html__( 'Mr.', 'gravityforms' ),
									'value'      => 'Mr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Mrs.', 'gravityforms' ),
									'value'      => 'Mrs.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Miss', 'gravityforms' ),
									'value'      => 'Miss',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Ms.', 'gravityforms' ),
									'value'      => 'Ms.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Dr.', 'gravityforms' ),
									'value'      => 'Dr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Prof.', 'gravityforms' ),
									'value'      => 'Prof.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Rev.', 'gravityforms' ),
									'value'      => 'Rev.',
									'isSelected' => false,
									'price'      => '',
								),
							),
							'isHidden'  => true,
							'inputType' => 'radio',
						),
						array(
							'id'                    => '7.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '7.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '7.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '7.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '2a2cb23e',
					'formId'                => 18,
				),
				array(
					'type'                  => 'email',
					'id'                    => 9,
					'label'                 => esc_html__( 'Email', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'emailConfirmEnabled'   => '',
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => 'email',
					'layoutGroupId'         => '6884a6d5',
					'formId'                => 18,
				),
				array(
					'type'                  => 'page',
					'id'                    => 12,
					'label'                 => '',
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'displayOnly'           => true,
					'nextButton'            => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Next', 'gravityforms' ),
						'imageUrl' => '',
					),
					'previousButton'        => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Previous', 'gravityforms' ),
						'imageUrl' => '',
					),
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '8d96f708',
					'formId'                => 18,
				),
				array(
					'type'                    => 'address',
					'id'                      => 14,
					'label'                   => esc_html__( 'Billing Address', 'gravityforms' ),
					'adminLabel'              => '',
					'isRequired'              => false,
					'size'                    => 'medium',
					'errorMessage'            => '',
					'addressType'             => 'international',
					'inputs'                  => array(
						array(
							'id'                    => '14.1',
							'label'                 => esc_html__( 'Street Address', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line1',
						),
						array(
							'id'                    => '14.2',
							'label'                 => esc_html__( 'Address Line 2', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line2',
						),
						array(
							'id'                    => '14.3',
							'label'                 => esc_html__( 'City', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level2',
						),
						array(
							'id'                    => '14.4',
							'label'                 => esc_html__( 'State / Province', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level1',
						),
						array(
							'id'                    => '14.5',
							'label'                 => esc_html__( 'ZIP / Postal Code', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'postal-code',
						),
						array(
							'id'                    => '14.6',
							'label'                 => esc_html__( 'Country', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'country-name',
						),
					),
					'description'             => '',
					'allowsPrepopulate'       => false,
					'inputMask'               => false,
					'inputMaskValue'          => '',
					'inputType'               => '',
					'labelPlacement'          => '',
					'descriptionPlacement'    => '',
					'subLabelPlacement'       => '',
					'placeholder'             => '',
					'cssClass'                => '',
					'inputName'               => '',
					'visibility'              => 'visible',
					'noDuplicates'            => false,
					'defaultValue'            => '',
					'choices'                 => '',
					'conditionalLogic'        => '',
					'defaultCountry'          => '',
					'defaultProvince'         => '',
					'productField'            => '',
					'defaultState'            => '',
					'enableCopyValuesOption'  => '',
					'copyValuesOptionDefault' => '',
					'copyValuesOptionLabel'   => '',
					'multipleFiles'           => false,
					'maxFiles'                => '',
					'calculationFormula'      => '',
					'calculationRounding'     => '',
					'enableCalculation'       => '',
					'disableQuantity'         => false,
					'displayAllCategories'    => false,
					'useRichTextEditor'       => false,
					'displayOnly'             => '',
					'fields'                  => '',
					'inputMaskIsCustom'       => '',
					'maxLength'               => '',
					'enableAutocomplete'      => true,
					'autocompleteAttribute'   => '',
					'layoutGroupId'           => 'f411d92c',
					'formId'                  => 18,
				),
				array(
					'type'                    => 'address',
					'id'                      => 8,
					'label'                   => esc_html__( 'Shipping Address', 'gravityforms' ),
					'adminLabel'              => '',
					'isRequired'              => false,
					'size'                    => 'medium',
					'errorMessage'            => '',
					'addressType'             => 'international',
					'inputs'                  => array(
						array(
							'id'                    => '8.1',
							'label'                 => esc_html__( 'Street Address', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line1',
						),
						array(
							'id'                    => '8.2',
							'label'                 => esc_html__( 'Address Line 2', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line2',
						),
						array(
							'id'                    => '8.3',
							'label'                 => esc_html__( 'City', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level2',
						),
						array(
							'id'                    => '8.4',
							'label'                 => esc_html__( 'State / Province', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level1',
						),
						array(
							'id'                    => '8.5',
							'label'                 => esc_html__( 'ZIP / Postal Code', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'postal-code',
						),
						array(
							'id'                    => '8.6',
							'label'                 => esc_html__( 'Country', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'country-name',
						),
					),
					'description'             => '',
					'allowsPrepopulate'       => false,
					'inputMask'               => false,
					'inputMaskValue'          => '',
					'inputType'               => '',
					'labelPlacement'          => '',
					'descriptionPlacement'    => '',
					'subLabelPlacement'       => '',
					'placeholder'             => '',
					'cssClass'                => '',
					'inputName'               => '',
					'visibility'              => 'visible',
					'noDuplicates'            => false,
					'defaultValue'            => '',
					'choices'                 => '',
					'conditionalLogic'        => '',
					'defaultCountry'          => '',
					'defaultProvince'         => '',
					'productField'            => '',
					'defaultState'            => '',
					'enableCopyValuesOption'  => 1,
					'copyValuesOptionDefault' => 1,
					'copyValuesOptionLabel'   => 'Same as billing address',
					'displayOnly'             => '',
					'multipleFiles'           => false,
					'maxFiles'                => '',
					'calculationFormula'      => '',
					'calculationRounding'     => '',
					'enableCalculation'       => '',
					'disableQuantity'         => false,
					'displayAllCategories'    => false,
					'useRichTextEditor'       => false,
					'copyValuesOptionField'   => '14',
					'fields'                  => '',
					'inputMaskIsCustom'       => '',
					'maxLength'               => '',
					'enableAutocomplete'      => true,
					'autocompleteAttribute'   => '',
					'layoutGroupId'           => '141adef5',
					'formId'                  => 18,
				),
				array(
					'type'                  => 'shipping',
					'id'                    => 5,
					'label'                 => esc_html__( 'Shipping', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'inputType'             => 'radio',
					'enablePrice'           => true,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => array(
						array(
							'text'       => esc_html__( 'Standard Shipping', 'gravityforms' ),
							'value'      => 'Standard Shipping',
							'isSelected' => false,
							'price'      => '$5.00',
						),
						array(
							'text'       => esc_html__( 'Express Shipping', 'gravityforms' ),
							'value'      => 'Express Shipping',
							'isSelected' => false,
							'price'      => '$10.00',
						),
						array(
							'text'       => esc_html__( 'Overnight Shipping', 'gravityforms' ),
							'value'      => 'Overnight Shipping',
							'isSelected' => false,
							'price'      => '$20.00',
						),
					),
					'conditionalLogic'      => false,
					'basePrice'             => '$0.00',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'enableChoiceValue'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => 'be3a214b',
					'validateState'         => true,
					'formId'                => 18,
				),
				array(
					'type'                  => 'total',
					'id'                    => 3,
					'label'                 => esc_html__( 'Total', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '04984a61',
					'formId'                => 18,
				),
				array(
					'type'                  => 'stripe_creditcard',
					'id'                    => 17,
					'formId'                => 18,
					'label'                 => esc_html__( 'Credit Card', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'large',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => array(
						array(
							'id'    => '17.1',
							'label' => esc_html__( 'Card Details', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '17.4',
							'label' => esc_html__( 'Card Type', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '17.5',
							'label' => esc_html__( 'Cardholder Name', 'gravityforms' ),
							'name'  => '',
						),
					),
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'layoutGridColumnSpan'  => 12,
					'enableEnhancedUI'      => 0,
					'layoutGroupId'         => 'b9aa1ed5',
					'fields'                => '',
				),
			),
			'useCurrentUserAsAuthor'     => true,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => array(
				'type'     => 'text',
				'text'     => esc_html__( 'Previous', 'gravityforms' ),
				'imageUrl' => '',
			),
			'pagination'                 => array(
				'type'                                => 'percentage',
				'pages'                               => array(
					'',
					'',
					'',
				),
				'style'                               => 'orange',
				'backgroundColor'                     => null,
				'color'                               => null,
				'display_progressbar_on_confirmation' => false,
				'progressbar_completion_text'         => null,
			),
			'firstPageCssClass'          => '',
			'subLabelPlacement'          => 'above',
			'cssClass'                   => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'nextFieldId'                => 18,
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'customRequiredIndicator'    => '',
			'markupVersion'              => 2,
			'confirmations'              => array(
				'62f267e2abf85' => array(
					'id'          => '62f267e2abf85',
					'name'        => 'Default Confirmation',
					'isDefault'   => true,
					'type'        => 'message',
					'message'     => esc_html__( 'Thank you for shopping with us! Your payment was successfully completed.', 'gravityforms' ),
					'url'         => '',
					'pageId'      => '',
					'queryString' => '',
				),
			),
			'notifications'              => array(
				'51794abf1f0d2' => array(
					'id'      => '51794abf1f0d2',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'New submission from {form_title}', 'gravityforms' ),
					'message' => '{all_fields}',
					'toType'  => 'email',
					'to' => '{admin_email}',
					'event'   => 'form_submission',
					'name'    => 'Admin Notification',
				),
			),
		),
	),
	'paypal'               => array(
		'id'                    => 'paypal',
		'title'                 => esc_html__( 'PayPal Checkout Form', 'gravityforms' ),
		'description'           => esc_html__( 'A form that allows you to sell products and let your customers pay via PayPal', 'gravityforms' ),
		'template_background'   => 'iceberg-blue',
		'template_thumbnail'    => 'eCommercePayPal.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/ecommerce-paypal-form/',
		'template_access_level' => [ 'pro', 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'title'                      => esc_html__( 'PayPal Checkout Form', 'gravityforms' ),
			'description'                => esc_html__( 'A form that allows you to sell products and let your customers pay via PayPal', 'gravityforms' ),
			'labelPlacement'             => 'top_label',
			'descriptionPlacement'       => 'above',
			'button'                     => array(
				'type'                 => 'text',
				'text'                 => esc_html__( 'Submit', 'gravityforms' ),
				'imageUrl'             => '',
				'conditionalLogic'     => null,
				'width'                => 'auto',
				'location'             => 'bottom',
				'layoutGridColumnSpan' => 12,
			),
			'fields'                     => array(
				array(
					'type'                  => 'product',
					'id'                    => 1,
					'label'                 => esc_html__( 'My Super Awesome Product', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => array(
						array(
							'id'    => '1.1',
							'label' => esc_html__( 'Name', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '1.2',
							'label' => esc_html__( 'Price', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '1.3',
							'label' => esc_html__( 'Quantity', 'gravityforms' ),
							'name'  => '',
						),
					),
					'inputType'             => 'singleproduct',
					'enablePrice'           => null,
					'description'           => esc_html__( 'This is my super awesome product. It\'s the best, so everyone should buy it!', 'gravityforms' ),
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => null,
					'conditionalLogic'      => '',
					'productField'          => '',
					'basePrice'             => '$10.00',
					'disableQuantity'       => false,
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '31aea450',
					'validateState'         => true,
					'formId'                => 18,
					'checkboxLabel'         => '',
				),
				array(
					'type'                  => 'product',
					'id'                    => 11,
					'label'                 => esc_html__( 'Another Amazing Product', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => array(
						array(
							'id'    => '11.1',
							'label' => esc_html__( 'Name', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '11.2',
							'label' => esc_html__( 'Price', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '11.3',
							'label' => esc_html__( 'Quantity', 'gravityforms' ),
							'name'  => '',
						),
					),
					'inputType'             => 'singleproduct',
					'enablePrice'           => null,
					'description'           => esc_html__( 'If you loved the first product, you\'re really going to love this one. Don\'t miss out, order yours while they\'re still in stock.', 'gravityforms' ),
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => null,
					'conditionalLogic'      => '',
					'productField'          => '',
					'basePrice'             => '$15.00',
					'disableQuantity'       => false,
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '03b4852e',
					'validateState'         => true,
					'formId'                => 18,
				),
				array(
					'type'                  => 'page',
					'id'                    => 10,
					'label'                 => '',
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'displayOnly'           => true,
					'nextButton'            => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Next', 'gravityforms' ),
						'imageUrl' => '',
					),
					'previousButton'        => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Previous', 'gravityforms' ),
						'imageUrl' => '',
					),
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '94bf6611',
					'formId'                => 18,
				),
				array(
					'type'                  => 'name',
					'id'                    => 7,
					'label'                 => esc_html__( 'Name', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'nameFormat'            => 'advanced',
					'inputs'                => array(
						array(
							'id'                    => '7.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'       => esc_html__( 'Mr.', 'gravityforms' ),
									'value'      => 'Mr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Mrs.', 'gravityforms' ),
									'value'      => 'Mrs.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Miss', 'gravityforms' ),
									'value'      => 'Miss',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Ms.', 'gravityforms' ),
									'value'      => 'Ms.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Dr.', 'gravityforms' ),
									'value'      => 'Dr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Prof.', 'gravityforms' ),
									'value'      => 'Prof.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Rev.', 'gravityforms' ),
									'value'      => 'Rev.',
									'isSelected' => false,
									'price'      => '',
								),
							),
							'isHidden'  => true,
							'inputType' => 'radio',
						),
						array(
							'id'                    => '7.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '7.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '7.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '7.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '2a2cb23e',
					'formId'                => 18,
				),
				array(
					'type'                  => 'email',
					'id'                    => 9,
					'label'                 => esc_html__( 'Email', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'emailConfirmEnabled'   => '',
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => 'email',
					'layoutGroupId'         => '6884a6d5',
					'formId'                => 18,
				),
				array(
					'type'                  => 'page',
					'id'                    => 12,
					'label'                 => '',
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'displayOnly'           => true,
					'nextButton'            => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Next', 'gravityforms' ),
						'imageUrl' => '',
					),
					'previousButton'        => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Previous', 'gravityforms' ),
						'imageUrl' => '',
					),
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '8d96f708',
					'formId'                => 18,
				),
				array(
					'type'                    => 'address',
					'id'                      => 14,
					'label'                   => esc_html__( 'Billing Address', 'gravityforms' ),
					'adminLabel'              => '',
					'isRequired'              => false,
					'size'                    => 'medium',
					'errorMessage'            => '',
					'addressType'             => 'international',
					'inputs'                  => array(
						array(
							'id'                    => '14.1',
							'label'                 => esc_html__( 'Street Address', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line1',
						),
						array(
							'id'                    => '14.2',
							'label'                 => esc_html__( 'Address Line 2', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line2',
						),
						array(
							'id'                    => '14.3',
							'label'                 => esc_html__( 'City', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level2',
						),
						array(
							'id'                    => '14.4',
							'label'                 => esc_html__( 'State / Province', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level1',
						),
						array(
							'id'                    => '14.5',
							'label'                 => esc_html__( 'ZIP / Postal Code', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'postal-code',
						),
						array(
							'id'                    => '14.6',
							'label'                 => esc_html__( 'Country', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'country-name',
						),
					),
					'description'             => '',
					'allowsPrepopulate'       => false,
					'inputMask'               => false,
					'inputMaskValue'          => '',
					'inputType'               => '',
					'labelPlacement'          => '',
					'descriptionPlacement'    => '',
					'subLabelPlacement'       => '',
					'placeholder'             => '',
					'cssClass'                => '',
					'inputName'               => '',
					'visibility'              => 'visible',
					'noDuplicates'            => false,
					'defaultValue'            => '',
					'choices'                 => '',
					'conditionalLogic'        => '',
					'defaultCountry'          => '',
					'defaultProvince'         => '',
					'productField'            => '',
					'defaultState'            => '',
					'enableCopyValuesOption'  => '',
					'copyValuesOptionDefault' => '',
					'copyValuesOptionLabel'   => '',
					'multipleFiles'           => false,
					'maxFiles'                => '',
					'calculationFormula'      => '',
					'calculationRounding'     => '',
					'enableCalculation'       => '',
					'disableQuantity'         => false,
					'displayAllCategories'    => false,
					'useRichTextEditor'       => false,
					'displayOnly'             => '',
					'fields'                  => '',
					'inputMaskIsCustom'       => '',
					'maxLength'               => '',
					'enableAutocomplete'      => true,
					'autocompleteAttribute'   => '',
					'layoutGroupId'           => 'f411d92c',
					'formId'                  => 18,
				),
				array(
					'type'                    => 'address',
					'id'                      => 8,
					'label'                   => esc_html__( 'Shipping Address', 'gravityforms' ),
					'adminLabel'              => '',
					'isRequired'              => false,
					'size'                    => 'medium',
					'errorMessage'            => '',
					'addressType'             => 'international',
					'inputs'                  => array(
						array(
							'id'                    => '8.1',
							'label'                 => esc_html__( 'Street Address', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line1',
						),
						array(
							'id'                    => '8.2',
							'label'                 => esc_html__( 'Address Line 2', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line2',
						),
						array(
							'id'                    => '8.3',
							'label'                 => esc_html__( 'City', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level2',
						),
						array(
							'id'                    => '8.4',
							'label'                 => esc_html__( 'State / Province', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level1',
						),
						array(
							'id'                    => '8.5',
							'label'                 => esc_html__( 'ZIP / Postal Code', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'postal-code',
						),
						array(
							'id'                    => '8.6',
							'label'                 => esc_html__( 'Country', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'country-name',
						),
					),
					'description'             => '',
					'allowsPrepopulate'       => false,
					'inputMask'               => false,
					'inputMaskValue'          => '',
					'inputType'               => '',
					'labelPlacement'          => '',
					'descriptionPlacement'    => '',
					'subLabelPlacement'       => '',
					'placeholder'             => '',
					'cssClass'                => '',
					'inputName'               => '',
					'visibility'              => 'visible',
					'noDuplicates'            => false,
					'defaultValue'            => '',
					'choices'                 => '',
					'conditionalLogic'        => '',
					'defaultCountry'          => '',
					'defaultProvince'         => '',
					'productField'            => '',
					'defaultState'            => '',
					'enableCopyValuesOption'  => 1,
					'copyValuesOptionDefault' => 1,
					'copyValuesOptionLabel'   => 'Same as billing address',
					'displayOnly'             => '',
					'multipleFiles'           => false,
					'maxFiles'                => '',
					'calculationFormula'      => '',
					'calculationRounding'     => '',
					'enableCalculation'       => '',
					'disableQuantity'         => false,
					'displayAllCategories'    => false,
					'useRichTextEditor'       => false,
					'copyValuesOptionField'   => '14',
					'fields'                  => '',
					'inputMaskIsCustom'       => '',
					'maxLength'               => '',
					'enableAutocomplete'      => true,
					'autocompleteAttribute'   => '',
					'layoutGroupId'           => '141adef5',
					'formId'                  => 18,
				),
				array(
					'type'                  => 'shipping',
					'id'                    => 5,
					'label'                 => esc_html__( 'Shipping', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'inputType'             => 'radio',
					'enablePrice'           => true,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => array(
						array(
							'text'       => esc_html__( 'Standard Shipping', 'gravityforms' ),
							'value'      => 'Standard Shipping',
							'isSelected' => false,
							'price'      => '$5.00',
						),
						array(
							'text'       => esc_html__( 'Express Shipping', 'gravityforms' ),
							'value'      => 'Express Shipping',
							'isSelected' => false,
							'price'      => '$10.00',
						),
						array(
							'text'       => esc_html__( 'Overnight Shipping', 'gravityforms' ),
							'value'      => 'Overnight Shipping',
							'isSelected' => false,
							'price'      => '$20.00',
						),
					),
					'conditionalLogic'      => false,
					'basePrice'             => '$0.00',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'enableChoiceValue'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => 'be3a214b',
					'validateState'         => true,
					'formId'                => 18,
				),
				array(
					'type'                  => 'total',
					'id'                    => 3,
					'label'                 => esc_html__( 'Total', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '04984a61',
					'formId'                => 18,
				),
				array(
					'type'                  => 'paypal',
					'id'                    => 18,
					'formId'                => 18,
					'label'                 => esc_html__( 'Payment Method', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'large',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => array(
						array(
							'id'    => '18.1',
							'label' => esc_html__( 'Card Number', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '18.2',
							'label' => esc_html__( 'Expiration Date', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '18.3',
							'label' => esc_html__( 'Security Code', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '18.4',
							'label' => esc_html__( 'Card Type', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '18.5',
							'label' => esc_html__( 'Cardholder Name', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '18.6',
							'label' => esc_html__( 'Payment Method', 'gravityforms' ),
							'name'  => '',
						),
					),
					'methods'               => array(
						'PayPal Checkout',
						'Credit Card',
					),
					'paypalPaymentButtons'  => '1',
					'buttonsLayout'         => 'vertical',
					'buttonsSize'           => 'medium',
					'buttonsShape'          => 'rect',
					'buttonsColor'          => 'gold',
					'displayCreditMessages' => '',
					'defaultPaymentMethod'  => 'PayPal Checkout',
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'layoutGridColumnSpan'  => 12,
					'enableEnhancedUI'      => 0,
					'layoutGroupId'         => 'dfc4a76f',
					'fields'                => '',
				),
			),
			'version'                    => '2.7',
			'useCurrentUserAsAuthor'     => true,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => array(
				'type'     => 'text',
				'text'     => esc_html__( 'Previous', 'gravityforms' ),
				'imageUrl' => '',
			),
			'pagination'                 => array(
				'type'                                => 'percentage',
				'pages'                               => array(
					'',
					'',
					'',
				),
				'style'                               => 'orange',
				'backgroundColor'                     => null,
				'color'                               => null,
				'display_progressbar_on_confirmation' => false,
				'progressbar_completion_text'         => null,
			),
			'firstPageCssClass'          => '',
			'subLabelPlacement'          => 'above',
			'cssClass'                   => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'nextFieldId'                => 19,
			'feeds'                      => array(
				'gravityformsadvancedpostcreation' => array(),
			),
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'customRequiredIndicator'    => '',
			'markupVersion'              => 2,
			'id'                         => 18,
			'confirmations'              => array(
				'62f267e2abf85' => array(
					'id'          => '62f267e2abf85',
					'name'        => 'Default Confirmation',
					'isDefault'   => true,
					'type'        => 'message',
					'message'     => esc_html__( 'Thank you for shopping with us! Your payment was successfully completed.', 'gravityforms' ),
					'url'         => '',
					'pageId'      => '',
					'queryString' => '',
				),
			),
			'notifications'              => array(
				'51794abf1f0d2' => array(
					'id'      => '51794abf1f0d2',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'New submission from {form_title}', 'gravityforms' ),
					'message' => '{all_fields}',
					'toType'  => 'email',
					'to' => '{admin_email}',
					'event'   => 'form_submission',
					'name'    => 'Admin Notification',
				),
			),
		),
		'version'               => '2.7',
	),
	'employment'           => array(
		'id'                    => 'employment',
		'description'           => esc_html__( 'Allow your users to apply for a job', 'gravityforms' ),
		'title'                 => esc_html__( 'Employment Application Form', 'gravityforms' ),
		'version'               => '2.7',
		'template_background'   => 'maverick-purple',
		'template_thumbnail'    => 'Employment.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/employment-application-form/',
		'template_access_level' => [ 'godaddy', 'gravityflow', 'gravityview', 'single', 'ltsingle', 'basic', 'multi', 'ltmulti', 'pro', 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'title'                      => esc_html__( 'Employment Application Form', 'gravityforms' ),
			'description'                => '',
			'labelPlacement'             => 'top_label',
			'descriptionPlacement'       => 'above',
			'button'                     => array(
				'type'             => 'text',
				'text'             => esc_html__( 'Submit Application', 'gravityforms' ),
				'imageUrl'         => '',
				'conditionalLogic' => null,
			),
			'fields'                     => array(
				array(
					'type'                  => 'section',
					'id'                    => 8,
					'label'                 => esc_html__( 'Your Personal Information', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'displayOnly'           => true,
					'formId'                => 93,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'layoutGroupId'         => '0fd84b02',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'name',
					'id'                    => 1,
					'label'                 => esc_html__( 'Your Name', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'nameFormat'            => 'advanced',
					'inputs'                => array(
						array(
							'id'                    => '1.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'       => esc_html__( 'Mr.', 'gravityforms' ),
									'value'      => 'Mr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Mrs.', 'gravityforms' ),
									'value'      => 'Mrs.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Miss', 'gravityforms' ),
									'value'      => 'Miss',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Ms.', 'gravityforms' ),
									'value'      => 'Ms.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Dr.', 'gravityforms' ),
									'value'      => 'Dr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Prof.', 'gravityforms' ),
									'value'      => 'Prof.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Rev.', 'gravityforms' ),
									'value'      => 'Rev.',
									'isSelected' => false,
									'price'      => '',
								),
							),
							'isHidden'    => true,
							'inputType'   => 'radio',
							'placeholder' => '',
						),
						array(
							'id'                    => '1.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'placeholder'           => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '1.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '1.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'placeholder'           => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '1.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'formId'                => 93,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'layoutGroupId'         => '5d413ccd',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'email',
					'id'                    => 2,
					'label'                 => esc_html__( 'Your Email Address', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => array(
						array(
							'id'                    => '2',
							'label'                 => esc_html__( 'Enter Email', 'gravityforms' ),
							'name'                  => '',
							'placeholder'           => '',
							'autocompleteAttribute' => 'email',
						),
						array(
							'id'                    => '2.2',
							'label'                 => esc_html__( 'Confirm Email', 'gravityforms' ),
							'name'                  => '',
							'placeholder'           => '',
							'autocompleteAttribute' => 'email',
						),
					),
					'formId'                => 93,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'emailConfirmEnabled'   => true,
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'layoutGroupId'         => '83be58aa',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => 'email',
				),
				array(
					'type'                    => 'address',
					'id'                      => 21,
					'label'                   => esc_html__( 'Address', 'gravityforms' ),
					'adminLabel'              => '',
					'isRequired'              => false,
					'size'                    => 'medium',
					'errorMessage'            => '',
					'visibility'              => 'visible',
					'addressType'             => 'international',
					'inputs'                  => array(
						array(
							'id'                    => '21.1',
							'label'                 => esc_html__( 'Street Address', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line1',
						),
						array(
							'id'                    => '21.2',
							'label'                 => esc_html__( 'Address Line 2', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line2',
						),
						array(
							'id'                    => '21.3',
							'label'                 => esc_html__( 'City', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level2',
						),
						array(
							'id'                    => '21.4',
							'label'                 => esc_html__( 'State / Province', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level1',
						),
						array(
							'id'                    => '21.5',
							'label'                 => esc_html__( 'ZIP / Postal Code', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'postal-code',
						),
						array(
							'id'                    => '21.6',
							'label'                 => esc_html__( 'Country', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'country-name',
						),
					),
					'formId'                  => 93,
					'description'             => '',
					'allowsPrepopulate'       => false,
					'inputMask'               => false,
					'inputMaskValue'          => '',
					'inputMaskIsCustom'       => '',
					'maxLength'               => '',
					'inputType'               => '',
					'labelPlacement'          => '',
					'descriptionPlacement'    => '',
					'subLabelPlacement'       => '',
					'placeholder'             => '',
					'cssClass'                => '',
					'inputName'               => '',
					'noDuplicates'            => false,
					'defaultValue'            => '',
					'choices'                 => '',
					'conditionalLogic'        => '',
					'defaultCountry'          => '',
					'defaultProvince'         => '',
					'copyValuesOptionLabel'   => '',
					'productField'            => '',
					'hideCountry'             => '',
					'defaultState'            => '',
					'hideState'               => '',
					'hideAddress2'            => '',
					'enableCopyValuesOption'  => '',
					'copyValuesOptionDefault' => '',
					'fields'                  => '',
					'enableAutocomplete'      => true,
				),
				array(
					'type'                  => 'phone',
					'id'                    => 10,
					'label'                 => esc_html__( 'Your Phone', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'phoneFormat'           => 'standard',
					'formId'                => 93,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'form_id'               => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'layoutGroupId'         => '567d7e54',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => 'tel',
				),
				array(
					'type'                  => 'select',
					'id'                    => 14,
					'label'                 => esc_html__( 'Best Time To Call You', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'choices'               => array(
						array(
							'text'       => esc_html__( 'Mornings', 'gravityforms' ),
							'value'      => 'Mornings',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Early Afternoon', 'gravityforms' ),
							'value'      => 'Early Afternoon',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Late Afternoon', 'gravityforms' ),
							'value'      => 'Late Afternoon',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Early Evening', 'gravityforms' ),
							'value'      => 'Early Evening',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'formId'                => 93,
					'description'           => esc_html__( 'When is the best time for us to reach you via telephone?', 'gravityforms' ),
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => 'above',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'enablePrice'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'layoutGroupId'         => '8e11d091',
					'autocompleteAttribute' => '',
					'enableAutocomplete'    => false,
				),
				array(
					'type'                 => 'page',
					'id'                   => 18,
					'label'                => '',
					'adminLabel'           => '',
					'isRequired'           => false,
					'size'                 => 'medium',
					'errorMessage'         => '',
					'visibility'           => 'visible',
					'inputs'               => null,
					'displayOnly'          => true,
					'nextButton'           => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Next', 'gravityforms' ),
						'imageUrl' => '',
					),
					'previousButton'       => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Previous', 'gravityforms' ),
						'imageUrl' => '',
					),
					'formId'               => 93,
					'description'          => '',
					'allowsPrepopulate'    => false,
					'inputMask'            => false,
					'inputMaskValue'       => '',
					'inputMaskIsCustom'    => '',
					'maxLength'            => '',
					'inputType'            => '',
					'labelPlacement'       => '',
					'descriptionPlacement' => '',
					'subLabelPlacement'    => '',
					'placeholder'          => '',
					'cssClass'             => '',
					'inputName'            => '',
					'noDuplicates'         => false,
					'defaultValue'         => '',
					'choices'              => '',
					'conditionalLogic'     => '',
					'productField'         => '',
					'fields'               => '',
				),
				array(
					'type'                  => 'section',
					'id'                    => 7,
					'label'                 => esc_html__( 'Position You\'re Applying For', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'displayOnly'           => true,
					'formId'                => 93,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'layoutGroupId'         => '76c4b57b',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'radio',
					'id'                    => 3,
					'label'                 => esc_html__( 'Position You\'re Applying For', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'choices'               => array(
						array(
							'text'       => esc_html__( 'Sales', 'gravityforms' ),
							'value'      => 'Sales',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Marketing', 'gravityforms' ),
							'value'      => 'Marketing',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Engineering', 'gravityforms' ),
							'value'      => 'Engineering',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'IT/Technical', 'gravityforms' ),
							'value'      => 'IT/Technical',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Clerical/Accounting', 'gravityforms' ),
							'value'      => 'Clerical/Accounting',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Facilities Maintenance', 'gravityforms' ),
							'value'      => 'Facilities Maintenance',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'formId'                => 93,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => 'gf_list_2col',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'enableOtherChoice'     => '',
					'enablePrice'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'layoutGroupId'         => 'c7d66f06',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'list',
					'id'                    => 4,
					'label'                 => esc_html__( 'Hours You Are Available for Work', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'formId'                => 93,
					'description'           => esc_html__( 'Please tell us what hours you are available for work each day of the week.', 'gravityforms' ),
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => 'above',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => array(
						array(
							'text'       => esc_html__( 'Monday', 'gravityforms' ),
							'value'      => 'Monday',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Tuesday', 'gravityforms' ),
							'value'      => 'Tuesday',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Wednesday', 'gravityforms' ),
							'value'      => 'Wednesday',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Thursday', 'gravityforms' ),
							'value'      => 'Thursday',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Friday', 'gravityforms' ),
							'value'      => 'Friday',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'conditionalLogic'      => '',
					'maxRows'               => 3,
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'enableColumns'         => true,
					'displayOnly'           => '',
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'layoutGroupId'         => 'cb7f97e8',
					'autocompleteAttribute' => '',
					'enableAutocomplete'    => false,
					'enablePrice'           => '',
				),
				array(
					'type'                  => 'section',
					'id'                    => 6,
					'label'                 => esc_html__( 'Previous Employment', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'displayOnly'           => true,
					'formId'                => 93,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'layoutGroupId'         => '5e459a52',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'list',
					'id'                    => 5,
					'label'                 => esc_html__( 'Your Previous Employers', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'formId'                => 93,
					'description'           => esc_html__( 'Please list your previous employers, the dates you worked and the position you held', 'gravityforms' ),
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => 'above',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => array(
						array(
							'text'       => esc_html__( 'Employer', 'gravityforms' ),
							'value'      => 'Employer',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Dates', 'gravityforms' ),
							'value'      => 'Dates',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Position', 'gravityforms' ),
							'value'      => 'Position',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Phone', 'gravityforms' ),
							'value'      => 'Phone',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'conditionalLogic'      => '',
					'maxRows'               => 3,
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'enableColumns'         => true,
					'displayOnly'           => '',
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'layoutGroupId'         => '19b3651c',
					'autocompleteAttribute' => '',
					'checkboxLabel'         => '',
					'enableAutocomplete'    => false,
					'enablePrice'           => '',
				),
				array(
					'type'                 => 'page',
					'id'                   => 20,
					'label'                => '',
					'adminLabel'           => '',
					'isRequired'           => false,
					'size'                 => 'medium',
					'errorMessage'         => '',
					'visibility'           => 'visible',
					'inputs'               => null,
					'displayOnly'          => true,
					'nextButton'           => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Next', 'gravityforms' ),
						'imageUrl' => '',
					),
					'previousButton'       => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Previous', 'gravityforms' ),
						'imageUrl' => '',
					),
					'formId'               => 93,
					'description'          => '',
					'allowsPrepopulate'    => false,
					'inputMask'            => false,
					'inputMaskValue'       => '',
					'inputMaskIsCustom'    => '',
					'maxLength'            => '',
					'inputType'            => '',
					'labelPlacement'       => '',
					'descriptionPlacement' => '',
					'subLabelPlacement'    => '',
					'placeholder'          => '',
					'cssClass'             => '',
					'inputName'            => '',
					'noDuplicates'         => false,
					'defaultValue'         => '',
					'choices'              => '',
					'conditionalLogic'     => '',
					'productField'         => '',
					'fields'               => '',
				),
				array(
					'type'                  => 'section',
					'id'                    => 11,
					'label'                 => esc_html__( 'More About You', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'displayOnly'           => true,
					'formId'                => 93,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'layoutGroupId'         => 'f19a631f',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'textarea',
					'id'                    => 12,
					'label'                 => esc_html__( 'Tell Us About Yourself', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'formId'                => 93,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'form_id'               => '',
					'useRichTextEditor'     => false,
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'displayOnly'           => '',
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'layoutGroupId'         => 'a9d1642a',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'fileupload',
					'id'                    => 13,
					'label'                 => esc_html__( 'Upload Your Resume', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'formId'                => 93,
					'description'           => esc_html__( 'Upload your resume in .pdf, .doc or .docx format', 'gravityforms' ),
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'maxFileSize'           => 25,
					'maxFiles'              => '',
					'multipleFiles'         => false,
					'allowedExtensions'     => 'pdf, doc, docx',
					'productField'          => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'displayOnly'           => '',
					'visibility'            => 'visible',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'layoutGroupId'         => 'e4c38a69',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                     => 'consent',
					'checked_indicator_url'    => 'https://www.gravityforms.com/wp-content/plugins/gravityforms/images/tick.png',
					'checked_indicator_markup' => '<img src="https://www.gravityforms.com/wp-content/plugins/gravityforms/images/tick.png" />',
					'id'                       => 22,
					'label'                    => esc_html__( 'Terms and Conditions', 'gravityforms' ),
					'adminLabel'               => '',
					'isRequired'               => true,
					'size'                     => 'large',
					'errorMessage'             => '',
					'visibility'               => 'visible',
					'inputs'                   => array(
						array(
							'id'    => '22.1',
							'label' => esc_html__( 'Consent', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'       => '22.2',
							'label'    => esc_html__( 'Text', 'gravityforms' ),
							'name'     => '',
							'isHidden' => true,
						),
						array(
							'id'       => '22.3',
							'label'    => esc_html__( 'Description', 'gravityforms' ),
							'name'     => '',
							'isHidden' => true,
						),
					),
					'checkboxLabel'            => wp_kses_post( __( '<strong>I agree to the terms and conditions.</strong>', 'gravityforms' ) ),
					'descriptionplaceholder'   => '',
					'choices'                  => array(
						array(
							'text'       => esc_html__( 'Checked', 'gravityforms' ),
							'value'      => '1',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'formId'                   => 124,
					'description'              => esc_html__( 'Terms and conditions placeholder.', 'gravityforms' ),
					'allowsPrepopulate'        => false,
					'inputMask'                => false,
					'inputMaskValue'           => '',
					'inputMaskIsCustom'        => false,
					'maxLength'                => '',
					'labelPlacement'           => '',
					'descriptionPlacement'     => 'above',
					'subLabelPlacement'        => '',
					'placeholder'              => '',
					'cssClass'                 => '',
					'inputName'                => '',
					'noDuplicates'             => false,
					'defaultValue'             => '',
					'enableAutocomplete'       => false,
					'autocompleteAttribute'    => '',
					'conditionalLogic'         => '',
					'productField'             => '',
					'layoutGridColumnSpan'     => 12,
					'enableEnhancedUI'         => 0,
					'layoutGroupId'            => '27e48dc9',
					'multipleFiles'            => false,
					'maxFiles'                 => '',
					'calculationFormula'       => '',
					'calculationRounding'      => '',
					'enableCalculation'        => '',
					'disableQuantity'          => false,
					'displayAllCategories'     => false,
					'useRichTextEditor'        => false,
					'fields'                   => '',
					'displayOnly'              => '',
					'inputType'                => '',
				),
			),
			'version'                    => '2.7',
			'id'                         => 93,
			'useCurrentUserAsAuthor'     => true,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => array(
				'type'     => 'text',
				'text'     => esc_html__( 'Previous', 'gravityforms' ),
				'imageUrl' => '',
			),
			'pagination'                 => array(
				'type'                                => 'percentage',
				'pages'                               => array(
					'',
					'',
					'',
				),
				'style'                               => 'blue',
				'backgroundColor'                     => null,
				'color'                               => null,
				'display_progressbar_on_confirmation' => false,
				'progressbar_completion_text'         => null,
			),
			'firstPageCssClass'          => '',
			'subLabelPlacement'          => 'above',
			'cssClass'                   => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'nextFieldId'                => 23,
			'markupVersion'              => 2,
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'customRequiredIndicator'    => '',
			'delete_entry'               => '',
			'delete_entry_condition'     => '',
			'delete_entry_period'        => '',
			'delete_entry_units'         => 'hour',
			'form_slug'                  => 'employment-application-form',
			'confirmations'              => array(
				'57911f996f9f1' => array(
					'id'                => '57911f996f9f1',
					'name'              => 'Default Confirmation',
					'isDefault'         => true,
					'type'              => 'message',
					'message'           => esc_html__( 'Thank you for submitting your application! We will get in touch with you shortly.', 'gravityforms' ),
					'url'               => '',
					'pageId'            => 0,
					'queryString'       => '',
					'disableAutoformat' => false,
					'conditionalLogic'  => array(),
				),
			),
			'notifications'              => array(
				'51794abf1f0d1' => array(
					'id'      => '51794abf1f0d1',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'We have received your application.', 'gravityforms' ),
					'message' => wp_kses_post( __('<p>Hi {Name (First):1.3},</p><p>Thank you for submitting your application. We are in the process of reviewing it and will get in touch with you shortly.</p>','gravityforms' ) ),
					'toType'  => 'field',
					'toField' => '2',
					'to' => '2',
					'event'   => 'form_submission',
					'name'    => 'User Notification',
					'type'    => 'user',
				),
				'51794abf1f0d2' => array(
					'id'      => '51794abf1f0d2',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'New submission from {form_title}', 'gravityforms' ),
					'message' => '{all_fields}',
					'toType'  => 'email',
					'to' => '{admin_email}',
					'event'   => 'form_submission',
					'name'    => 'Admin Notification',
				),
			),
		),
	),
	'event'                => array(
		'id'                    => 'event',
		'description'           => esc_html__( 'Let your users book tickets for an event', 'gravityforms' ),
		'title'                 => esc_html__( 'Event Registration Form', 'gravityforms' ),
		'template_background'   => 'porcelain-gray',
		'template_thumbnail'    => 'Event.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/event-registration-form/',
		'template_access_level' => [ 'pro', 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'title'                      => esc_html__( 'Event Registration Form', 'gravityforms' ),
			'description'                => esc_html__( 'Please complete this form to register for the event.', 'gravityforms' ),
			'labelPlacement'             => 'top_label',
			'descriptionPlacement'       => 'below',
			'button'                     => array(
				'type'             => 'text',
				'text'             => esc_html__( 'Submit', 'gravityforms' ),
				'imageUrl'         => '',
				'conditionalLogic' => null,
			),
			'fields'                     => array(
				array(
					'type'                  => 'section',
					'id'                    => 12,
					'label'                 => esc_html__( 'Contact Details', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'displayOnly'           => true,
					'formId'                => 6,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'layoutGroupId'         => 'fcb47101',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'name',
					'id'                    => 1,
					'label'                 => esc_html__( 'Name', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'nameFormat'            => 'advanced',
					'inputs'                => array(
						array(
							'id'                    => '1.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'       => esc_html__( 'Mr.', 'gravityforms' ),
									'value'      => 'Mr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Mrs.', 'gravityforms' ),
									'value'      => 'Mrs.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Miss', 'gravityforms' ),
									'value'      => 'Miss',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Ms.', 'gravityforms' ),
									'value'      => 'Ms.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Dr.', 'gravityforms' ),
									'value'      => 'Dr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Prof.', 'gravityforms' ),
									'value'      => 'Prof.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Rev.', 'gravityforms' ),
									'value'      => 'Rev.',
									'isSelected' => false,
									'price'      => '',
								),
							),
							'isHidden'  => true,
							'inputType' => 'radio',
						),
						array(
							'id'                    => '1.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '1.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '1.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '1.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'formId'                => 6,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'displayOnly'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'layoutGroupId'         => 'b37189d3',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'email',
					'id'                    => 2,
					'label'                 => esc_html__( 'Email', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => array(
						array(
							'id'                    => '2',
							'label'                 => esc_html__( 'Enter Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
						array(
							'id'                    => '2.2',
							'label'                 => esc_html__( 'Confirm Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
					),
					'formId'                => 6,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'emailConfirmEnabled'   => true,
					'fields'                => '',
					'displayOnly'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'layoutGroupId'         => 'ecbfd229',
					'autocompleteAttribute' => 'email',
					'enableAutocomplete'    => true,
				),
				array(
					'type'                  => 'phone',
					'id'                    => 3,
					'label'                 => esc_html__( 'Phone', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'phoneFormat'           => 'standard',
					'formId'                => 6,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'displayOnly'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'layoutGroupId'         => '06e6b70b',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => 'tel',
				),
				array(
					'type'                    => 'address',
					'id'                      => 4,
					'label'                   => esc_html__( 'Address', 'gravityforms' ),
					'adminLabel'              => '',
					'isRequired'              => false,
					'size'                    => 'medium',
					'errorMessage'            => '',
					'visibility'              => 'visible',
					'addressType'             => 'international',
					'inputs'                  => array(
						array(
							'id'                    => '4.1',
							'label'                 => esc_html__( 'Street Address', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line1',
						),
						array(
							'id'                    => '4.2',
							'label'                 => esc_html__( 'Address Line 2', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line2',
						),
						array(
							'id'                    => '4.3',
							'label'                 => esc_html__( 'City', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level2',
						),
						array(
							'id'                    => '4.4',
							'label'                 => esc_html__( 'State / Province', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level1',
						),
						array(
							'id'                    => '4.5',
							'label'                 => esc_html__( 'ZIP / Postal Code', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'postal-code',
						),
						array(
							'id'                    => '4.6',
							'label'                 => esc_html__( 'Country', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'country-name',
						),
					),
					'formId'                  => 6,
					'description'             => '',
					'allowsPrepopulate'       => false,
					'inputMask'               => false,
					'inputMaskValue'          => '',
					'inputMaskIsCustom'       => false,
					'maxLength'               => '',
					'inputType'               => '',
					'labelPlacement'          => '',
					'descriptionPlacement'    => '',
					'subLabelPlacement'       => '',
					'placeholder'             => '',
					'cssClass'                => '',
					'inputName'               => '',
					'noDuplicates'            => false,
					'defaultValue'            => '',
					'choices'                 => '',
					'conditionalLogic'        => '',
					'defaultCountry'          => '',
					'defaultProvince'         => '',
					'productField'            => '',
					'defaultState'            => '',
					'enableCopyValuesOption'  => '',
					'copyValuesOptionDefault' => '',
					'copyValuesOptionLabel'   => '',
					'fields'                  => '',
					'displayOnly'             => '',
					'multipleFiles'           => false,
					'maxFiles'                => '',
					'calculationFormula'      => '',
					'calculationRounding'     => '',
					'enableCalculation'       => '',
					'disableQuantity'         => false,
					'displayAllCategories'    => false,
					'useRichTextEditor'       => false,
					'layoutGroupId'           => '3543a418',
					'enableAutocomplete'      => true,
					'autocompleteAttribute'   => '',
				),
				array(
					'type'                  => 'page',
					'id'                    => 9,
					'label'                 => '',
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'displayOnly'           => true,
					'nextButton'            => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Next', 'gravityforms' ),
						'imageUrl' => '',
					),
					'previousButton'        => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Previous', 'gravityforms' ),
						'imageUrl' => '',
					),
					'formId'                => 6,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'layoutGroupId'         => '5190d1c5',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'section',
					'id'                    => 13,
					'label'                 => esc_html__( 'Event Details', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'displayOnly'           => true,
					'formId'                => 6,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'layoutGroupId'         => 'b3745151',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'select',
					'id'                    => 15,
					'label'                 => esc_html__( 'Gender', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'choices'               => array(
						array(
							'text'       => esc_html__( 'Male', 'gravityforms' ),
							'value'      => 'Male',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Female', 'gravityforms' ),
							'value'      => 'Female',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Non-binary', 'gravityforms' ),
							'value'      => 'Non-binary',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Agender', 'gravityforms' ),
							'value'      => 'Agender',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'My gender isn\'t listed', 'gravityforms' ),
							'value'      => 'My gender isn\'t listed',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Prefer Not to Answer', 'gravityforms' ),
							'value'      => 'Prefer Not to Answer',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'formId'                => 6,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'enablePrice'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
					'layoutGroupId'         => '3318d479',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'select',
					'id'                    => 16,
					'label'                 => esc_html__( 'Age', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'choices'               => array(
						array(
							'text'       => esc_html__( '16-24', 'gravityforms' ),
							'value'      => '16-24',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( '25-34', 'gravityforms' ),
							'value'      => '25-34',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( '35-44', 'gravityforms' ),
							'value'      => '35-44',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( '45-54', 'gravityforms' ),
							'value'      => '45-54',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( '55-64', 'gravityforms' ),
							'value'      => '55-64',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( '65+', 'gravityforms' ),
							'value'      => '65+',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'formId'                => 6,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'enablePrice'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
					'layoutGroupId'         => 'dfa2f0cc',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'select',
					'id'                    => 11,
					'label'                 => esc_html__( 'How did you hear about this event?', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'choices'               => array(
						array(
							'text'       => esc_html__( 'Social Media', 'gravityforms' ),
							'value'      => 'Social Media',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Google', 'gravityforms' ),
							'value'      => 'Google',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Word of Mouth', 'gravityforms' ),
							'value'      => 'Word of Mouth',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Refer a Friend', 'gravityforms' ),
							'value'      => 'Refer a Friend',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Past Participant', 'gravityforms' ),
							'value'      => 'Past Participant',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Other', 'gravityforms' ),
							'value'      => 'Other',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'formId'                => 6,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'enablePrice'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
					'layoutGroupId'         => '7cde1a13',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'page',
					'id'                    => 10,
					'label'                 => '',
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'displayOnly'           => true,
					'nextButton'            => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Next', 'gravityforms' ),
						'imageUrl' => '',
					),
					'previousButton'        => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Previous', 'gravityforms' ),
						'imageUrl' => '',
					),
					'formId'                => 6,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'layoutGroupId'         => '67fbf551',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'section',
					'id'                    => 14,
					'label'                 => esc_html__( 'Payment Details', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'displayOnly'           => true,
					'formId'                => 6,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'layoutGroupId'         => 'a17d8ebf',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'product',
					'id'                    => 5,
					'label'                 => esc_html__( 'Ticket Type', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'large',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'inputType'             => 'radio',
					'enablePrice'           => true,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'choices'               => array(
						array(
							'text'       => esc_html__( 'Early Bird Ticket', 'gravityforms' ),
							'value'      => 'Early Bird Ticket',
							'isSelected' => false,
							'price'      => '$10.00',
						),
						array(
							'text'       => esc_html__( 'Premium Ticket', 'gravityforms' ),
							'value'      => 'Premium Ticket',
							'isSelected' => true,
							'price'      => '$20.00',
						),
						array(
							'text'       => esc_html__( 'VIP Ticket', 'gravityforms' ),
							'value'      => 'VIP Ticket',
							'isSelected' => false,
							'price'      => '$30.00',
						),
					),
					'conditionalLogic'      => '',
					'productField'          => '',
					'layoutGridColumnSpan'  => 12,
					'disableQuantity'       => false,
					'basePrice'             => '$0.00',
					'enableEnhancedUI'      => 0,
					'layoutGroupId'         => '954fb656',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'validateState'         => true,
					'checkboxLabel'         => '',
					'errors'                => array(),
					'enableChoiceValue'     => true,
					'fields'                => '',
				),
				array(
					'type'                  => 'quantity',
					'id'                    => 19,
					'label'                 => esc_html__( 'Number of tickets needed', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'large',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputType'             => 'number',
					'productField'          => 5,
					'numberFormat'          => 'decimal_dot',
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '1',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'inputs'                => null,
					'conditionalLogic'      => '',
					'enableCalculation'     => false,
					'rangeMin'              => '',
					'rangeMax'              => '',
					'calculationFormula'    => '',
					'layoutGridColumnSpan'  => 12,
					'enableEnhancedUI'      => 0,
					'layoutGroupId'         => 'de64842d',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationRounding'   => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'errors'                => array(),
					'checkboxLabel'         => '',
					'enableChoiceValue'     => true,
					'fields'                => '',
				),
				array(
					'type'                  => 'total',
					'id'                    => 7,
					'label'                 => esc_html__( 'Total', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'formId'                => 6,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'displayOnly'           => '',
					'layoutGroupId'         => 'eeb61ba3',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
			),
			'version'                    => '2.7',
			'id'                         => 6,
			'nextFieldId'                => 18,
			'useCurrentUserAsAuthor'     => true,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => array(
				'type'     => 'text',
				'text'     => esc_html__( 'Previous', 'gravityforms' ),
				'imageUrl' => '',
			),
			'pagination'                 => array(
				'type'                                => 'percentage',
				'pages'                               => array(
					'',
					'',
					'',
				),
				'style'                               => 'blue',
				'backgroundColor'                     => null,
				'color'                               => null,
				'display_progressbar_on_confirmation' => false,
				'progressbar_completion_text'         => null,
			),
			'firstPageCssClass'          => '',
			'subLabelPlacement'          => 'above',
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'customRequiredIndicator'    => '',
			'cssClass'                   => '',
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'markupVersion'              => 2,
			'confirmations'              => array(
				'5f0347180ebbb' => array(
					'id'          => '5f0347180ebbb',
					'name'        => 'Default Confirmation',
					'isDefault'   => true,
					'type'        => 'message',
					'message'     => esc_html__( 'Thank you for contacting us! We will get in touch with you shortly.', 'gravityforms' ),
					'url'         => '',
					'pageId'      => '',
					'queryString' => '',
				),
			),
			'notifications'              => array(
				'51794abf1f0d1' => array(
					'id'      => '51794abf1f0d1',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'We have received your registration.', 'gravityforms' ),
					'message' => wp_kses_post( __( '<p>Hi there {Name (First):1.3},</p><p>Thank you for registering for our event. We look forward to seeing you!</p>','gravityforms' ) ),
					'toType'  => 'field',
					'toField' => '2',
					'to' => '2',
					'event'   => 'form_submission',
					'name'    => 'User Notification',
					'type'    => 'user',
				),
				'51794abf1f0d2' => array(
					'id'      => '51794abf1f0d2',
					'from'    => '{admin_email}',
					'subject' => esc_html__( 'New submission from {form_title}', 'gravityforms' ),
					'message' => '{all_fields}',
					'toType'  => 'email',
					'to' => '{admin_email}',
					'event'   => 'form_submission',
					'name'    => 'Admin Notification',
				),
			),
		),
		'version'               => '2.7',
	),
	'gift'                 => array(
		'id'                    => 'gift',
		'description'           => esc_html__( 'Allow your users to purchase a gift certificate', 'gravityforms' ),
		'title'                 => esc_html__( 'Gift Certificate Form', 'gravityforms' ),
		'template_background'   => 'sazerac-yellow',
		'template_thumbnail'    => 'Gift.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/gift-certificate-order-form/',
		'template_access_level' => [ 'pro', 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'title'                      => esc_html__( 'Gift Certificate Order Form', 'gravityforms' ),
			'description'                => esc_html__( 'Purchase a gift certificate today for your nearest and dearest...', 'gravityforms' ),
			'labelPlacement'             => 'top_label',
			'descriptionPlacement'       => 'below',
			'button'                     => array(
				'type'             => 'text',
				'text'             => esc_html__( 'Buy Now', 'gravityforms' ),
				'imageUrl'         => '',
				'conditionalLogic' => null,
			),
			'fields'                     => array(
				array(
					'type'                  => 'name',
					'id'                    => 1,
					'label'                 => esc_html__( 'Your Name', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'nameFormat'            => 'advanced',
					'inputs'                => array(
						array(
							'id'                    => '1.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'  => esc_html__( 'Dr.', 'gravityforms' ),
									'value' => 'Dr.',
								),
								array(
									'text'  => esc_html__( 'Miss', 'gravityforms' ),
									'value' => 'Miss',
								),
								array(
									'text'  => esc_html__( 'Mr.', 'gravityforms' ),
									'value' => 'Mr.',
								),
								array(
									'text'  => esc_html__( 'Mrs.', 'gravityforms' ),
									'value' => 'Mrs.',
								),
								array(
									'text'  => esc_html__( 'Ms.', 'gravityforms' ),
									'value' => 'Ms.',
								),
								array(
									'text'  => esc_html__( 'Prof.', 'gravityforms' ),
									'value' => 'Prof.',
								),
								array(
									'text'  => esc_html__( 'Rev.', 'gravityforms' ),
									'value' => 'Rev.',
								),
							),
							'isHidden'  => true,
							'inputType' => 'radio',
						),
						array(
							'id'                    => '1.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '1.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '1.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '1.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'formId'                => 53,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'displayOnly'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => 'b12360de',
				),
				array(
					'type'                  => 'email',
					'id'                    => 2,
					'label'                 => esc_html__( 'Your Email', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => array(
						array(
							'id'                    => '2',
							'label'                 => esc_html__( 'Enter Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
						array(
							'id'                    => '2.2',
							'label'                 => esc_html__( 'Confirm Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
					),
					'formId'                => 53,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'emailConfirmEnabled'   => true,
					'fields'                => '',
					'displayOnly'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => 'email',
					'layoutGroupId'         => 'c86e9a39',
				),
				array(
					'type'                  => 'select',
					'id'                    => 20,
					'label'                 => esc_html__( 'How would you like the gift certificate delivered?', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'choices'               => array(
						array(
							'text'       => esc_html__( 'Email', 'gravityforms' ),
							'value'      => 'Email',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Mail', 'gravityforms' ),
							'value'      => 'Mail',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'formId'                => 53,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'enablePrice'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '784461fc',
				),
				array(
					'type'                  => 'name',
					'id'                    => 3,
					'label'                 => esc_html__( 'Name of Recipient', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'nameFormat'            => 'advanced',
					'inputs'                => array(
						array(
							'id'        => '3.2',
							'label'     => esc_html__( 'Prefix', 'gravityforms' ),
							'name'      => '',
							'choices'   => array(
								array(
									'text'  => esc_html__( 'Dr.', 'gravityforms' ),
									'value' => 'Dr.',
								),
								array(
									'text'  => esc_html__( 'Miss', 'gravityforms' ),
									'value' => 'Miss',
								),
								array(
									'text'  => esc_html__( 'Mr.', 'gravityforms' ),
									'value' => 'Mr.',
								),
								array(
									'text'  => esc_html__( 'Mrs.', 'gravityforms' ),
									'value' => 'Mrs.',
								),
								array(
									'text'  => esc_html__( 'Ms.', 'gravityforms' ),
									'value' => 'Ms.',
								),
								array(
									'text'  => esc_html__( 'Prof.', 'gravityforms' ),
									'value' => 'Prof.',
								),
								array(
									'text'  => esc_html__( 'Rev.', 'gravityforms' ),
									'value' => 'Rev.',
								),
							),
							'isHidden'  => true,
							'inputType' => 'radio',
						),
						array(
							'id'    => '3.3',
							'label' => esc_html__( 'First', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'       => '3.4',
							'label'    => esc_html__( 'Middle', 'gravityforms' ),
							'name'     => '',
							'isHidden' => true,
						),
						array(
							'id'    => '3.6',
							'label' => esc_html__( 'Last', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'       => '3.8',
							'label'    => esc_html__( 'Suffix', 'gravityforms' ),
							'name'     => '',
							'isHidden' => true,
						),
					),
					'formId'                => 53,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'displayOnly'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => 'c041e5c9',
				),
				array(
					'type'                  => 'email',
					'id'                    => 4,
					'label'                 => esc_html__( 'Email of Recipient', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => array(
						array(
							'id'    => '4',
							'label' => esc_html__( 'Enter Email', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '4.2',
							'label' => esc_html__( 'Confirm Email', 'gravityforms' ),
							'name'  => '',
						),
					),
					'formId'                => 53,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => array(
						'actionType' => 'show',
						'logicType'  => 'all',
						'rules'      => array(
							array(
								'fieldId'  => '20',
								'operator' => 'is',
								'value'    => 'Email',
							),
						),
					),
					'productField'          => '',
					'emailConfirmEnabled'   => true,
					'fields'                => '',
					'displayOnly'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => 'email',
					'layoutGroupId'         => '7f88e4f7',
				),
				array(
					'type'                    => 'address',
					'id'                      => 6,
					'label'                   => esc_html__( 'Address of Recipient', 'gravityforms' ),
					'adminLabel'              => '',
					'isRequired'              => true,
					'size'                    => 'medium',
					'errorMessage'            => '',
					'visibility'              => 'visible',
					'addressType'             => 'international',
					'inputs'                  => array(
						array(
							'id'    => '6.1',
							'label' => esc_html__( 'Street Address', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '6.2',
							'label' => esc_html__( 'Address Line 2', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '6.3',
							'label' => esc_html__( 'City', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '6.4',
							'label' => esc_html__( 'State / Province', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '6.5',
							'label' => esc_html__( 'ZIP / Postal Code', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '6.6',
							'label' => esc_html__( 'Country', 'gravityforms' ),
							'name'  => '',
						),
					),
					'formId'                  => 53,
					'description'             => '',
					'allowsPrepopulate'       => false,
					'inputMask'               => false,
					'inputMaskValue'          => '',
					'inputMaskIsCustom'       => false,
					'maxLength'               => '',
					'inputType'               => '',
					'labelPlacement'          => '',
					'descriptionPlacement'    => '',
					'subLabelPlacement'       => '',
					'placeholder'             => '',
					'cssClass'                => '',
					'inputName'               => '',
					'noDuplicates'            => false,
					'defaultValue'            => '',
					'choices'                 => '',
					'conditionalLogic'        => array(
						'actionType' => 'show',
						'logicType'  => 'all',
						'rules'      => array(
							array(
								'fieldId'  => '20',
								'operator' => 'is',
								'value'    => 'Mail',
							),
						),
					),
					'defaultCountry'          => '',
					'defaultProvince'         => '',
					'copyValuesOptionLabel'   => '',
					'productField'            => '',
					'defaultState'            => '',
					'enableCopyValuesOption'  => '',
					'copyValuesOptionDefault' => '',
					'fields'                  => '',
					'displayOnly'             => '',
					'multipleFiles'           => false,
					'maxFiles'                => '',
					'calculationFormula'      => '',
					'calculationRounding'     => '',
					'enableCalculation'       => '',
					'disableQuantity'         => false,
					'displayAllCategories'    => false,
					'useRichTextEditor'       => false,
					'enableAutocomplete'      => false,
					'autocompleteAttribute'   => '',
					'layoutGroupId'           => 'ab30e486',
				),
				array(
					'type'                  => 'page',
					'id'                    => 10,
					'label'                 => '',
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'displayOnly'           => true,
					'nextButton'            => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Next', 'gravityforms' ),
						'imageUrl' => '',
					),
					'previousButton'        => array(
						'type'     => 'text',
						'text'     => esc_html__( 'Previous', 'gravityforms' ),
						'imageUrl' => '',
					),
					'formId'                => 53,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => 'a0fba5ea',
				),
				array(
					'type'                  => 'textarea',
					'id'                    => 17,
					'label'                 => esc_html__( 'Add a message to your gift certificate...', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'formId'                => 53,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'form_id'               => '',
					'useRichTextEditor'     => false,
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'fields'                => '',
					'displayOnly'           => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => 'dce4663b',
				),
				array(
					'type'                  => 'product',
					'id'                    => 9,
					'label'                 => esc_html__( 'Gift Certificate Amount', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'inputType'             => 'radio',
					'enablePrice'           => true,
					'formId'                => 53,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => array(
						array(
							'text'       => esc_html__( '$30', 'gravityforms' ),
							'value'      => '$30',
							'isSelected' => false,
							'price'      => '$30.00',
						),
						array(
							'text'       => esc_html__( '$50', 'gravityforms' ),
							'value'      => '$50',
							'isSelected' => false,
							'price'      => '$50.00',
						),
						array(
							'text'       => esc_html__( '$100', 'gravityforms' ),
							'value'      => '$100',
							'isSelected' => false,
							'price'      => '$100.00',
						),
					),
					'conditionalLogic'      => '',
					'productField'          => '',
					'basePrice'             => '$0.00',
					'disableQuantity'       => false,
					'fields'                => '',
					'displayOnly'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'checkboxLabel'         => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '8111dc78',
				),
				array(
					'type'                  => 'total',
					'id'                    => 19,
					'label'                 => esc_html__( 'Total', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'formId'                => 53,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'layoutGroupId'         => '4595b07e',
				),
			),
			'version'                    => '2.7',
			'id'                         => 53,
			'nextFieldId'                => 23,
			'useCurrentUserAsAuthor'     => true,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => array(
				'type'     => 'text',
				'text'     => esc_html__( 'Previous', 'gravityforms' ),
				'imageUrl' => '',
			),
			'pagination'                 => array(
				'type'                                => 'percentage',
				'pages'                               => array(
					'',
					'',
				),
				'style'                               => 'blue',
				'backgroundColor'                     => null,
				'color'                               => null,
				'display_progressbar_on_confirmation' => false,
				'progressbar_completion_text'         => null,
			),
			'firstPageCssClass'          => '',
			'subLabelPlacement'          => 'above',
			'cssClass'                   => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'feeds'                      => array(
				'gravityformsadvancedpostcreation' => array(),
			),
			'markupVersion'              => 2,
			'notifications'              => array(
				'5fca0b9442ae4' => array(
					'isActive'          => true,
					'name'              => 'Admin Notification',
					'service'           => 'wordpress',
					'event'             => 'form_submission',
					'to'                => '{admin_email}',
					'toType'            => 'email',
					'cc'                => '',
					'bcc'               => '',
					'subject'           => 'New submission from {form_title}',
					'message'           => '{all_fields}',
					'from'              => '{admin_email}',
					'fromName'          => '',
					'replyTo'           => '',
					'routing'           => null,
					'conditionalLogic'  => null,
					'disableAutoformat' => false,
					'enableAttachments' => false,
					'id'                => '5fca0b9442ae4',
				),
				'5fd20b987c527' => array(
					'isActive'          => true,
					'name'              => 'Purchaser Notification - Email',
					'service'           => 'wordpress',
					'event'             => 'form_submission',
					'to'                => '2',
					'toType'            => 'field',
					'cc'                => '',
					'bcc'               => '',
					'subject'           => 'Thank you for making a purchase!',
					'message'           => wp_kses_post( __( '<p>Dear {Your Name (First):1.3} </p><p>Thank you for making a gift certificate purchase.</p><p>A {Gift Certificate Amount:9} gift certificate will now be emailed to {Name of Recipient (First):3.3} {Name of Recipient (Last):3.6}.</p><p>We will email you a receipt for your purchase shortly.</p>', 'gravityforms' ) ),
					'from'              => '{admin_email}',
					'fromName'          => '',
					'replyTo'           => '',
					'routing'           => array(
						array(),
					),
					'conditionalLogic'  => array(
						'actionType' => 'show',
						'logicType'  => 'all',
						'rules'      => array(
							array(
								'fieldId'  => '20',
								'operator' => 'is',
								'value'    => 'Email',
							),
						),
					),
					'enableAttachments' => false,
					'id'                => '5fd20b987c527',
				),
				'5fd20d2ccc068' => array(
					'isActive'          => true,
					'name'              => 'Recipient Notification',
					'service'           => 'wordpress',
					'event'             => 'form_submission',
					'to'                => '4',
					'toType'            => 'field',
					'cc'                => '',
					'bcc'               => '',
					'subject'           => 'A Gift Certificate from {Your Name (First):1.3} {Your Name (Last):1.6}!',
					'message'           => wp_kses_post( __( '<p>Gift Certificate ID: <strong>000GIFT{entry_id}</strong></p><p>Dear {Name of Recipient (First):3.3},</p><p>We are delighted to send you this gift certificate worth {Gift Certificate Amount:9} from {Your Name (First):1.3} {Your Name (Last):1.6}.</p><p>Here\'s a message from them to you...</p><p><blockquote>{Add a message to your gift certificate...:17}</blockquote></p><p>We look forward to seeing you in-store to redeem your gift certificate!</p>', 'gravityforms' ) ),
					'from'              => '{admin_email}',
					'fromName'          => '',
					'replyTo'           => '',
					'routing'           => array(
						array(),
					),
					'conditionalLogic'  => array(
						'actionType' => 'show',
						'logicType'  => 'all',
						'rules'      => array(
							array(
								'fieldId'  => '20',
								'operator' => 'is',
								'value'    => 'Email',
							),
						),
					),
					'enableAttachments' => false,
					'id'                => '5fd20d2ccc068',
				),
				'5fd370d2d6caf' => array(
					'id'                => '5fd370d2d6caf',
					'name'              => 'Purchaser Notification - Mail',
					'service'           => 'wordpress',
					'event'             => 'form_submission',
					'toType'            => 'field',
					'to'                => '2',
					'from'              => '{admin_email}',
					'subject'           => 'Thank you for making a purchase!',
					'message'           => wp_kses_post( __( '<p>Dear {Your Name (First):1.3} </p><p>Thank you for making a gift certificate purchase.</p><p>A {Gift Certificate Amount:9} gift certificate will now be emailed to {Name of Recipient (First):3.3} {Name of Recipient (Last):3.6}.</p><p>We will email you a receipt for your purchase shortly.</p>', 'gravityforms' ) ),
					'enableAttachments' => false,
					'conditionalLogic'  => array(
						'actionType' => 'show',
						'logicType'  => 'all',
						'rules'      => array(
							array(
								'fieldId'  => '20',
								'operator' => 'is',
								'value'    => 'Mail',
							),
						),
					),
					'isActive'          => true,
				),
			),
			'confirmations'              => array(
				'5fca0b9442eca' => array(
					'id'                => '5fca0b9442eca',
					'name'              => 'Default Confirmation',
					'isDefault'         => true,
					'type'              => 'message',
					'message'           => esc_html__( 'Thank you for making a gift certificate purchase! You should receive an email from us shortly with more information.', 'gravityforms' ),
					'url'               => '',
					'pageId'            => 0,
					'queryString'       => '',
					'disableAutoformat' => false,
					'conditionalLogic'  => array(),
				),
			),
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'customRequiredIndicator'    => '(Required)',
		),
		'version'               => '2.7',
	),
	'newsletter'           => array(
		'id'                    => 'newsletter',
		'description'           => esc_html__( 'Let users sign up to your newsletter', 'gravityforms' ),
		'title'                 => esc_html__( 'Newsletter Signup Form', 'gravityforms' ),
		'template_background'   => 'panache-green',
		'template_thumbnail'    => 'Newsletter.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/newsletter-signup-form/',
		'template_access_level' => [ 'basic', 'multi', 'ltmulti', 'pro', 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'title'                      => esc_html__( 'Form Template Library: Newsletter Signup Form', 'gravityforms' ),
			'description'                => esc_html__( 'If you want to keep up to date with what\'s happening on the blog, sign up for our newsletter!', 'gravityforms' ),
			'labelPlacement'             => 'top_label',
			'descriptionPlacement'       => 'below',
			'button'                     => array(
				'type'     => 'text',
				'text'     => esc_html__( 'Keep me up to date!', 'gravityforms' ),
				'imageUrl' => '',
			),
			'fields'                     => array(
				array(
					'type'                 => 'name',
					'id'                   => 2,
					'label'                => esc_html__( 'Name', 'gravityforms' ),
					'adminLabel'           => '',
					'isRequired'           => true,
					'size'                 => 'medium',
					'errorMessage'         => '',
					'visibility'           => 'visible',
					'nameFormat'           => 'advanced',
					'inputs'               => array(
						array(
							'id'                    => '2.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'  => esc_html__( 'Dr.', 'gravityforms' ),
									'value' => 'Dr.',
								),
								array(
									'text'  => esc_html__( 'Miss', 'gravityforms' ),
									'value' => 'Miss',
								),
								array(
									'text'  => esc_html__( 'Mr.', 'gravityforms' ),
									'value' => 'Mr.',
								),
								array(
									'text'  => esc_html__( 'Mrs.', 'gravityforms' ),
									'value' => 'Mrs.',
								),
								array(
									'text'  => esc_html__( 'Ms.', 'gravityforms' ),
									'value' => 'Ms.',
								),
								array(
									'text'  => esc_html__( 'Prof.', 'gravityforms' ),
									'value' => 'Prof.',
								),
								array(
									'text'  => esc_html__( 'Rev.', 'gravityforms' ),
									'value' => 'Rev.',
								),
							),
							'isHidden'  => true,
							'inputType' => 'radio',
						),
						array(
							'id'                    => '2.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '2.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '2.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '2.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'formId'               => '98',
					'description'          => '',
					'allowsPrepopulate'    => false,
					'inputMask'            => false,
					'inputMaskValue'       => '',
					'inputMaskIsCustom'    => false,
					'maxLength'            => '',
					'inputType'            => '',
					'labelPlacement'       => '',
					'descriptionPlacement' => '',
					'subLabelPlacement'    => '',
					'placeholder'          => '',
					'cssClass'             => '',
					'inputName'            => '',
					'noDuplicates'         => false,
					'defaultValue'         => '',
					'choices'              => '',
					'conditionalLogic'     => '',
					'productField'         => '',
					'multipleFiles'        => false,
					'maxFiles'             => '',
					'calculationFormula'   => '',
					'calculationRounding'  => '',
					'enableCalculation'    => '',
					'disableQuantity'      => false,
					'displayAllCategories' => false,
					'useRichTextEditor'    => false,
					'fields'               => '',
					'displayOnly'          => '',
					'enableAutocomplete'   => true,
				),
				array(
					'type'                 => 'email',
					'id'                   => 1,
					'label'                => esc_html__( 'Email', 'gravityforms' ),
					'adminLabel'           => '',
					'isRequired'           => true,
					'size'                 => 'medium',
					'errorMessage'         => '',
					'visibility'           => 'visible',
					'inputs'               => array(
						array(
							'id'                    => '1',
							'label'                 => esc_html__( 'Enter Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
						array(
							'id'                    => '1.2',
							'label'                 => esc_html__( 'Confirm Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
					),
					'formId'               => '98',
					'description'          => '',
					'allowsPrepopulate'    => false,
					'inputMask'            => false,
					'inputMaskValue'       => '',
					'inputMaskIsCustom'    => false,
					'maxLength'            => '',
					'inputType'            => '',
					'labelPlacement'       => 'hidden_label',
					'descriptionPlacement' => '',
					'subLabelPlacement'    => '',
					'placeholder'          => '',
					'cssClass'             => '',
					'inputName'            => '',
					'noDuplicates'         => false,
					'defaultValue'         => '',
					'choices'              => '',
					'conditionalLogic'     => '',
					'productField'         => '',
					'emailConfirmEnabled'  => true,
					'multipleFiles'        => false,
					'maxFiles'             => '',
					'calculationFormula'   => '',
					'calculationRounding'  => '',
					'enableCalculation'    => '',
					'disableQuantity'      => false,
					'displayAllCategories' => false,
					'useRichTextEditor'    => false,
					'fields'               => '',
					'displayOnly'          => '',
					'checkboxLabel'        => '',
					'enableAutocomplete'   => true,
				),
				array(
					'type'                 => 'checkbox',
					'id'                   => 3,
					'label'                => esc_html__( 'Privacy', 'gravityforms' ),
					'adminLabel'           => '',
					'isRequired'           => true,
					'size'                 => 'medium',
					'errorMessage'         => '',
					'visibility'           => 'visible',
					'choices'              => array(
						array(
							'text'       => wp_kses_post( __( 'I agree with the storage and handling of my data by this website. - <a target="_blank" href="#" rel="noopener noreferrer">Privacy Policy</a> <abbr class="wpgdprc-required" title="You need to accept this checkbox.">*</abbr>', 'gravityforms' ) ),
							'value'      => wp_kses_post( __( 'I agree with the storage and handling of my data by this website.', 'gravityforms' ) ),
							'isSelected' => false,
							'price'      => '',
						),
					),
					'inputs'               => array(
						array(
							'id'    => '3.1',
							'label' => wp_kses_post( __( 'I agree with the storage and handling of my data by this website. - <a target="_blank" href="#" rel="noopener noreferrer">Privacy  Policy</a> <abbr class="wpgdprc-required" title="You need to accept this checkbox.">*</abbr>', 'gravityforms' ) ),
							'name'  => '',
						),
					),
					'formId'               => '98',
					'description'          => '',
					'allowsPrepopulate'    => false,
					'inputMask'            => false,
					'inputMaskValue'       => '',
					'inputMaskIsCustom'    => false,
					'maxLength'            => '',
					'inputType'            => '',
					'labelPlacement'       => '',
					'descriptionPlacement' => '',
					'subLabelPlacement'    => '',
					'placeholder'          => '',
					'cssClass'             => '',
					'inputName'            => '',
					'noDuplicates'         => false,
					'defaultValue'         => '',
					'conditionalLogic'     => '',
					'productField'         => '',
					'enableSelectAll'      => '',
					'enablePrice'          => '',
					'multipleFiles'        => false,
					'maxFiles'             => '',
					'calculationFormula'   => '',
					'calculationRounding'  => '',
					'enableCalculation'    => '',
					'disableQuantity'      => false,
					'displayAllCategories' => false,
					'useRichTextEditor'    => false,
					'fields'               => '',
					'displayOnly'          => '',
				),
			),
			'version'                    => '2.7',
			'id'                         => '98',
			'nextFieldId'                => 5,
			'useCurrentUserAsAuthor'     => true,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => null,
			'pagination'                 => null,
			'firstPageCssClass'          => null,
			'subLabelPlacement'          => 'above',
			'cssClass'                   => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'is_active'                  => '1',
			'date_created'               => '2020-07-30 15:44:18',
			'is_trash'                   => '0',
			'delete_entry'               => '',
			'delete_entry_condition'     => '',
			'delete_entry_period'        => '',
			'delete_entry_units'         => 'hour',
			'form_slug'                  => 'newsletter-signup-form',
			'confirmations'              => array(
				'5f10e6359b4a7' => array(
					'id'                => '5f10e6359b4a7',
					'name'              => 'Default Confirmation',
					'isDefault'         => true,
					'type'              => 'message',
					'message'           => esc_html__( 'Thank you for signing up. Be on the lookout for our monthly newsletter!', 'gravityforms' ),
					'url'               => '',
					'pageId'            => 0,
					'queryString'       => '',
					'disableAutoformat' => false,
					'conditionalLogic'  => array(),
				),
			),
			'notifications'              => array(
				'5f10e6359b253' => array(
					'id'       => '5f10e6359b253',
					'to'       => '{admin_email}',
					'name'     => 'Admin Notification',
					'event'    => 'form_submission',
					'toType'   => 'email',
					'subject'  => 'New submission from {form_title}',
					'message'  => '{all_fields}',
				),
			),
		),
		'version'               => '2.7',
	),
	'quote'                => array(
		'id'                    => 'quote',
		'description'           => esc_html__( 'Helps users ask for a quote for a certain service or product you are selling on your website', 'gravityforms' ),
		'title'                 => esc_html__( 'Request a Quote Form', 'gravityforms' ),
		'template_background'   => 'hawkes-blue',
		'template_thumbnail'    => 'Quote.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/request-a-quote/',
		'template_access_level' => [ 'basic', 'multi', 'ltmulti', 'pro', 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'title'                      => esc_html__( 'Request a Quote Form', 'gravityforms' ),
			'description'                => esc_html__( 'Please fill out the information below and we will be in touch shortly with your personalized quote.', 'gravityforms' ),
			'labelPlacement'             => 'top_label',
			'descriptionPlacement'       => 'below',
			'button'                     => array(
				'type'             => 'text',
				'text'             => esc_html__( 'Submit', 'gravityforms' ),
				'imageUrl'         => '',
				'conditionalLogic' => null,
			),
			'fields'                     => array(
				array(
					'type'                  => 'name',
					'id'                    => 1,
					'label'                 => esc_html__( 'Name', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'nameFormat'            => 'advanced',
					'inputs'                => array(
						array(
							'id'                    => '1.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'       => esc_html__( 'Mr.', 'gravityforms' ),
									'value'      => 'Mr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Mrs.', 'gravityforms' ),
									'value'      => 'Mrs.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Miss', 'gravityforms' ),
									'value'      => 'Miss',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Ms.', 'gravityforms' ),
									'value'      => 'Ms.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Dr.', 'gravityforms' ),
									'value'      => 'Dr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Prof.', 'gravityforms' ),
									'value'      => 'Prof.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Rev.', 'gravityforms' ),
									'value'      => 'Rev.',
									'isSelected' => false,
									'price'      => '',
								),
							),
							'isHidden'  => true,
							'inputType' => 'radio',
						),
						array(
							'id'                    => '1.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '1.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '1.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '1.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'formId'                => 2,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
					'layoutGroupId'         => 'd2b49ca9',
					'checkboxLabel'         => '',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'email',
					'id'                    => 2,
					'label'                 => esc_html__( 'Email', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => array(
						array(
							'id'                    => '2',
							'label'                 => esc_html__( 'Enter Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
						array(
							'id'                    => '2.2',
							'label'                 => esc_html__( 'Confirm Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
					),
					'formId'                => 2,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'emailConfirmEnabled'   => true,
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
					'layoutGroupId'         => 'dddffc54',
					'autocompleteAttribute' => 'email',
					'enableAutocomplete'    => true,
				),
				array(
					'type'                    => 'address',
					'id'                      => 3,
					'label'                   => esc_html__( 'Address', 'gravityforms' ),
					'adminLabel'              => '',
					'isRequired'              => true,
					'size'                    => 'medium',
					'errorMessage'            => '',
					'visibility'              => 'visible',
					'addressType'             => 'international',
					'inputs'                  => array(
						array(
							'id'                    => '3.1',
							'label'                 => esc_html__( 'Street Address', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line1',
						),
						array(
							'id'                    => '3.2',
							'label'                 => esc_html__( 'Address Line 2', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-line2',
						),
						array(
							'id'                    => '3.3',
							'label'                 => esc_html__( 'City', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'address-level2',
						),
						array(
							'id'                    => '3.4',
							'label'                 => esc_html__( 'State / Province', 'gravityforms' ),
							'name'                  => '',
           				 	'autocompleteAttribute' => 'address-level1',
						),
						array(
							'id'                    => '3.5',
							'label'                 => esc_html__( 'ZIP / Postal Code', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'postal-code',
						),
						array(
							'id'                    => '3.6',
							'label'                 => esc_html__( 'Country', 'gravityforms' ),
							'name'                  => '',
							'isHidden'              => true,
							'autocompleteAttribute' => 'country-name',
						),
					),
					'formId'                  => 2,
					'description'             => '',
					'allowsPrepopulate'       => false,
					'inputMask'               => false,
					'inputMaskValue'          => '',
					'inputMaskIsCustom'       => false,
					'maxLength'               => '',
					'inputType'               => '',
					'labelPlacement'          => '',
					'descriptionPlacement'    => '',
					'subLabelPlacement'       => '',
					'placeholder'             => '',
					'cssClass'                => '',
					'inputName'               => '',
					'noDuplicates'            => false,
					'defaultValue'            => '',
					'choices'                 => '',
					'conditionalLogic'        => '',
					'defaultCountry'          => '',
					'defaultProvince'         => '',
					'productField'            => '',
					'defaultState'            => '',
					'enableCopyValuesOption'  => '',
					'copyValuesOptionDefault' => '',
					'copyValuesOptionLabel'   => '',
					'multipleFiles'           => false,
					'maxFiles'                => '',
					'calculationFormula'      => '',
					'calculationRounding'     => '',
					'enableCalculation'       => '',
					'disableQuantity'         => false,
					'displayAllCategories'    => false,
					'useRichTextEditor'       => false,
					'fields'                  => '',
					'displayOnly'             => '',
					'layoutGroupId'           => 'bf94b059',
					'enableAutocomplete'      => true,
					'autocompleteAttribute'   => '',
				),
				array(
					'type'                  => 'checkbox',
					'id'                    => 4,
					'label'                 => esc_html__( 'Please select the service/s you require...', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'choices'               => array(
						array(
							'text'       => esc_html__( 'Landscape Gardening', 'gravityforms' ),
							'value'      => 'Landscape Gardening',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Ground Maintenance', 'gravityforms' ),
							'value'      => 'Ground Maintenance',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Tree Surgery Services', 'gravityforms' ),
							'value'      => 'Tree Surgery Services',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Fencing', 'gravityforms' ),
							'value'      => 'Fencing',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Clearance', 'gravityforms' ),
							'value'      => 'Clearance',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'inputs'                => array(
						array(
							'id'    => '4.1',
							'label' => esc_html__( 'Landscape Gardening', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '4.2',
							'label' => esc_html__( 'Ground Maintenance', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '4.3',
							'label' => esc_html__( 'Tree Surgery Services', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '4.4',
							'label' => esc_html__( 'Fencing', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '4.5',
							'label' => esc_html__( 'Clearance', 'gravityforms' ),
							'name'  => '',
						),
					),
					'formId'                => 2,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'enableSelectAll'       => '',
					'enablePrice'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
					'layoutGroupId'         => '0d3d0e60',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'textarea',
					'id'                    => 6,
					'label'                 => esc_html__( 'How can we help you?', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'formId'                => 2,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'form_id'               => '',
					'useRichTextEditor'     => false,
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'fields'                => '',
					'displayOnly'           => '',
					'layoutGroupId'         => '2dabd260',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
			),
			'version'                    => '2.7',
			'id'                         => 2,
			'nextFieldId'                => 9,
			'useCurrentUserAsAuthor'     => true,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => null,
			'pagination'                 => null,
			'firstPageCssClass'          => null,
			'feeds'                      => array(
				'gravityformsadvancedpostcreation' => array(),
			),
			'subLabelPlacement'          => 'above',
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'customRequiredIndicator'    => '',
			'cssClass'                   => '',
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'markupVersion'              => 2,
			'confirmations'              => array(
				'5efca585e0837' => array(
					'id'          => '5efca585e0837',
					'name'        => 'Default Confirmation',
					'isDefault'   => true,
					'type'        => 'message',
					'message'     => esc_html__( 'Thank you for contacting us! We will get in touch with you shortly.', 'gravityforms' ),
					'url'         => '',
					'pageId'      => '',
					'queryString' => '',
				),
			),
			'notifications'              => array(
				'5efca585ddb6b' => array(
					'id'       => '5efca585ddb6b',
					'isActive' => true,
					'to'       => '{admin_email}',
					'name'     => 'Admin Notification',
					'event'    => 'form_submission',
					'toType'   => 'email',
					'subject'  => 'New submission from {form_title}',
					'message'  => '{all_fields}',
				),
			),
		),
		'version'               => '2.7',
	),
	'survey'               => array(
		'id'                    => 'survey',
		'description'           => esc_html__( 'Get feedback about your product using a survey form', 'gravityforms' ),
		'title'                 => esc_html__( 'Survey Form', 'gravityforms' ),
		'template_background'   => 'iceberg-blue',
		'template_thumbnail'    => 'Survey.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/survey/',
		'template_access_level' => [ 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'title'                      => esc_html__( 'Survey Form', 'gravityforms' ),
			'description'                => esc_html__( 'Tell us what you think about Acme Products...', 'gravityforms' ),
			'labelPlacement'             => 'top_label',
			'descriptionPlacement'       => 'below',
			'button'                     => array(
				'type'             => 'text',
				'text'             => esc_html__( 'Submit', 'gravityforms' ),
				'imageUrl'         => '',
				'conditionalLogic' => null,
			),
			'fields'                     => array(
				array(
					'type'                  => 'name',
					'id'                    => 1,
					'label'                 => esc_html__( 'Name', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'nameFormat'            => 'advanced',
					'inputs'                => array(
						array(
							'id'                    => '1.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'       => esc_html__( 'Mr.', 'gravityforms' ),
									'value'      => 'Mr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Mrs.', 'gravityforms' ),
									'value'      => 'Mrs.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Miss', 'gravityforms' ),
									'value'      => 'Miss',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Ms.', 'gravityforms' ),
									'value'      => 'Ms.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Dr.', 'gravityforms' ),
									'value'      => 'Dr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Prof.', 'gravityforms' ),
									'value'      => 'Prof.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Rev.', 'gravityforms' ),
									'value'      => 'Rev.',
									'isSelected' => false,
									'price'      => '',
								),
							),
							'isHidden'  => true,
							'inputType' => 'radio',
						),
						array(
							'id'                    => '1.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '1.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '1.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '1.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'formId'                => 7,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'displayOnly'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'layoutGroupId'         => '27ab4159',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'email',
					'id'                    => 2,
					'label'                 => esc_html__( 'Email', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'formId'                => 7,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'emailConfirmEnabled'   => '',
					'fields'                => '',
					'displayOnly'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'layoutGroupId'         => '50f65e12',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => 'email',
				),
				array(
					'type'                  => 'text',
					'id'                    => 3,
					'label'                 => esc_html__( 'Company Name', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'formId'                => 7,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'enablePasswordInput'   => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
					'layoutGroupId'         => 'e55e9746',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'website',
					'id'                    => 4,
					'label'                 => esc_html__( 'Website', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => true,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'placeholder'           => '',
					'formId'                => 7,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'fields'                => '',
					'displayOnly'           => '',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'layoutGroupId'         => '27267a07',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                            => 'survey',
					'id'                              => 7,
					'label'                           => esc_html__( 'Rate Acme Products', 'gravityforms' ),
					'adminLabel'                      => '',
					'isRequired'                      => true,
					'size'                            => 'medium',
					'errorMessage'                    => '',
					'visibility'                      => 'visible',
					'inputs'                          => null,
					'enableChoiceValue'               => true,
					'enablePrice'                     => false,
					'gsurveyLikertEnableMultipleRows' => false,
					'gsurveyLikertEnableScoring'      => false,
					'choices'                         => array(
						array(
							'text'       => esc_html__( 'Terrible', 'gravityforms' ),
							'value'      => 'grating7c1f31b7f',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Not so great', 'gravityforms' ),
							'value'      => 'grating73526aca2',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Neutral', 'gravityforms' ),
							'value'      => 'grating7fe735525',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Pretty good', 'gravityforms' ),
							'value'      => 'grating7187ad1ac',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Excellent', 'gravityforms' ),
							'value'      => 'grating70ac81dbc',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'gsurveyLikertRows'               => array(
						array(
							'text'  => esc_html__( 'First row', 'gravityforms' ),
							'value' => 'glikertrow25430902',
						),
						array(
							'text'  => esc_html__( 'Second row', 'gravityforms' ),
							'value' => 'glikertrowd8185e64',
						),
						array(
							'text'  => esc_html__( 'Third row', 'gravityforms' ),
							'value' => 'glikertrowf31a102d',
						),
						array(
							'text'  => esc_html__( 'Fourth row', 'gravityforms' ),
							'value' => 'glikertrow29b56bc8',
						),
						array(
							'text'  => esc_html__( 'Fifth row', 'gravityforms' ),
							'value' => 'glikertrowdd659284',
						),
					),
					'inputType'                       => 'rating',
					'formId'                          => 7,
					'description'                     => '',
					'allowsPrepopulate'               => false,
					'inputMask'                       => false,
					'inputMaskValue'                  => '',
					'inputMaskIsCustom'               => false,
					'maxLength'                       => '',
					'labelPlacement'                  => '',
					'descriptionPlacement'            => '',
					'subLabelPlacement'               => '',
					'placeholder'                     => '',
					'cssClass'                        => '',
					'inputName'                       => '',
					'noDuplicates'                    => false,
					'defaultValue'                    => '',
					'conditionalLogic'                => '',
					'productField'                    => '',
					'multipleFiles'                   => false,
					'maxFiles'                        => '',
					'calculationFormula'              => '',
					'calculationRounding'             => '',
					'enableCalculation'               => '',
					'disableQuantity'                 => false,
					'displayAllCategories'            => false,
					'useRichTextEditor'               => false,
					'reversed'                        => true,
					'checkboxLabel'                   => '',
					'fields'                          => '',
					'displayOnly'                     => '',
					'layoutGroupId'                   => '5fc1e58e',
					'autocompleteAttribute'           => '',
					'enableAutocomplete'              => false,
				),
				array(
					'type'                            => 'survey',
					'id'                              => 5,
					'label'                           => esc_html__( 'How many sites do you have Acme Products installed on?', 'gravityforms' ),
					'adminLabel'                      => '',
					'isRequired'                      => true,
					'size'                            => 'medium',
					'errorMessage'                    => '',
					'visibility'                      => 'visible',
					'inputs'                          => null,
					'enableChoiceValue'               => true,
					'enablePrice'                     => false,
					'gsurveyLikertEnableMultipleRows' => false,
					'gsurveyLikertEnableScoring'      => false,
					'choices'                         => array(
						array(
							'text'       => esc_html__( 'Just one', 'gravityforms' ),
							'value'      => 'gsurvey5f6568110',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( '2-5', 'gravityforms' ),
							'value'      => 'gsurvey5876710ad',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( '6-10', 'gravityforms' ),
							'value'      => 'gsurvey564e81af0',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( '11-25', 'gravityforms' ),
							'value'      => 'gsurvey56e8d990b',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( '26-100', 'gravityforms' ),
							'value'      => 'gsurvey51c58fece',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( '100+', 'gravityforms' ),
							'value'      => 'gsurvey56e88e6d2',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'gsurveyLikertRows'               => array(
						array(
							'text'  => esc_html__( 'First row', 'gravityforms' ),
							'value' => 'glikertrow31fe74e6',
						),
						array(
							'text'  => esc_html__( 'Second row', 'gravityforms' ),
							'value' => 'glikertroweb77703e',
						),
						array(
							'text'  => esc_html__( 'Third row', 'gravityforms' ),
							'value' => 'glikertrowdcba0197',
						),
						array(
							'text'  => esc_html__( 'Fourth row', 'gravityforms' ),
							'value' => 'glikertrow0a0f5fea',
						),
						array(
							'text'  => esc_html__( 'Fifth row', 'gravityforms' ),
							'value' => 'glikertrow846db0c5',
						),
					),
					'inputType'                       => 'select',
					'formId'                          => 7,
					'description'                     => '',
					'allowsPrepopulate'               => false,
					'inputMask'                       => false,
					'inputMaskValue'                  => '',
					'inputMaskIsCustom'               => false,
					'maxLength'                       => '',
					'labelPlacement'                  => '',
					'descriptionPlacement'            => '',
					'subLabelPlacement'               => '',
					'placeholder'                     => '',
					'cssClass'                        => '',
					'inputName'                       => '',
					'noDuplicates'                    => false,
					'defaultValue'                    => '',
					'conditionalLogic'                => '',
					'productField'                    => '',
					'multipleFiles'                   => false,
					'maxFiles'                        => '',
					'calculationFormula'              => '',
					'calculationRounding'             => '',
					'enableCalculation'               => '',
					'disableQuantity'                 => false,
					'displayAllCategories'            => false,
					'useRichTextEditor'               => false,
					'checkboxLabel'                   => '',
					'fields'                          => '',
					'displayOnly'                     => '',
					'layoutGroupId'                   => '36887513',
					'autocompleteAttribute'           => '',
					'errors'                          => array(),
					'enableAutocomplete'              => false,
				),
				array(
					'type'                            => 'survey',
					'id'                              => 6,
					'label'                           => esc_html__( 'Do you use Acme Products to sell products or services?', 'gravityforms' ),
					'adminLabel'                      => '',
					'isRequired'                      => true,
					'size'                            => 'medium',
					'errorMessage'                    => '',
					'visibility'                      => 'visible',
					'inputs'                          => null,
					'enableChoiceValue'               => true,
					'enablePrice'                     => false,
					'gsurveyLikertEnableMultipleRows' => false,
					'gsurveyLikertEnableScoring'      => false,
					'choices'                         => array(
						array(
							'text'       => esc_html__( 'Yes', 'gravityforms' ),
							'value'      => 'gsurvey6f36a3ffe',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'No', 'gravityforms' ),
							'value'      => 'gsurvey68ab45f58',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'gsurveyLikertRows'               => array(
						array(
							'text'  => esc_html__( 'First row', 'gravityforms' ),
							'value' => 'glikertrowdbe373af',
						),
						array(
							'text'  => esc_html__( 'Second row', 'gravityforms' ),
							'value' => 'glikertrow8cc489f6',
						),
						array(
							'text'  => esc_html__( 'Third row', 'gravityforms' ),
							'value' => 'glikertrow3c03e0c3',
						),
						array(
							'text'  => esc_html__( 'Fourth row', 'gravityforms' ),
							'value' => 'glikertrowad5218c6',
						),
						array(
							'text'  => esc_html__( 'Fifth row', 'gravityforms' ),
							'value' => 'glikertrowd5232ade',
						),
					),
					'inputType'                       => 'radio',
					'formId'                          => 7,
					'description'                     => '',
					'allowsPrepopulate'               => false,
					'inputMask'                       => false,
					'inputMaskValue'                  => '',
					'inputMaskIsCustom'               => false,
					'maxLength'                       => '',
					'labelPlacement'                  => '',
					'descriptionPlacement'            => '',
					'subLabelPlacement'               => '',
					'placeholder'                     => '',
					'cssClass'                        => '',
					'inputName'                       => '',
					'noDuplicates'                    => false,
					'defaultValue'                    => '',
					'conditionalLogic'                => '',
					'productField'                    => '',
					'multipleFiles'                   => false,
					'maxFiles'                        => '',
					'calculationFormula'              => '',
					'calculationRounding'             => '',
					'enableCalculation'               => '',
					'disableQuantity'                 => false,
					'displayAllCategories'            => false,
					'useRichTextEditor'               => false,
					'checkboxLabel'                   => '',
					'fields'                          => '',
					'displayOnly'                     => '',
					'layoutGroupId'                   => 'a97c50b7',
					'autocompleteAttribute'           => '',
					'errors'                          => array(),
					'enableAutocomplete'              => false,
				),
				array(
					'type'                            => 'survey',
					'id'                              => 9,
					'label'                           => esc_html__( 'What types of forms have you created with Acme Products?', 'gravityforms' ),
					'adminLabel'                      => '',
					'isRequired'                      => true,
					'size'                            => 'medium',
					'errorMessage'                    => '',
					'visibility'                      => 'visible',
					'inputs'                          => array(
						array(
							'id'    => '9.1',
							'label' => esc_html__( 'Payment Form', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '9.2',
							'label' => esc_html__( 'Survey Form', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '9.3',
							'label' => esc_html__( 'Donation Form', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '9.4',
							'label' => esc_html__( 'Contact Form', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '9.5',
							'label' => esc_html__( 'Request a Quote Form', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '9.6',
							'label' => esc_html__( 'Event Registration Form', 'gravityforms' ),
							'name'  => '',
						),
					),
					'enableChoiceValue'               => true,
					'enablePrice'                     => false,
					'gsurveyLikertEnableMultipleRows' => false,
					'gsurveyLikertEnableScoring'      => false,
					'choices'                         => array(
						array(
							'text'       => esc_html__( 'Payment Form', 'gravityforms' ),
							'value'      => 'gsurvey987fb8f52',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Survey Form', 'gravityforms' ),
							'value'      => 'gsurvey9671d0151',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Donation Form', 'gravityforms' ),
							'value'      => 'gsurvey98963f804',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Contact Form', 'gravityforms' ),
							'value'      => 'gsurvey9f51f117f',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Request a Quote Form', 'gravityforms' ),
							'value'      => 'gsurvey915ff13f4',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Event Registration Form', 'gravityforms' ),
							'value'      => 'gsurvey995015cfc',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'gsurveyLikertRows'               => array(
						array(
							'text'  => esc_html__( 'First row', 'gravityforms' ),
							'value' => 'glikertrow8003872d',
						),
						array(
							'text'  => esc_html__( 'Second row', 'gravityforms' ),
							'value' => 'glikertrowa7a803de',
						),
						array(
							'text'  => esc_html__( 'Third row', 'gravityforms' ),
							'value' => 'glikertrowbbc2a0a7',
						),
						array(
							'text'  => esc_html__( 'Fourth row', 'gravityforms' ),
							'value' => 'glikertrow4f216e59',
						),
						array(
							'text'  => esc_html__( 'Fifth row', 'gravityforms' ),
							'value' => 'glikertrow025aaece',
						),
					),
					'inputType'                       => 'checkbox',
					'formId'                          => 7,
					'description'                     => '',
					'allowsPrepopulate'               => false,
					'inputMask'                       => false,
					'inputMaskValue'                  => '',
					'inputMaskIsCustom'               => false,
					'maxLength'                       => '',
					'labelPlacement'                  => '',
					'descriptionPlacement'            => '',
					'subLabelPlacement'               => '',
					'placeholder'                     => '',
					'cssClass'                        => '',
					'inputName'                       => '',
					'noDuplicates'                    => false,
					'defaultValue'                    => '',
					'conditionalLogic'                => '',
					'productField'                    => '',
					'multipleFiles'                   => false,
					'maxFiles'                        => '',
					'calculationFormula'              => '',
					'calculationRounding'             => '',
					'enableCalculation'               => '',
					'disableQuantity'                 => false,
					'displayAllCategories'            => false,
					'useRichTextEditor'               => false,
					'checkboxLabel'                   => '',
					'fields'                          => '',
					'displayOnly'                     => '',
					'layoutGroupId'                   => '558734ad',
					'errors'                          => array(),
					'enableAutocomplete'              => false,
					'autocompleteAttribute'           => '',
				),
				array(
					'type'                            => 'survey',
					'id'                              => 8,
					'label'                           => esc_html__( 'Rank these add-ons based on how useful they are to you.', 'gravityforms' ),
					'adminLabel'                      => '',
					'isRequired'                      => false,
					'size'                            => 'medium',
					'errorMessage'                    => '',
					'visibility'                      => 'visible',
					'inputs'                          => null,
					'enableChoiceValue'               => true,
					'enablePrice'                     => false,
					'gsurveyLikertEnableMultipleRows' => false,
					'gsurveyLikertEnableScoring'      => false,
					'choices'                         => array(
						array(
							'text'       => esc_html__( 'Stripe', 'gravityforms' ),
							'value'      => 'grank8f9ccc084',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'MailChimp', 'gravityforms' ),
							'value'      => 'grank8808bc416',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Zapier', 'gravityforms' ),
							'value'      => 'grank8910790bd',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Surveys', 'gravityforms' ),
							'value'      => 'grank87a265679',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Dropbox', 'gravityforms' ),
							'value'      => 'grank82f1f0789',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'gsurveyLikertRows'               => array(
						array(
							'text'  => esc_html__( 'First row', 'gravityforms' ),
							'value' => 'glikertrow1e01c85b',
						),
						array(
							'text'  => esc_html__( 'Second row', 'gravityforms' ),
							'value' => 'glikertrowcefb897f',
						),
						array(
							'text'  => esc_html__( 'Third row', 'gravityforms' ),
							'value' => 'glikertrow4bdfd436',
						),
						array(
							'text'  => esc_html__( 'Fourth row', 'gravityforms' ),
							'value' => 'glikertrowe22751d7',
						),
						array(
							'text'  => esc_html__( 'Fifth row', 'gravityforms' ),
							'value' => 'glikertrow80c716b2',
						),
					),
					'inputType'                       => 'rank',
					'formId'                          => 7,
					'description'                     => '',
					'allowsPrepopulate'               => false,
					'inputMask'                       => false,
					'inputMaskValue'                  => '',
					'inputMaskIsCustom'               => false,
					'maxLength'                       => '',
					'labelPlacement'                  => '',
					'descriptionPlacement'            => '',
					'subLabelPlacement'               => '',
					'placeholder'                     => '',
					'cssClass'                        => '',
					'inputName'                       => '',
					'noDuplicates'                    => false,
					'defaultValue'                    => '',
					'conditionalLogic'                => '',
					'productField'                    => '',
					'multipleFiles'                   => false,
					'maxFiles'                        => '',
					'calculationFormula'              => '',
					'calculationRounding'             => '',
					'enableCalculation'               => '',
					'disableQuantity'                 => false,
					'displayAllCategories'            => false,
					'useRichTextEditor'               => false,
					'checkboxLabel'                   => '',
					'fields'                          => '',
					'displayOnly'                     => '',
					'layoutGroupId'                   => '4dd92cf2',
					'enableAutocomplete'              => false,
					'autocompleteAttribute'           => '',
				),
				array(
					'type'                            => 'survey',
					'id'                              => 11,
					'label'                           => esc_html__( 'Acme Products fulfils all my form needs', 'gravityforms' ),
					'adminLabel'                      => '',
					'isRequired'                      => true,
					'size'                            => 'medium',
					'errorMessage'                    => '',
					'visibility'                      => 'visible',
					'inputs'                          => null,
					'enableChoiceValue'               => true,
					'enablePrice'                     => false,
					'gsurveyLikertEnableMultipleRows' => false,
					'gsurveyLikertEnableScoring'      => false,
					'choices'                         => array(
						array(
							'text'       => esc_html__( 'Strongly disagree', 'gravityforms' ),
							'value'      => 'glikertcol1155f01cdf',
							'isSelected' => false,
							'score'      => 1,
						),
						array(
							'text'       => esc_html__( 'Disagree', 'gravityforms' ),
							'value'      => 'glikertcol11dfa44d10',
							'isSelected' => false,
							'score'      => 2,
						),
						array(
							'text'       => esc_html__( 'Neutral', 'gravityforms' ),
							'value'      => 'glikertcol11358b8ecf',
							'isSelected' => false,
							'score'      => 3,
						),
						array(
							'text'       => esc_html__( 'Agree', 'gravityforms' ),
							'value'      => 'glikertcol11c522b458',
							'isSelected' => false,
							'score'      => 4,
						),
						array(
							'text'       => esc_html__( 'Strongly agree', 'gravityforms' ),
							'value'      => 'glikertcol11fcc374ae',
							'isSelected' => false,
							'score'      => 5,
						),
					),
					'gsurveyLikertRows'               => array(
						array(
							'text'  => esc_html__( 'First row', 'gravityforms' ),
							'value' => 'glikertrowc52dc85f',
						),
						array(
							'text'  => esc_html__( 'Second row', 'gravityforms' ),
							'value' => 'glikertrow3a5ccd99',
						),
						array(
							'text'  => esc_html__( 'Third row', 'gravityforms' ),
							'value' => 'glikertrow2b93fc92',
						),
						array(
							'text'  => esc_html__( 'Fourth row', 'gravityforms' ),
							'value' => 'glikertrow16b06a24',
						),
						array(
							'text'  => esc_html__( 'Fifth row', 'gravityforms' ),
							'value' => 'glikertrow12632421',
						),
					),
					'inputType'                       => 'likert',
					'formId'                          => 7,
					'description'                     => '',
					'allowsPrepopulate'               => false,
					'inputMask'                       => false,
					'inputMaskValue'                  => '',
					'inputMaskIsCustom'               => false,
					'maxLength'                       => '',
					'labelPlacement'                  => '',
					'descriptionPlacement'            => '',
					'subLabelPlacement'               => '',
					'placeholder'                     => '',
					'cssClass'                        => '',
					'inputName'                       => '',
					'noDuplicates'                    => false,
					'defaultValue'                    => '',
					'conditionalLogic'                => '',
					'productField'                    => '',
					'multipleFiles'                   => false,
					'maxFiles'                        => '',
					'calculationFormula'              => '',
					'calculationRounding'             => '',
					'enableCalculation'               => '',
					'disableQuantity'                 => false,
					'displayAllCategories'            => false,
					'useRichTextEditor'               => false,
					'fields'                          => '',
					'displayOnly'                     => '',
					'layoutGroupId'                   => 'ddb4c893',
					'autocompleteAttribute'           => '',
					'enableAutocomplete'              => false,
				),
				array(
					'type'                            => 'survey',
					'id'                              => 12,
					'label'                           => esc_html__( 'How could Acme Products be improved?', 'gravityforms' ),
					'adminLabel'                      => '',
					'isRequired'                      => false,
					'size'                            => 'medium',
					'errorMessage'                    => '',
					'visibility'                      => 'visible',
					'inputs'                          => null,
					'enableChoiceValue'               => true,
					'enablePrice'                     => false,
					'gsurveyLikertEnableMultipleRows' => false,
					'gsurveyLikertEnableScoring'      => false,
					'choices'                         => null,
					'gsurveyLikertRows'               => array(
						array(
							'text'  => esc_html__( 'First row', 'gravityforms' ),
							'value' => 'glikertrow26cabb34',
						),
						array(
							'text'  => esc_html__( 'Second row', 'gravityforms' ),
							'value' => 'glikertrowfac6bd78',
						),
						array(
							'text'  => esc_html__( 'Third row', 'gravityforms' ),
							'value' => 'glikertrow6ddc2315',
						),
						array(
							'text'  => esc_html__( 'Fourth row', 'gravityforms' ),
							'value' => 'glikertrow32956892',
						),
						array(
							'text'  => esc_html__( 'Fifth row', 'gravityforms' ),
							'value' => 'glikertrow77241c11',
						),
					),
					'inputType'                       => 'textarea',
					'formId'                          => 7,
					'description'                     => '',
					'allowsPrepopulate'               => false,
					'inputMask'                       => false,
					'inputMaskValue'                  => '',
					'inputMaskIsCustom'               => false,
					'maxLength'                       => '',
					'labelPlacement'                  => '',
					'descriptionPlacement'            => '',
					'subLabelPlacement'               => '',
					'placeholder'                     => '',
					'cssClass'                        => '',
					'inputName'                       => '',
					'noDuplicates'                    => false,
					'defaultValue'                    => '',
					'conditionalLogic'                => '',
					'productField'                    => '',
					'multipleFiles'                   => false,
					'maxFiles'                        => '',
					'calculationFormula'              => '',
					'calculationRounding'             => '',
					'enableCalculation'               => '',
					'disableQuantity'                 => false,
					'displayAllCategories'            => false,
					'useRichTextEditor'               => false,
					'checkboxLabel'                   => '',
					'fields'                          => '',
					'displayOnly'                     => '',
					'layoutGroupId'                   => '966d767b',
					'autocompleteAttribute'           => '',
					'errors'                          => array(),
					'enableAutocomplete'              => false,
				),
			),
			'version'                    => '2.7',
			'id'                         => 7,
			'nextFieldId'                => 14,
			'useCurrentUserAsAuthor'     => true,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => null,
			'pagination'                 => null,
			'firstPageCssClass'          => null,
			'subLabelPlacement'          => 'above',
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'customRequiredIndicator'    => '',
			'cssClass'                   => '',
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'markupVersion'              => 2,
			'confirmations'              => array(
				'5f0353d1a994f' => array(
					'id'          => '5f0353d1a994f',
					'name'        => 'Default Confirmation',
					'isDefault'   => true,
					'type'        => 'message',
					'message'     => esc_html__( 'Thank you for contacting us! We will get in touch with you shortly.', 'gravityforms' ),
					'url'         => '',
					'pageId'      => '',
					'queryString' => '',
				),
			),
			'notifications'              => array(
				'5f0353d1a8c63' => array(
					'id'       => '5f0353d1a8c63',
					'isActive' => true,
					'to'       => '{admin_email}',
					'name'     => 'Admin Notification',
					'event'    => 'form_submission',
					'toType'   => 'email',
					'subject'  => 'New submission from {form_title}',
					'message'  => '{all_fields}',
				),
			),
		),
		'version'               => '2.7',
	),
	'user_registration'    => array(
		'id'                    => 'user_registration',
		'description'           => esc_html__( 'Let your users register to your website easily', 'gravityforms' ),
		'title'                 => esc_html__( 'User Registration Form', 'gravityforms' ),
		'template_background'   => 'maverick-purple',
		'template_thumbnail'    => 'UserRegistration.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/user-registration/',
		'template_access_level' => [ 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'title'                      => esc_html__( 'User Registration Form', 'gravityforms' ),
			'description'                => esc_html__( 'Please complete the following form to register on our site. Thanks.', 'gravityforms' ),
			'labelPlacement'             => 'top_label',
			'descriptionPlacement'       => 'above',
			'button'                     => array(
				'type'             => 'text',
				'text'             => esc_html__( 'Submit', 'gravityforms' ),
				'imageUrl'         => '',
				'conditionalLogic' => null,
			),
			'fields'                     => array(
				array(
					'type'                  => 'name',
					'id'                    => 1,
					'label'                 => esc_html__( 'Name', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'nameFormat'            => 'advanced',
					'inputs'                => array(
						array(
							'id'                    => '1.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'       => esc_html__( 'Mr.', 'gravityforms' ),
									'value'      => 'Mr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Mrs.', 'gravityforms' ),
									'value'      => 'Mrs.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Miss', 'gravityforms' ),
									'value'      => 'Miss',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Ms.', 'gravityforms' ),
									'value'      => 'Ms.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Dr.', 'gravityforms' ),
									'value'      => 'Dr.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Prof.', 'gravityforms' ),
									'value'      => 'Prof.',
									'isSelected' => false,
									'price'      => '',
								),
								array(
									'text'       => esc_html__( 'Rev.', 'gravityforms' ),
									'value'      => 'Rev.',
									'isSelected' => false,
									'price'      => '',
								),
							),
							'isHidden'  => true,
							'inputType' => 'radio',
						),
						array(
							'id'                    => '1.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'given-name',
						),
						array(
							'id'                    => '1.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '1.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'family-name',
						),
						array(
							'id'                    => '1.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'formId'                => 9,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'maxLength'             => '',
					'layoutGroupId'         => '3540de4e',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                  => 'email',
					'id'                    => 2,
					'label'                 => esc_html__( 'Email', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => array(
						array(
							'id'                    => '2',
							'label'                 => esc_html__( 'Enter Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
						array(
							'id'                    => '2.2',
							'label'                 => esc_html__( 'Confirm Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
						),
					),
					'formId'                => 9,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => 'visible',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'emailConfirmEnabled'   => true,
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'layoutGroupId'         => '530ff557',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'autocompleteAttribute' => 'email',
					'useRichTextEditor'     => false,
					'enableAutocomplete'    => true,
				),
				array(
					'type'                  => 'username',
					'id'                    => 3,
					'label'                 => esc_html__( 'Username', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'inputs'                => null,
					'formId'                => 9,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'visibility'            => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'enablePasswordInput'   => '',
					'maxLength'             => '',
					'displayOnly'           => '',
					'fields'                => '',
					'inputMaskIsCustom'     => '',
					'layoutGroupId'         => '863643e3',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
				),
				array(
					'type'                    => 'password',
					'id'                      => 4,
					'label'                   => esc_html__( 'Password', 'gravityforms' ),
					'adminLabel'              => '',
					'isRequired'              => false,
					'size'                    => 'medium',
					'errorMessage'            => '',
					'inputs'                  => array(
						array(
							'id'    => '4',
							'label' => esc_html__( 'Enter Password', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'    => '4.2',
							'label' => esc_html__( 'Confirm Password', 'gravityforms' ),
							'name'  => '',
						),
					),
					'displayOnly'             => true,
					'formId'                  => 9,
					'description'             => '',
					'allowsPrepopulate'       => false,
					'inputMask'               => false,
					'inputMaskValue'          => '',
					'inputType'               => '',
					'labelPlacement'          => '',
					'descriptionPlacement'    => '',
					'subLabelPlacement'       => '',
					'placeholder'             => '',
					'cssClass'                => '',
					'inputName'               => '',
					'visibility'              => '',
					'noDuplicates'            => false,
					'defaultValue'            => '',
					'choices'                 => '',
					'conditionalLogic'        => '',
					'productField'            => '',
					'passwordStrengthEnabled' => '',
					'fields'                  => '',
					'inputMaskIsCustom'       => '',
					'maxLength'               => '',
					'layoutGroupId'           => '73b91ae6',
					'enableAutocomplete'      => false,
					'autocompleteAttribute'   => '',
				),
			),
			'version'                    => '2.7',
			'id'                         => 9,
			'useCurrentUserAsAuthor'     => true,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => null,
			'pagination'                 => null,
			'firstPageCssClass'          => null,
			'nextFieldId'                => 6,
			'subLabelPlacement'          => 'above',
			'cssClass'                   => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'feeds'                      => array(
				'gravityformsadvancedpostcreation' => array(),
			),
			'markupVersion'              => 2,
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'customRequiredIndicator'    => '',
			'confirmations'              => array(
				'59de6bab448e6' => array(
					'id'                => '59de6bab448e6',
					'name'              => 'Default Confirmation',
					'isDefault'         => true,
					'type'              => 'message',
					'message'           => esc_html__( 'Thank you for registering! You should receive an email from us shortly containing your account information.', 'gravityforms' ),
					'url'               => '',
					'pageId'            => 0,
					'queryString'       => '',
					'disableAutoformat' => false,
					'conditionalLogic'  => array(),
				),
			),
			'notifications'              => array(
				'59de6bab43fe8' => array(
					'id'      => '59de6bab43fe8',
					'to'      => '{admin_email}',
					'name'    => 'Admin Notification',
					'event'   => 'form_submission',
					'toType'  => 'email',
					'subject' => 'New submission from {form_title}',
					'message' => '{all_fields}',
				),
			),
		),
		'version'               => '2.7',
	),
	'webinar_registration' => array(
		'id'                    => 'webinar_registration',
		'description'           => esc_html__( 'Helps your users register to a webinar', 'gravityforms' ),
		'title'                 => esc_html__( 'Webinar Registration Form', 'gravityforms' ),
		'template_background'   => 'porcelain-gray',
		'template_thumbnail'    => 'WebinarRegistration.svg',
		'template_preview_url'  => 'https://www.gravityforms.com/form-templates/webinar-registration-form/',
		'template_access_level' => [ 'godaddy', 'gravityflow', 'gravityview', 'single', 'ltsingle', 'basic', 'multi', 'ltmulti', 'pro', 'dev', 'ltdev', 'nonprofit', 'elite', 'enterprise', 'wpcom' ],
		'form_meta'             => array(
			'title'                      => esc_html__( 'Webinar Registration Form', 'gravityforms' ),
			'description'                => esc_html__( 'Register for our latest webinar...', 'gravityforms' ),
			'labelPlacement'             => 'top_label',
			'descriptionPlacement'       => 'below',
			'button'                     => array(
				'type'             => 'text',
				'text'             => esc_html__( 'Register Today', 'gravityforms' ),
				'imageUrl'         => '',
				'conditionalLogic' => null,
			),
			'fields'                     => array(
				array(
					'type'                  => 'name',
					'id'                    => 1,
					'label'                 => esc_html__( 'Name', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'large',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'nameFormat'            => 'advanced',
					'inputs'                => array(
						array(
							'id'                    => '1.2',
							'label'                 => esc_html__( 'Prefix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-prefix',
							'choices'               => array(
								array(
									'text'  => esc_html__( 'Dr.', 'gravityforms' ),
									'value' => 'Dr.',
								),
								array(
									'text'  => esc_html__( 'Miss', 'gravityforms' ),
									'value' => 'Miss',
								),
								array(
									'text'  => esc_html__( 'Mr.', 'gravityforms' ),
									'value' => 'Mr.',
								),
								array(
									'text'  => esc_html__( 'Mrs.', 'gravityforms' ),
									'value' => 'Mrs.',
								),
								array(
									'text'  => esc_html__( 'Ms.', 'gravityforms' ),
									'value' => 'Ms.',
								),
								array(
									'text'  => esc_html__( 'Prof.', 'gravityforms' ),
									'value' => 'Prof.',
								),
								array(
									'text'  => esc_html__( 'Rev.', 'gravityforms' ),
									'value' => 'Rev.',
								),
							),
							'isHidden'              => true,
							'inputType'             => 'radio',
						),
						array(
							'id'                    => '1.3',
							'label'                 => esc_html__( 'First', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'given-name',
							'placeholder'           => '',
						),
						array(
							'id'                    => '1.4',
							'label'                 => esc_html__( 'Middle', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'additional-name',
							'isHidden'              => true,
						),
						array(
							'id'                    => '1.6',
							'label'                 => esc_html__( 'Last', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'family-name',
							'placeholder'           => '',
						),
						array(
							'id'                    => '1.8',
							'label'                 => esc_html__( 'Suffix', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'honorific-suffix',
							'isHidden'              => true,
						),
					),
					'formId'                => 25,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => 'hidden_label',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'enableAutocomplete'    => true,
					'autocompleteAttribute' => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'layoutGridColumnSpan'  => '',
					'enableEnhancedUI'      => 0,
					'layoutGroupId'         => '139d210b',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
					'checkboxLabel'         => '',
				),
				array(
					'type'                  => 'email',
					'id'                    => 2,
					'label'                 => esc_html__( 'Email', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'large',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => array(
						array(
							'id'                    => '2',
							'label'                 => esc_html__( 'Enter Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
							'placeholder'           => '',
						),
						array(
							'id'                    => '2.2',
							'label'                 => esc_html__( 'Confirm Email', 'gravityforms' ),
							'name'                  => '',
							'autocompleteAttribute' => 'email',
							'placeholder'           => '',
						),
					),
					'autocompleteAttribute' => 'email',
					'formId'                => 25,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => 'hidden_label',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'enableAutocomplete'    => true,
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'layoutGridColumnSpan'  => 12,
					'emailConfirmEnabled'   => true,
					'enableEnhancedUI'      => 0,
					'layoutGroupId'         => '326d857d',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'fields'                => '',
					'displayOnly'           => '',
					'checkboxLabel'         => '',
				),
				array(
					'type'                  => 'website',
					'id'                    => 4,
					'label'                 => esc_html__( 'Company Website', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'placeholder'           => '',
					'formId'                => 25,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'layoutGridColumnSpan'  => 12,
					'enableEnhancedUI'      => 0,
					'layoutGroupId'         => 'da909499',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'errors'                => array(),
					'fields'                => '',
					'displayOnly'           => '',
				),
				array(
					'type'                  => 'text',
					'id'                    => 7,
					'label'                 => esc_html__( 'Position / Job Title', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'medium',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'formId'                => 25,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => '',
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'layoutGridColumnSpan'  => 12,
					'enablePasswordInput'   => '',
					'enableEnhancedUI'      => 0,
					'layoutGroupId'         => 'ccc73496',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'useRichTextEditor'     => false,
					'errors'                => array(),
					'fields'                => '',
					'displayOnly'           => '',
				),
				array(
					'type'                 => 'select',
					'id'                   => 13,
					'label'                => esc_html__( 'Industry Type', 'gravityforms' ),
					'adminLabel'           => '',
					'isRequired'           => false,
					'size'                 => 'medium',
					'errorMessage'         => '',
					'visibility'           => 'visible',
					'inputs'               => null,
					'choices'              => array(
						array(
							'text'       => esc_html__( 'Advertising', 'gravityforms' ),
							'value'      => 'Advertising',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Agriculture', 'gravityforms' ),
							'value'      => 'Agriculture',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Banking', 'gravityforms' ),
							'value'      => 'Banking',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Construction', 'gravityforms' ),
							'value'      => 'Construction',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Creatives', 'gravityforms' ),
							'value'      => 'Creatives',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Education', 'gravityforms' ),
							'value'      => 'Education',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Entertainment', 'gravityforms' ),
							'value'      => 'Entertainment',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Fashion', 'gravityforms' ),
							'value'      => 'Fashion',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Finance', 'gravityforms' ),
							'value'      => 'Finance',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Hospitality', 'gravityforms' ),
							'value'      => 'Hospitality',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Marketing', 'gravityforms' ),
							'value'      => 'Marketing',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Retail', 'gravityforms' ),
							'value'      => 'Retail',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Services', 'gravityforms' ),
							'value'      => 'Services',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Technology', 'gravityforms' ),
							'value'      => 'Technology',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Other', 'gravityforms' ),
							'value'      => 'Other',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'formId'               => 25,
					'description'          => '',
					'allowsPrepopulate'    => false,
					'inputMask'            => false,
					'inputMaskValue'       => '',
					'inputMaskIsCustom'    => false,
					'maxLength'            => '',
					'inputType'            => '',
					'labelPlacement'       => '',
					'descriptionPlacement' => '',
					'subLabelPlacement'    => '',
					'placeholder'          => '',
					'cssClass'             => '',
					'inputName'            => '',
					'noDuplicates'         => false,
					'defaultValue'         => '',
					'conditionalLogic'     => '',
					'productField'         => '',
					'enablePrice'          => '',
					'multipleFiles'        => false,
					'maxFiles'             => '',
					'calculationFormula'   => '',
					'calculationRounding'  => '',
					'enableCalculation'    => '',
					'disableQuantity'      => false,
					'displayAllCategories' => false,
					'useRichTextEditor'    => false,
					'fields'               => '',
					'displayOnly'          => '',
				),
				array(
					'type'                  => 'textarea',
					'id'                    => 8,
					'label'                 => esc_html__( 'Do you have any questions you would like to ask our speakers?', 'gravityforms' ),
					'adminLabel'            => '',
					'isRequired'            => false,
					'size'                  => 'small',
					'errorMessage'          => '',
					'visibility'            => 'visible',
					'inputs'                => null,
					'formId'                => 25,
					'description'           => '',
					'allowsPrepopulate'     => false,
					'inputMask'             => false,
					'inputMaskValue'        => '',
					'inputMaskIsCustom'     => false,
					'maxLength'             => 500,
					'inputType'             => '',
					'labelPlacement'        => '',
					'descriptionPlacement'  => '',
					'subLabelPlacement'     => '',
					'placeholder'           => '',
					'cssClass'              => '',
					'inputName'             => '',
					'noDuplicates'          => false,
					'defaultValue'          => '',
					'enableAutocomplete'    => false,
					'autocompleteAttribute' => '',
					'choices'               => '',
					'conditionalLogic'      => '',
					'productField'          => '',
					'layoutGridColumnSpan'  => 12,
					'form_id'               => '',
					'useRichTextEditor'     => false,
					'enableEnhancedUI'      => 0,
					'layoutGroupId'         => 'f3341a6c',
					'multipleFiles'         => false,
					'maxFiles'              => '',
					'calculationFormula'    => '',
					'calculationRounding'   => '',
					'enableCalculation'     => '',
					'disableQuantity'       => false,
					'displayAllCategories'  => false,
					'errors'                => array(),
					'fields'                => '',
					'displayOnly'           => '',
				),
				array(
					'type'                 => 'select',
					'id'                   => 16,
					'label'                => esc_html__( 'How did you hear about this webinar?', 'gravityforms' ),
					'adminLabel'           => '',
					'isRequired'           => false,
					'size'                 => 'medium',
					'errorMessage'         => '',
					'visibility'           => 'visible',
					'inputs'               => null,
					'choices'              => array(
						array(
							'text'       => esc_html__( 'Social Media', 'gravityforms' ),
							'value'      => 'Social Media',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Advertisement', 'gravityforms' ),
							'value'      => 'Advertisement',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Word of Mouth', 'gravityforms' ),
							'value'      => 'Word of Mouth',
							'isSelected' => false,
							'price'      => '',
						),
						array(
							'text'       => esc_html__( 'Other', 'gravityforms' ),
							'value'      => 'Other',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'formId'               => 25,
					'description'          => '',
					'allowsPrepopulate'    => false,
					'inputMask'            => false,
					'inputMaskValue'       => '',
					'inputMaskIsCustom'    => false,
					'maxLength'            => '',
					'inputType'            => '',
					'labelPlacement'       => '',
					'descriptionPlacement' => '',
					'subLabelPlacement'    => '',
					'placeholder'          => '',
					'cssClass'             => '',
					'inputName'            => '',
					'noDuplicates'         => false,
					'defaultValue'         => '',
					'conditionalLogic'     => '',
					'productField'         => '',
					'enablePrice'          => '',
					'multipleFiles'        => false,
					'maxFiles'             => '',
					'calculationFormula'   => '',
					'calculationRounding'  => '',
					'enableCalculation'    => '',
					'disableQuantity'      => false,
					'displayAllCategories' => false,
					'useRichTextEditor'    => false,
					'fields'               => '',
					'displayOnly'          => '',
				),
				array(
					'type'                     => 'consent',
					'checked_indicator_url'    => 'https://shy-pig.w6.gravitydemo.com/wp-content/plugins/gravityforms/images/tick.png',
					'checked_indicator_markup' => '<img src="https://shy-pig.w6.gravitydemo.com/wp-content/plugins/gravityforms/images/tick.png" />',
					'id'                       => 11,
					'label'                    => esc_html__( 'Want to keep up-to-date with our latest news and announcements?', 'gravityforms' ),
					'adminLabel'               => '',
					'isRequired'               => false,
					'size'                     => 'medium',
					'errorMessage'             => '',
					'visibility'               => 'visible',
					'inputs'                   => array(
						array(
							'id'    => '11.1',
							'label' => esc_html__( 'Consent', 'gravityforms' ),
							'name'  => '',
						),
						array(
							'id'       => '11.2',
							'label'    => esc_html__( 'Text', 'gravityforms' ),
							'name'     => '',
							'isHidden' => true,
						),
						array(
							'id'       => '11.3',
							'label'    => esc_html__( 'Description', 'gravityforms' ),
							'name'     => '',
							'isHidden' => true,
						),
					),
					'checkboxLabel'            => 'Yes please!',
					'descriptionplaceholder'   => '',
					'choices'                  => array(
						array(
							'text'       => esc_html__( 'Checked', 'gravityforms' ),
							'value'      => '1',
							'isSelected' => false,
							'price'      => '',
						),
					),
					'formId'                   => 25,
					'description'              => '',
					'allowsPrepopulate'        => false,
					'inputMask'                => false,
					'inputMaskValue'           => '',
					'inputMaskIsCustom'        => false,
					'maxLength'                => '',
					'labelPlacement'           => '',
					'descriptionPlacement'     => '',
					'subLabelPlacement'        => '',
					'placeholder'              => '',
					'cssClass'                 => '',
					'inputName'                => '',
					'noDuplicates'             => false,
					'defaultValue'             => '',
					'conditionalLogic'         => '',
					'productField'             => '',
					'multipleFiles'            => false,
					'maxFiles'                 => '',
					'calculationFormula'       => '',
					'calculationRounding'      => '',
					'enableCalculation'        => '',
					'disableQuantity'          => false,
					'displayAllCategories'     => false,
					'useRichTextEditor'        => false,
					'fields'                   => '',
					'displayOnly'              => '',
					'inputType'                => '',
				),
			),
			'version'                    => '2.7',
			'id'                         => 25,
			'markupVersion'              => 2,
			'nextFieldId'                => 17,
			'useCurrentUserAsAuthor'     => true,
			'postContentTemplateEnabled' => false,
			'postTitleTemplateEnabled'   => false,
			'postTitleTemplate'          => '',
			'postContentTemplate'        => '',
			'lastPageButton'             => null,
			'pagination'                 => null,
			'firstPageCssClass'          => null,
			'subLabelPlacement'          => 'above',
			'validationSummary'          => '1',
			'requiredIndicator'          => 'text',
			'customRequiredIndicator'    => '(Required)',
			'cssClass'                   => '',
			'save'                       => array(
				'enabled' => false,
				'button'  => array(
					'type' => 'link',
					'text' => esc_html__( 'Save and Continue Later', 'gravityforms' ),
				),
			),
			'limitEntries'               => false,
			'limitEntriesCount'          => '',
			'limitEntriesPeriod'         => '',
			'limitEntriesMessage'        => '',
			'requireLogin'               => false,
			'requireLoginMessage'        => '',
			'scheduleForm'               => false,
			'scheduleStart'              => '',
			'scheduleStartHour'          => '',
			'scheduleStartMinute'        => '',
			'scheduleStartAmpm'          => '',
			'scheduleEnd'                => '',
			'scheduleEndHour'            => '',
			'scheduleEndMinute'          => '',
			'scheduleEndAmpm'            => '',
			'schedulePendingMessage'     => '',
			'scheduleMessage'            => '',
			'enableHoneypot'             => false,
			'honeypotAction'             => 'spam',
			'enableAnimation'            => false,
			'feeds'                      => array(
				'gravityformsadvancedpostcreation' => array(),
			),
			'confirmations'              => array(
				'5f8dc7b7667f3' => array(
					'id'                => '5f8dc7b7667f3',
					'name'              => 'Default Confirmation',
					'isDefault'         => true,
					'type'              => 'message',
					'message'           => esc_html__( 'Thank you for registering for our webinar! Keep an eye out for an email from us containing more information.', 'gravityforms' ),
					'url'               => '',
					'pageId'            => '',
					'queryString'       => '',
					'event'             => '',
					'disableAutoformat' => false,
					'conditionalLogic'  => array(),
				),
			),
			'notifications'              => array(
				'5f8dc7b7655ed' => array(
					'id'       => '5f8dc7b7655ed',
					'isActive' => true,
					'to'       => '{admin_email}',
					'name'     => 'Admin Notification',
					'event'    => 'form_submission',
					'toType'   => 'email',
					'subject'  => 'New submission from {form_title}',
					'message'  => '{all_fields}',
				),
			),
		),
		'version'               => '2.7',
	),
);
