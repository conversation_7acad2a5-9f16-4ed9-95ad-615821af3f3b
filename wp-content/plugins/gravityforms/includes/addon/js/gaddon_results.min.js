var gresultsAjaxRequest,gresults={drawCharts:function(){jQuery(".gresults-chart-wrapper").each(function(e,t){var r,s=jQuery(t).attr("id"),a=jQuery(t).data("options"),l=jQuery(t).data("datatable"),t=jQuery(t).data("charttype"),l=google.visualization.arrayToDataTable(l),s=document.getElementById(s);"bar"==t?r=new google.visualization.BarChart(s):"pie"==t?r=new google.visualization.PieChart(s):"column"==t&&(r=new google.visualization.ColumnChart(s)),r.draw(l,a)});var e="rtl"===document.documentElement.dir;document.querySelector(".gresults-chart-wrapper svg")&&e&&document.querySelectorAll('text[text-anchor="end"]').forEach(function(e){e.setAttribute("text-anchor","start")})},renderStateData:function(e){var t=jQuery("#gresults-results"),e=(t.data("searchcriteria",e.searchCriteria),jQuery("#gresults-results-filter").html(e.filterUI),t.css("opacity",0),t.html(e.html),gresults.drawCharts(),t.fadeTo("slow",1),jQuery("#gresults-results-field-filters-container"));e.resizable(),e.resizable("destroy"),e.resizable({handles:"s"})},getResults:function(){gresults.recordFormState();var e=jQuery("#gresults-results-filter-form").serialize();gresults.sendRequest(e)},sendRequest:function(r,s,e){var a=jQuery("#gresults-results"),l=jQuery("#gresults-results-filter-buttons input"),t=jQuery("#gresults-view-slug").val(),u=jQuery("#_gf_results_nonce").val(),t="action=gresults_get_results_"+t+"&"+r+"&_gf_results_nonce"+u;s&&(t+="&state="+s+"&checkSum="+e),gresultsAjaxRequest=jQuery.ajax({url:ajaxurl,type:"POST",dataType:"json",data:t,beforeSend:function(e,t){a.fadeTo("slow",.33),a.html(""),gform.utils.trigger({event:"gform/page_loader/show"}),l.attr("disabled","disabled")}}).done(function(e){var t;e&&-1!==e?"complete"===e.status?(l.removeAttr("disabled"),gform.utils.trigger({event:"gform/page_loader/hide"}),a.html(e.html),jQuery("#gresults-results").data("searchcriteria",e.searchCriteria),t=jQuery("#gresults-results-filter").html(),gresults.drawCharts(),a.fadeTo("slow",1),window.history.replaceState&&(history.state?history.pushState({html:e.html,filterUI:t,searchCriteria:e.searchCriteria},"","?"+r):history.replaceState({html:e.html,filterUI:t,searchCriteria:e.searchCriteria},"","?"+r)),gresults.drawCharts(),window.gform_initialize_tooltips&&gform_initialize_tooltips()):"incomplete"===e.status?(s=e.stateObject,gresults.sendRequest(r,s,e.checkSum),a.html(e.html)):(gform.utils.trigger({event:"gform/page_loader/hide"}),a.html(gresultsStrings.ajaxError)):(gform.utils.trigger({event:"gform/page_loader/hide"}),a.html(gresultsStrings.ajaxError))}).fail(function(e){l.removeAttr("disabled"),a.fadeTo("fast",1);e=e.statusText;gform.utils.trigger({event:"gform/page_loader/hide"}),e="abort"==e?"Request cancelled":gresultsStrings.ajaxError,a.html(e)})},getMoreResults:function(e,t){var r=jQuery("#gresults-results-field-content-"+t),s=jQuery("#gresults-results"),a=jQuery(r).data("offset"),l=jQuery("#gresults-view-slug").val(),s=s.data("searchcriteria"),u=jQuery("#_gf_results_nonce").val();return jQuery.ajax({url:ajaxurl,type:"POST",dataType:"json",data:{action:"gresults_get_more_results_"+l,view:l,form_id:e,field_id:t,offset:a,search_criteria:s,_gf_results_nonce:u},success:function(e){-1!==e&&(e.html&&jQuery(r).append(e.html),e.more_remaining||jQuery("#gresults-results-field-more-link-"+t).hide(),jQuery(r).data("offset",e.offset))}}),!1},clearFilterForm:function(){jQuery("#gresults-results-field-filters-container").off("click",".gform-add").gfFilterUI(gresultsFilterSettings,[],!0),jQuery("#gresults-results-filter-form").find("input, select").each(function(){switch(this.type){case"text":case"select-one":jQuery(this).val("").change();break;case"checkbox":case"radio":this.checked=!1}})},recordFormState:function(){jQuery("#gresults-results-filter-form input[type='radio']").each(function(){this.checked?jQuery(this).prop("defaultChecked",!0):jQuery(this).prop("defaultChecked",!1)}),jQuery("#gresults-results-filter-form input[type='checkbox']").each(function(){this.checked?jQuery(this).prop("defaultChecked",!0):jQuery(this).prop("defaultChecked",!1)}),jQuery("#gresults-results-filter-form input[type='text']").each(function(){jQuery(this).prop("defaultValue",jQuery(this).val())}),jQuery("#gresults-results-filter-form select option").each(function(){jQuery(this).prop("defaultSelected",jQuery(this).prop("selected"))})},setCustomFilter:function(e,t){elementId="gresults-custom-"+e,0==jQuery("#"+elementId).length?jQuery("#gresults-results-filter-form").append("<input type='hidden' id='"+elementId+"' name='"+e+"' value='"+t+"'>"):jQuery("#"+elementId).val(t)}};google.load("visualization","1",{packages:["corechart"]}),google.setOnLoadCallback(gresults.drawCharts),jQuery(window).on("load",function(){0<jQuery("#gresults-results").length&&(jQuery("#gresults-results-field-filters-container").gfFilterUI(gresultsFilterSettings,gresultsInitVars,!0),jQuery(window).resize(function(e){e.target===window&&gresults.drawCharts()}),window.onpopstate=function(e){e.state&&gresults.renderStateData(e.state)},jQuery("#gresults-results-filter-date-start, #gresults-results-filter-date-end").datepicker({dateFormat:"yy-mm-dd",changeMonth:!0,changeYear:!0}),jQuery(".ui-datepicker-trigger").on("click",function(){jQuery(this).parent().find("input").datepicker("show")}),jQuery("#gresults-results-filter-form").submit(function(e){return gresults.getResults(),!1}),history.state?gresults.renderStateData(history.state):gresults.getResults(),window.gform_initialize_tooltips)&&gform_initialize_tooltips()});