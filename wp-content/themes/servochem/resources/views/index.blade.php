@extends('layouts.app')

@section('content')
	<section class="page mt-[72px]">
		@include('partials.page-header', ['title' => 'The Servochem Blog'])
	</section>
	<section class="">
		<div class="container grid grid-cols-1 lg:grid-cols-3">
			<div class="bg-white rounded-2xl shadow-[0_4px_20px_0_rgba(0,_0,_0,_0.10)] px-10 py-6 mb-10 flex items-center gap-3">
				<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path fill-rule="evenodd" clip-rule="evenodd" d="M4.66572 4.66547C6.21295 3.11825 8.31144 2.24902 10.4995 2.24902C12.6877 2.24902 14.7862 3.11825 16.3334 4.66547C17.8806 6.2127 18.7498 8.31119 18.7498 10.4993C18.7498 12.436 18.0689 14.3025 16.8396 15.7787L21.5304 20.4695C21.8233 20.7624 21.8233 21.2372 21.5304 21.5301C21.2375 21.823 20.7626 21.823 20.4697 21.5301L15.7789 16.8393C14.3027 18.0686 12.4362 18.7496 10.4995 18.7496C8.31144 18.7496 6.21295 17.8804 4.66572 16.3331C3.11849 14.7859 2.24927 12.6874 2.24927 10.4993C2.24927 8.31119 3.11849 6.2127 4.66572 4.66547ZM10.4995 3.74902C8.70926 3.74902 6.9923 4.46021 5.72638 5.72614C4.46046 6.99206 3.74927 8.70902 3.74927 10.4993C3.74927 12.2896 4.46046 14.0066 5.72638 15.2725C6.9923 16.5384 8.70926 17.2496 10.4995 17.2496C12.2898 17.2496 14.0068 16.5384 15.2727 15.2725C16.5386 14.0066 17.2498 12.2896 17.2498 10.4993C17.2498 8.70902 16.5386 6.99206 15.2727 5.72614C14.0068 4.46021 12.2898 3.74902 10.4995 3.74902Z" fill="#939598"/>
				</svg>
				{!! facetwp_display( 'facet', 'search_posts' ) !!}
			</div>
		</div>

		<div class="facetwp-template container flex flex-col gap-6 mb-[72px] posts">

			@while(have_posts()) @php(the_post())
				<div class="bg-white rounded-2xl shadow-[0_4px_18px_0_rgba(0,_0,_0,_0.10)] p-6">
					<div class="text-sm lg:text-base font-medium mb-6">{{ get_the_date('d/m/Y ') }}</div>
					<div class="text-sm lg:text-base font-semibold mb-2">{!! the_title() !!}</div>
					<p class="text-sm lg:text-base text-grey mb-8">{!! get_the_excerpt() !!}</p>
					<a class="text-red hover:text-black transition-all text-xs lg:text-sm font-bold" href="{{ the_permalink() }}">Read More</a>
				</div>
			@endwhile
			{!! facetwp_display( 'facet', 'post_pagination' ) !!}
		</div>
	</section>

	@php($content = get_field('blocks'))
	@if($content)
		@foreach ($content as $content_section)
			{{-- load the component, named after the layout name in ACF --}}
			<div id="block_{{ $loop->iteration }}" class="c-{{ $content_section['acf_fc_layout'] }}">
				@include('blocks.' . $content_section['acf_fc_layout'], [
					'data' => $content_section,
					'first_swimlane' => $loop->first 
				])
			</div>
		@endforeach
	@endif

	
	
@endsection