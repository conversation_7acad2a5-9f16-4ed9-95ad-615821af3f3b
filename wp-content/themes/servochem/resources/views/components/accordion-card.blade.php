@props([
	'title' => null,
])

<div class="bg-white px-4 lg:px-6 py-7 rounded-2xl border-t-4 border-red shadow-[0_4px_22.2px_0_rgba(0,_0,_0,_0.20)]">
	<div class="max-lg:hidden h-full flex flex-col">
		<div class="text-lg lg:text-2xl font-semibold">{!! $title !!}</div>
		{!! $slot !!}
	</div>

	<details class="lg:hidden">
		<summary class="flex justify-between items-center">
			<div class="text-lg lg:text-2xl font-semibold">{!! $title !!}</div>
			<svg class="transition-all shrink-0" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M21 7L11.9979 17.0012L2.99579 7" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
		</summary>
		 {!! $slot !!}
	</details>

</div>