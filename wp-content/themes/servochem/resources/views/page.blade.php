@extends('layouts.app')

@section('content')
    @while (have_posts())
        @php(the_post())
        <article class="page mt-[72px]">
			@include('partials.page-header')
			<div class="container">@php(the_content())</div>
			@php($content = get_field('blocks'))
			@if($content)
				@foreach ($content as $content_section)
					{{-- load the component, named after the layout name in ACF --}}
					<div id="block_{{ $loop->iteration }}" class="c-{{ $content_section['acf_fc_layout'] }}">
						@include('blocks.' . $content_section['acf_fc_layout'], [
							'data' => $content_section,
							'first_swimlane' => $loop->first 
						])
					</div>
				@endforeach
			@endif
		</article>
    @endwhile
@endsection
