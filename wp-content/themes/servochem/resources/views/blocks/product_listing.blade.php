@php
	// Inner Loop using WP_Query
	$args = array(
		'post_type' => 'product-range',
		'order' => 'DESC',
		'orderby' => 'date',
		'posts_per_page' => -1,
		"facetwp" => true // this flags this custom query to be used by FacetWP
	);

	$product_ranges_query = new WP_Query( $args );
@endphp
<section class="product-listing mb-12 lg:mb-28">
	<div class="container">
		<div class="bg-white rounded-2xl shadow-[0_4px_20px_0_rgba(0,_0,_0,_0.10)] px-10 py-6 mb-10 flex items-center gap-3">
			<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd" d="M4.66572 4.66547C6.21295 3.11825 8.31144 2.24902 10.4995 2.24902C12.6877 2.24902 14.7862 3.11825 16.3334 4.66547C17.8806 6.2127 18.7498 8.31119 18.7498 10.4993C18.7498 12.436 18.0689 14.3025 16.8396 15.7787L21.5304 20.4695C21.8233 20.7624 21.8233 21.2372 21.5304 21.5301C21.2375 21.823 20.7626 21.823 20.4697 21.5301L15.7789 16.8393C14.3027 18.0686 12.4362 18.7496 10.4995 18.7496C8.31144 18.7496 6.21295 17.8804 4.66572 16.3331C3.11849 14.7859 2.24927 12.6874 2.24927 10.4993C2.24927 8.31119 3.11849 6.2127 4.66572 4.66547ZM10.4995 3.74902C8.70926 3.74902 6.9923 4.46021 5.72638 5.72614C4.46046 6.99206 3.74927 8.70902 3.74927 10.4993C3.74927 12.2896 4.46046 14.0066 5.72638 15.2725C6.9923 16.5384 8.70926 17.2496 10.4995 17.2496C12.2898 17.2496 14.0068 16.5384 15.2727 15.2725C16.5386 14.0066 17.2498 12.2896 17.2498 10.4993C17.2498 8.70902 16.5386 6.99206 15.2727 5.72614C14.0068 4.46021 12.2898 3.74902 10.4995 3.74902Z" fill="#939598"/>
			</svg>

			{!! facetwp_display( 'facet', 'search_products' ) !!}
		</div>
		
		<div class="facetwp-template bg-white rounded-2xl shadow-[0_4px_20px_0_rgba(0,_0,_0,_0.10)] px-10 py-3">
			@if ($product_ranges_query->have_posts())
		
				@while($product_ranges_query->have_posts()) @php($product_ranges_query->the_post())
					@php($range_products = get_field('products'))
					<details class="border-b border-[#EDEDED] last:border-none" data-id="{{ the_ID() }}">
						<summary class="flex justify-between items-center py-6">
							<h2>{!! the_title() !!}</h2>
							<div class="flex items-center gap-12 shrink-0">
								<div class="text-red text-xs px-2 py-1 rounded-full font-medium bg-[#FFECEC]">{{ $range_products ? count($range_products) : 0 }} products</div>
								<svg class="transition-all shrink-0" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M21 7L11.9979 17.0012L2.99579 7" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>
							</div>
						</summary>
						<div class="flex flex-col gap-3">
							@if($range_products)
								@foreach($range_products as $product)
									<div class="bg-light-grey p-4 rounded-lg flex gap-4 items-center">
										<div class="max-sm:hidden">
											<img src="{{ $product['image']['url'] }}" alt="{{ $product['image']['alt'] ?? $product['title'] }}" />
										</div>
										<div class="">
											<h3 class="lg:text-lg font-semibold mb-1">{!! $product['title'] !!}</h3>
											<p class="text-sm lg:text-base mb-2">{!! $product['subtitle'] !!}</p>
											<p class="text-sm italic mb-2">{!! $product['description'] !!}</p>
											@if($product['applications'] && count($product['applications']) > 0)
												<div class="text-sm lg:text-base font-medium mb-2">Applications:</div>
												<div class="flex items-center flex-wrap gap-4">
													@foreach ($product['applications'] as $application)
														<div class="px-3 py-1 bg-white border border-dark-blue text-dark-blue rounded-full">{{ $application['application'] }}</div>
													@endforeach
												</div>
											@endif
										</div>
									</div>
								@endforeach
							@endif
						</div>
					</details>
				@endwhile
			@else
				<div class="py-8 lg:py-12 flex flex-col items-center justify-center text-center">
					<svg class="mb-10" width="84" height="85" viewBox="0 0 84 85" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path fill-rule="evenodd" clip-rule="evenodd" d="M16.4309 16.8292C21.8247 11.4139 29.1402 8.37158 36.7682 8.37158C44.3962 8.37158 51.7118 11.4139 57.1056 16.8292C62.4994 22.2445 65.5296 29.5892 65.5296 37.2476C65.5296 44.026 63.1558 50.5587 58.8703 55.7253L75.2229 72.1432C76.244 73.1683 76.244 74.8303 75.2229 75.8555C74.2019 76.8806 72.5464 76.8806 71.5254 75.8555L55.1727 59.4376C50.0265 63.7402 43.5198 66.1236 36.7682 66.1236C29.1402 66.1236 21.8247 63.0813 16.4309 57.666C11.037 52.2507 8.00684 44.906 8.00684 37.2476C8.00684 29.5892 11.037 22.2445 16.4309 16.8292ZM36.7682 13.6216C30.5271 13.6216 24.5416 16.1107 20.1284 20.5415C15.7153 24.9722 13.236 30.9816 13.236 37.2476C13.236 43.5136 15.7153 49.5229 20.1284 53.9537C24.5416 58.3844 30.5271 60.8736 36.7682 60.8736C43.0094 60.8736 48.9949 58.3844 53.408 53.9537C57.8212 49.5229 60.3005 43.5136 60.3005 37.2476C60.3005 30.9816 57.8212 24.9722 53.408 20.5415C48.9949 16.1107 43.0094 13.6216 36.7682 13.6216Z" fill="#F1F2F2"/>
						<path d="M51 25.82L48.18 23L37 34.18L25.82 23L23 25.82L34.18 37L23 48.18L25.82 51L37 39.82L48.18 51L51 48.18L39.82 37L51 25.82Z" fill="#F1F2F2"/>
					</svg>
					<div class="text-lg lg:text-2xl font-medium mb-4">No results found</div>
					<p class="text-sm lg:text-base text-grey mb-10">We couldn't find any products matching your search. Try adjusting your search terms or browse our popular categories below.</p>
					<button class="js-clear-search p-3 rounded-lg border border-grey text-grey text-sm lg:text-base hover:bg-grey hover:text-black transition-all cursor-pointer">
						Clear Search
					</button>
				</div>
			@endif
		</div>
	</div>
</section>

<script>
	(function() {
		document.addEventListener('facetwp-loaded', function() {
	
			const searchfacet = 'search_products'; // Replace 'my_search_facet' with the name of your Search facet
			const searchbox = document.querySelector(`[data-name="${searchfacet}"] .facetwp-search`);
			const clearIcon = document.querySelector('.js-clear-search');
		
			searchbox.addEventListener('keyup', function() {
				if (FWP.settings[searchfacet]['auto_refresh'] === 'yes') {
					this.classList.add('loading');
				}
			});
	
			searchbox.classList.remove('loading');
	
			if (clearIcon) {
				clearIcon.addEventListener('click', function() {
					// ignore while Search facet is loading
					if (!searchbox.previousElementSibling.classList.contains('f-loading')) {
						searchbox.value = '';
						if (FWP.facets[searchfacet].length) {
							FWP.autoload();
						}
					}
				});
			}
		});
    })();
</script>