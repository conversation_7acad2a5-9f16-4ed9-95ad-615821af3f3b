@if(get_field('mobile_background'))
	<style>
		.page-hero {
			background-image: linear-gradient(90deg, rgba(43, 75, 115, 0.90) 0%, rgba(17, 37, 62, 0.90) 100%), url('{{ get_field('mobile_background')['url'] }}');
			color: white;
			padding: 64px 0 !important;
		}
		@media (min-width: 1024px) {
			.page-hero {
				background-image: linear-gradient(90deg, rgba(43, 75, 115, 0.90) 0%, rgba(17, 37, 62, 0.90) 100%), url('{{ get_field('desktop_background')['url'] }}');
			}
		}
	</style>
@endif	
<section class="page-hero bg-center bg-cover bg-no-repeat text-center pt-8 lg:pt-12 mb-14">
	<div class="container">
		<h1 class="text-2xl lg:text-[40px] mb-6">{!! $title ?? the_title() !!}</h1>
		@if(get_field('subtitle'))
			<p class="max-w-[620px] mx-auto">{!! $subtitle ?? get_field('subtitle') !!}</p>
		@endif
	</div>
</section>