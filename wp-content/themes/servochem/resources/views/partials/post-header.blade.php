@php
	function estimate_reading_time() {
		$content = get_post_field( 'post_content', get_the_ID() ); // Get the content of the current post
		$word_count = str_word_count( strip_tags( $content ) ); // Count words after stripping HTML tags
		$words_per_minute = 250; // Average reading speed in words per minute (adjust as needed)
		$reading_time_minutes = ceil( $word_count / $words_per_minute ); // Calculate minutes and round up

		return $reading_time_minutes . ' min read';
	}
@endphp

<section class="pt-6 lg:pt-[72px]">
	<div class="container">
		<div class="pb-4 mb-6 lg:pb-6 lg:mb-8 border-b border-[#EDEDED]">
			<div class="flex gap-1 items-center text-xs mb-3">
				<a href="{{ site_url('/blog') }}" class="text-grey font-medium hover:text-red transition-all">Blog /</a>
				<span class="font-semibold">{!! the_title() !!}</span>
			</div>
			<h1 class="text-[32px] lg:text-4xl font-semibold mb-3">{{ the_title() }}</h1>
			@if(get_field('subtitle'))
				<p class="text-xl lg:text-2xl">{!! get_field('subtitle') !!}</p>
			@endif
			<div class="flex items-center mt-8 gap-4">
				<div class="flex items-center gap-3">
					<svg width="14" height="17" viewBox="0 0 14 17" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M4.66667 7.7H3.11111V9.3H4.66667V7.7ZM7.77778 7.7H6.22222V9.3H7.77778V7.7ZM10.8889 7.7H9.33333V9.3H10.8889V7.7ZM12.4444 2.1H11.6667V0.5H10.1111V2.1H3.88889V0.5H2.33333V2.1H1.55556C1.35167 2.1001 1.14981 2.14162 0.961567 2.22216C0.773319 2.3027 0.602389 2.42069 0.458582 2.56935C0.314776 2.718 0.200926 2.89441 0.123566 3.08844C0.0462047 3.28246 0.00685612 3.49029 0.00777777 3.7L0 14.9C0 15.3243 0.163888 15.7313 0.455612 16.0314C0.747335 16.3314 1.143 16.5 1.55556 16.5H12.4444C12.8566 16.4987 13.2516 16.3298 13.543 16.03C13.8345 15.7302 13.9988 15.324 14 14.9V3.7C13.9988 3.27604 13.8345 2.86981 13.543 2.57003C13.2516 2.27024 12.8566 2.10127 12.4444 2.1ZM12.4444 14.9H1.55556V6.1H12.4444V14.9Z" fill="#939598"/>
					</svg>
					<span class="text-sm text-grey">{{ get_the_date('d/m/Y ') }}</span>
				</div>
				<div class="flex items-center gap-3">
					<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path fill-rule="evenodd" clip-rule="evenodd" d="M8 14.5C4.68629 14.5 2 11.8137 2 8.5C2 5.18629 4.68629 2.5 8 2.5C11.3137 2.5 14 5.18629 14 8.5C14 11.8137 11.3137 14.5 8 14.5Z" stroke="#939598" stroke-width="1.68831" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M8 5.8335V8.50016L6 10.5002" stroke="#939598" stroke-width="1.68831" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
					<span class="text-sm text-grey">{{ estimate_reading_time()}}</span>
				</div>
			</div>
		</div>
	</div>
</section>