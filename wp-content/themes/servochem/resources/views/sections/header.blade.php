@if (has_nav_menu('primary_navigation'))
	@php($primary_navigation = wp_get_nav_menu_items(wp_get_nav_menu_name('primary_navigation')))
@endif

<header x-data="{ showSearch: false, showMobileMenu: false }" class="bg-white/90 shadow-[0_4px_16.2px_0_rgba(0,_0,_0,_0.03)] fixed inset-x-0 z-50">
	<div class="container flex justify-between items-center py-6">
		<a href="{{ site_url() }}"><img src="{{Vite::asset('resources/images/logo.svg')}}" alt="Servochem Logo" width="189px" height="20px" /></a>	
		<div 
			class="max-lg:absolute max-lg:top-[72px] max-lg:left-0 max-lg:bottom-0 max-lg:w-full max-lg:h-fit max-lg:pt-14 flex flex-col lg:flex-row items-center gap-14 max-lg:bg-white/90"
			:class="{ 'max-lg:hidden': !showMobileMenu }"
		>
			@foreach ($primary_navigation as $item)
				<a href="{{ $item->url }}" class="font-semibold hover:text-red transition-all no-underline">{{ $item->title }}</a>
			@endforeach
			<a @click.prevent="showSearch = !showSearch" class="group" href="#">
				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
					<path class="fill-black group-hover:fill-red transition-all" fill-rule="evenodd" clip-rule="evenodd" d="M4.66547 4.66547C6.2127 3.11825 8.31119 2.24902 10.4993 2.24902C12.6874 2.24902 14.7859 3.11825 16.3331 4.66547C17.8804 6.2127 18.7496 8.31119 18.7496 10.4993C18.7496 12.436 18.0686 14.3025 16.8393 15.7787L21.5301 20.4695C21.823 20.7624 21.823 21.2372 21.5301 21.5301C21.2372 21.823 20.7624 21.823 20.4695 21.5301L15.7787 16.8393C14.3025 18.0686 12.436 18.7496 10.4993 18.7496C8.3112 18.7496 6.2127 17.8804 4.66547 16.3331C3.11825 14.7859 2.24902 12.6874 2.24902 10.4993C2.24902 8.31119 3.11825 6.2127 4.66547 4.66547ZM10.4993 3.74902C8.70902 3.74902 6.99206 4.46021 5.72614 5.72614C4.46021 6.99206 3.74902 8.70902 3.74902 10.4993C3.74902 12.2896 4.46021 14.0066 5.72614 15.2725C6.99206 16.5384 8.70902 17.2496 10.4993 17.2496C12.2896 17.2496 14.0066 16.5384 15.2725 15.2725C16.5384 14.0066 17.2496 12.2896 17.2496 10.4993C17.2496 8.70902 16.5384 6.99206 15.2725 5.72614C14.0066 4.46021 12.2896 3.74902 10.4993 3.74902Z"/>
				</svg>
			</a>
		</div>
		<a @click.prevent="showMobileMenu = !showMobileMenu" class="block lg:hidden" href="#">
			<svg x-show="!showMobileMenu" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd" d="M3 6.75C3 6.33579 3.33579 6 3.75 6H20.25C20.6642 6 21 6.33579 21 6.75C21 7.16421 20.6642 7.5 20.25 7.5H3.75C3.33579 7.5 3 7.16421 3 6.75ZM3 12C3 11.5858 3.33579 11.25 3.75 11.25H20.25C20.6642 11.25 21 11.5858 21 12C21 12.4142 20.6642 12.75 20.25 12.75H3.75C3.33579 12.75 3 12.4142 3 12ZM3 17.25C3 16.8358 3.33579 16.5 3.75 16.5H20.25C20.6642 16.5 21 16.8358 21 17.25C21 17.6642 20.6642 18 20.25 18H3.75C3.33579 18 3 17.6642 3 17.25Z" fill="#11253E"/>
			</svg>
			<svg x-show="showMobileMenu" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd" d="M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L12 10.9393L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L13.0607 12L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L12 13.0607L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L10.9393 12L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z" fill="#101828"/>
			</svg>
		</a>
	</div>
	<div x-show="showSearch" class="fixed inset-0 z-20 bg-white/80">
		<a @click.prevent="showSearch = !showSearch" class="group absolute block top-10 right-10">
			<svg width="40px" height="40px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path class="fill-black group-hover:fill-red transition-all" fill-rule="evenodd" clip-rule="evenodd" d="M5.29289 5.29289C5.68342 4.90237 6.31658 4.90237 6.70711 5.29289L12 10.5858L17.2929 5.29289C17.6834 4.90237 18.3166 4.90237 18.7071 5.29289C19.0976 5.68342 19.0976 6.31658 18.7071 6.70711L13.4142 12L18.7071 17.2929C19.0976 17.6834 19.0976 18.3166 18.7071 18.7071C18.3166 19.0976 17.6834 19.0976 17.2929 18.7071L12 13.4142L6.70711 18.7071C6.31658 19.0976 5.68342 19.0976 5.29289 18.7071C4.90237 18.3166 4.90237 17.6834 5.29289 17.2929L10.5858 12L5.29289 6.70711C4.90237 6.31658 4.90237 5.68342 5.29289 5.29289Z" />
			</svg>
		</a>
		<div class="container h-full flex justify-center items-center">
			Search
		</div>
	</div>
</header>

