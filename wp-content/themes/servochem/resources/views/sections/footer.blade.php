@php
$product_ranges = get_posts([
    'post_type' => 'product-range',
    'posts_per_page' => -1,
    'fields' => 'ids', // Only get post IDs for better performance
    'orderby' => 'title',
    'order' => 'ASC',
    'no_found_rows' => true, // Improves performance when pagination is not needed
    'update_post_meta_cache' => false, // Disable meta cache as we don't need it
    'update_post_term_cache' => false, // Disable term cache as we don't need it
]);

$product_range_links = array_map(function($post_id) {
    return [
        'title' => get_the_title($post_id),
        'id' => $post_id
    ];
}, $product_ranges);
@endphp

<footer class="bg-white shadow-[0_4px_15px_0_rgba(0,_0,_0,_0.10)]">
	<div class="container flex max-lg:flex-col justify-center lg:justify-between gap-10 py-8">
		<div class="flex gap-4 justify-center flex-col shrink-0">
			<a class="block max-lg:mx-auto" href="{{ site_url() }}"><img src="{{Vite::asset('resources/images/logo-full.svg')}}" alt="Servochem Logo" /></a>
			<div class=""><img class="block max-lg:mx-auto" src="{{Vite::asset('resources/images/wics-badge.jpg')}}" alt="WICS Compliance badge" loading="lazy" /></div>
		</div>
		<div class="flex max-lg:flex-col max-lg:justify-center gap-7 lg:gap-20 text-sm lg:text-base">
			<div class="flex max-lg:flex-col max-lg:justify-center gap-6 lg:gap-10">
				<div class="flex flex-col gap-3 max-lg:text-center">
					<div class="font-medium">About Us</div>
					<a href="{{ site_url('/about') }}#our-mission" class="font-light hover:text-red transition-all max-lg:hidden">Our Mission</a>
					<a href="{{ site_url('/about') }}#community-outreach" class="font-light hover:text-red transition-all max-lg:hidden">Community Outreach</a>
				</div>
				<div class="flex flex-col gap-3 max-lg:text-center">
					<div class="font-medium">Product Range</div>
					@foreach($product_range_links as $link)
						<a href="{{ site_url('/product-range') }}#{{ $link['id'] }}" class="font-light hover:text-red transition-all max-lg:hidden">{{ $link['title'] }}</a>
					@endforeach
				</div>
				<div class="flex flex-col gap-3 max-lg:text-center">
					<div class="font-medium">Services</div>
					<a href="{{ site_url('/services') }}" class="font-light hover:text-red transition-all max-lg:hidden">Our Services</a>
					<a href="{{ site_url('/services') }}#laboratory" class="font-light hover:text-red transition-all max-lg:hidden">Laboratory</a>
				</div>
				<div class="flex flex-col gap-3 max-lg:text-center">
					<a href="{{ site_url('/blog') }}" class="font-medium hover:text-red transition-all">Blog</a>
				</div>
				<div class="flex flex-col gap-3 max-lg:text-center">
					<a href="{{ site_url('/contact') }}" class="font-medium hover:text-red transition-all">Contact</a>
				</div>
			</div>
			<div class="max-lg:flex max-lg:justify-center gap-6">
				<div class="font-medium mb-3 max-lg:hidden">Get Social</div>
				<a href="#" class="flex items-center gap-2 mb-2 group">
					<svg class="shrink-0" width="46" height="47" viewBox="0 0 46 47" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path class="stroke-dark-blue group-hover:stroke-red transition-all" d="M23.1084 1.21582C35.356 1.21582 45.2851 11.144 45.2852 23.3916C45.2852 35.6393 35.3561 45.5684 23.1084 45.5684C10.8608 45.5683 0.932617 35.6392 0.932617 23.3916C0.932716 11.144 10.8608 1.21592 23.1084 1.21582Z" fill="white" stroke-width="1.43074"/>
						<g clip-path="url(#clip0_352_3868)">
							<path d="M28.6178 21.1743H26.6148H25.8994V20.4589V18.2413V17.5259H26.6148H28.117C28.5105 17.5259 28.8324 17.2398 28.8324 16.8105V13.0191C28.8324 12.6256 28.5463 12.3037 28.117 12.3037H25.5059C22.6802 12.3037 20.713 14.3067 20.713 17.2755V20.3874V21.1027H19.9976H17.5654C17.0646 21.1027 16.5996 21.4962 16.5996 22.0685V24.6438C16.5996 25.1446 16.9931 25.6096 17.5654 25.6096H19.9261H20.6414V26.3249V33.5144C20.6414 34.0151 21.0349 34.4801 21.6072 34.4801H24.9694C25.184 34.4801 25.3629 34.3728 25.5059 34.2297C25.649 34.0867 25.7563 33.8363 25.7563 33.6217V26.3607V25.6453H26.5075H28.117C28.582 25.6453 28.9397 25.3592 29.0112 24.93V24.8942V24.8584L29.512 22.3904C29.5478 22.14 29.512 21.8539 29.2974 21.5677C29.2259 21.3889 28.9039 21.21 28.6178 21.1743Z" fill="#11253E"/>
						</g>
						<defs>
							<clipPath id="clip0_352_3868">
								<rect width="22.8918" height="22.8918" fill="white" transform="translate(11.6631 11.9458)"/>
							</clipPath>
						</defs>
					</svg>
					<span class="max-lg:hidden">facebook</span>
				</a>
				<a href="#" class="flex items-center gap-2 mb-2 group">
					<svg class="shrink-0" width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path class="stroke-dark-blue group-hover:stroke-red transition-all" d="M22.9453 0.999512C35.1029 0.999512 44.9588 10.8546 44.959 23.0122C44.959 35.1699 35.1031 45.0259 22.9453 45.0259C10.7878 45.0257 0.932617 35.1698 0.932617 23.0122C0.93283 10.8548 10.7879 0.999724 22.9453 0.999512Z" fill="white" stroke-width="1.43074"/>
						<path d="M17.2827 15.7474C17.2824 16.304 17.061 16.8378 16.6672 17.2312C16.2734 17.6246 15.7394 17.8455 15.1828 17.8452C14.6261 17.8449 14.0924 17.6235 13.699 17.2297C13.3055 16.8359 13.0847 16.3019 13.085 15.7453C13.0852 15.1886 13.3066 14.6549 13.7004 14.2615C14.0943 13.868 14.6282 13.6472 15.1849 13.6475C15.7415 13.6477 16.2753 13.8691 16.6687 14.2629C17.0621 14.6568 17.283 15.1907 17.2827 15.7474ZM17.3457 19.3994H13.1479V32.5384H17.3457V19.3994ZM23.9781 19.3994H19.8014V32.5384H23.9361V25.6436C23.9361 21.8026 28.942 21.4458 28.942 25.6436V32.5384H33.0872V24.2163C33.0872 17.7413 25.6782 17.9827 23.9361 21.1625L23.9781 19.3994Z" fill="#11253E"/>
					</svg>
					<span class="max-lg:hidden">linkedin</span>
				</a>
			</div>
		</div>
	</div>
	<div class="bg-dark-blue py-4">
		<div class="container flex justify-center items-center text-white font-light gap-4">
			<span>&copy; Copyright {{ date('Y') }} Servochem PTY Ltd</span>
			<span>|</span>
			<a>General Conditions of Use</a>
		</div>
	</div>
</footer>
