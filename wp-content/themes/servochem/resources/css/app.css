@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import "tailwindcss" theme(static);
@source "../views/";
@source "../../app/";

@theme {
	--font-poppins: 'Poppins', sans-serif;

	--color-red: #F01E29;
	--color-dark-blue: #11253E;
	--color-grey: #939598;
	--color-light-grey: #F1F2F2;
}

@utility container {
	padding-inline: 1rem;
	margin-inline: auto;

	@variant sm {
	}

	@variant md {
	}
}

:root {
  --gap: 24px;
  --scroll-start: 0;
  --scroll-end: calc(-100% - var(--gap));
}

.marquee__group {
  animation: scroll-x 60s linear infinite;
  height: 102px;
}

/* Element styles */
.marquee__group img {
  display: grid;
  place-items: center;
  height: 100%;
  padding: 32px;
  object-fit: contain;
}


@media (prefers-reduced-motion: reduce) {
  .marquee__group {
    animation-play-state: paused;
  }
}

@keyframes scroll-x {
  from {
    transform: translateX(var(--scroll-start));
  }
  to {
    transform: translateX(var(--scroll-end));
  }
}

@keyframes scroll-y {
  from {
    transform: translateY(var(--scroll-start));
  }
  to {
    transform: translateY(var(--scroll-end));
  }
}

header {
	top: 0;
}

.admin-bar header {
	top: 32px;
}

.home-hero {
	background: linear-gradient(180deg, rgba(255, 255, 255, 0.00) -57.17%, rgba(255, 255, 255, 0.80) 99.99%), url('../images/home-hero-bg.webp');
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}

.cta {
	background: linear-gradient(90deg, rgba(43, 75, 115, 0.90) 0%, rgba(17, 37, 62, 0.90) 100%), url('../images/cta-background-mobile.webp');
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}

@media (min-width: 1024px) {

	.cta {
		background: linear-gradient(90deg, rgba(43, 75, 115, 0.90) 0%, rgba(17, 37, 62, 0.90) 100%), url('../images/cta-background-desktop.webp');
	}
}

details {
	interpolate-size: allow-keywords;
	overflow: clip;
}

details summary {
	cursor: pointer;
	position: relative;
}

details:not([open]) summary:hover,
details:not([open]) summary:focus {

}

details[open] summary svg {
	rotate: 180deg;
}

details[open]::details-content {
  height: auto;
}

details::details-content {
  height: 0;
  overflow-y: clip;
  transition: content-visibility 475ms allow-discrete, height 475ms;
}

details main {
  padding: 1em 2.2em;
}

.facetwp-facet {
	@apply !mb-0 grow;
}

.facetwp-input-wrap {
	@apply w-full;
}

.facetwp-icon {
	@apply hidden;
}

.facetwp-facet input.facetwp-search {
	@apply !w-full !p-0 placeholder:text-[#939598] focus-visible:outline-none;
}

.facetwp-pager {
	@apply flex justify-center;
}

.facetwp-page {
	@apply !text-sm !font-medium !p-[10px] rounded-lg;
}

.facetwp-page.active {
	@apply !bg-red !text-white;
}

.prose p {
	@apply text-sm lg:text-base mb-8;
}

.prose h2 {
	@apply font-semibold mb-8;
}

.prose figure {
	@apply flex flex-col items-center justify-center mb-8;
}

.prose ul {
	@apply list-disc ps-5;	
}

.prose li {
	@apply mb-8;
}

.gform-theme--api, .gform-theme--foundation {
	--gf-form-gap-y: 24px;
	--gf-form-gap-x: 24px;
	--gf-ctrl-label-font-size-primary: 16px;
	--gf-ctrl-label-font-weight-primary: normal;
	--gf-ctrl-radius: 8px;
	

}

#gform_wrapper_1[data-form-index="0"].gform-theme input,
#gform_wrapper_1[data-form-index="0"].gform-theme select,
#gform_wrapper_1[data-form-index="0"].gform-theme textarea {
	--gf-ctrl-border-color: #CFD8DC;
	--gf-local-outline-color: #F01E29;
}

#gform_wrapper_1[data-form-index="0"].gform-theme input[type="submit"],
#gform_wrapper_1[data-form-index="0"].gform-theme input[type="button"] {
	--gf-local-bg-color: #F01E29;
	@apply py-2 px-5 rounded-lg text-white font-semibold hover:bg-dark-blue transition-all
}

.gfield_label {
	@apply text-sm lg:text-base
}

.gform-theme--foundation .gform_footer {
	@apply justify-center;
}